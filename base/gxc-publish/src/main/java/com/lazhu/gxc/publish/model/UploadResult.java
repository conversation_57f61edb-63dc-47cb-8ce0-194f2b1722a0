package com.lazhu.gxc.publish.model;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 上传结果模型
 * 封装视频上传操作的结果信息
 */
@Data
public class UploadResult {

    private final boolean success;
    private final String message;
    private final String videoId;
    private final LocalDateTime uploadTime;
    private final Exception error;
    private final PlatformType platform;
    private String videoUrl;

    /**
     * 私有构造函数
     */
    private UploadResult(boolean success, String message, String videoId,
                         LocalDateTime uploadTime, Exception error, PlatformType platform) {
        this.success = success;
        this.message = message;
        this.videoId = videoId;
        this.uploadTime = uploadTime;
        this.error = error;
        this.platform = platform;
    }

    /**
     * 创建成功的上传结果
     *
     * @param message  成功消息
     * @param videoId  视频ID
     * @param platform 平台类型
     * @return 成功的上传结果
     */
    public static UploadResult success(String message, String videoId, PlatformType platform) {
        return new UploadResult(true, message, videoId, LocalDateTime.now(), null, platform);
    }

    /**
     * 创建成功的上传结果（无视频ID）
     *
     * @param message  成功消息
     * @param platform 平台类型
     * @return 成功的上传结果
     */
    public static UploadResult success(String message, PlatformType platform) {
        return success(message, null, platform);
    }

    /**
     * 创建失败的上传结果
     *
     * @param message  失败消息
     * @param error    异常信息
     * @param platform 平台类型
     * @return 失败的上传结果
     */
    public static UploadResult failure(String message, Exception error, PlatformType platform) {
        return new UploadResult(false, message, null, LocalDateTime.now(), error, platform);
    }

    /**
     * 创建失败的上传结果（无异常）
     *
     * @param message  失败消息
     * @param platform 平台类型
     * @return 失败的上传结果
     */
    public static UploadResult failure(String message, PlatformType platform) {
        return failure(message, null, platform);
    }

    /**
     * 检查是否有视频ID
     *
     * @return 如果有视频ID返回true
     */
    public boolean hasVideoId() {
        return videoId != null && !videoId.trim().isEmpty();
    }

    /**
     * 检查是否有错误信息
     *
     * @return 如果有错误信息返回true
     */
    public boolean hasError() {
        return error != null;
    }

    /**
     * 获取错误详细信息
     *
     * @return 错误详细信息，如果没有错误返回null
     */
    public String getErrorDetails() {
        if (error == null) {
            return null;
        }

        StringBuilder details = new StringBuilder();
        details.append("Exception: ").append(error.getClass().getSimpleName());
        if (error.getMessage() != null) {
            details.append(" - ").append(error.getMessage());
        }

        return details.toString();
    }
}