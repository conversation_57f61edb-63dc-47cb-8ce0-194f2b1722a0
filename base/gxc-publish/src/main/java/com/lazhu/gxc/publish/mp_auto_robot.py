#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2025/5/22 14:06
# <AUTHOR> lilin
# @File    : mp_auto_robot.py
# @Software: PyCharm
import time
import re
import datetime
import json
import functools
import hashlib
from app.tools.qy_bot_msg_api import send_image_msg, send_txt_msg
from app.utils.log import logger
import base64
from DrissionPage import ChromiumPage, ChromiumOptions
from DrissionPage.common import Keys, Actions

class MpAutoRobot(object):
    def __init__(self, options: ChromiumOptions, qy_key:str, new: bool = False,name:str='默认',
                 **kwargs):
        self.name=name
        self.handled_gzhs = []
        self.qy_key = qy_key
        self.page = ChromiumPage(options)
        self.page.set.window.size(1920, 1080)
        # self.page.get('https://www.baidu.com')
        self.page.get_screenshot('test.png')
        self.page.set.headers({
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.13 Safari/537.36"
        })
        self.token=''
        self.msg_content=''
        if new:
            self.page.clear_cache()
        self.mp_login()

    def __del__(self):
        try:
            self.page.quit(timeout=2)
            logger.debug('浏览器退出成功')
        except Exception as e:
            logger.error(f'浏览器退出报错: {e}')

    @staticmethod
    def relogin(func):
        '''处理登陆超时时可以自己点击重新登陆'''
        @functools.wraps(func)
        def wrapper(*args, **kw):
            try:
                return func(*args, **kw)
            except Exception as e:
                try:
                    logger.debug(f'wrapper捕获error：{e}')
                    try:
                        page = args[0].page
                        try:
                            ele=page.ele('text:验证')
                            assert ele
                            args[0].page.clear_cache()
                        except:
                            pass
                        page.ele('#jumpUrl', timeout=2).click()
                    except:
                        pass
                    args[0].mp_login()
                    return func(*args, **kw)
                except Exception as e:
                    logger.error(f'wrapper刷新并重新执行失败：{e}')
                    send_txt_msg(args[0].qy_key, f'自动发布机器人执行失败，切换账号发布失败。\n报错：{e}', mentioned_list=[])
        return wrapper

    @relogin
    def mp_login(self):
        url = 'https://mp.weixin.qq.com/'
        page = self.page
        page.get(url)
        if 'token=' in page.url:
            self.token = re.findall(r'token=(\d+)', page.url)[0]
            logger.debug(f'已登陆过,{self.token}')
            return
        time.sleep(1)
        qr_code = page.ele('.login__type__container__scan__qrcode',timeout=20)
        qr_b64 = qr_code.get_screenshot(as_base64=True)
        image_data = base64.b64decode(qr_b64)
        tick=datetime.datetime.now()
        # 计算MD5值
        md5_hash = hashlib.md5(image_data).hexdigest()
        send_image_msg(key=self.qy_key, base64_str=qr_b64,
                       md5_str=md5_hash)
        send_txt_msg(key=self.qy_key, content=f'微信自动发布机器人{self.name}启动，微信公众号二维码已发送，请尽快扫描登陆！',
                     mentioned_list=[])
        while True:
            try:
                # 等待登陆跳转
                page.wait.url_change(timeout=30, text='token=', raise_err=True)
                self.token = re.findall(r'token=(\d+)', page.url)[0]
                logger.debug(f'登录成功,{self.token}')
                break
            except Exception as e:
                try:
                    # 代表码被扫了，需要刷新
                    t = page.ele('.login__type__container__scan__info__desc', timeout=1).text
                    logger.debug(t)
                    if '微信扫一扫，选择该微信下' not in t:
                        logger.debug('码被扫了，但未登陆成功，延迟5s后,刷新页面')
                        time.sleep(5)
                        page.get(url)
                        # 如果恰好在此时登陆了，则获取cookie
                        if 'token=' in page.url:
                            self.token = re.findall(r'token=(\d+)', page.url)[0]
                            logger.debug(f'码被扫了，且登陆成功,{self.token}')
                            return
                        qr_code = page.ele('.login__type__container__scan__qrcode')
                        qr_code.get_screenshot(path='./img/qrcode.png')
                    else:
                        now=datetime.datetime.now()
                        if now-tick>=datetime.timedelta(minutes=10):
                            logger.debug('超时，重新发送二维码')
                            page.refresh()
                            if 'token=' in page.url:
                                self.token = re.findall(r'token=(\d+)', page.url)[0]
                                logger.debug(f'已登陆,{self.token}')
                                return
                            qr_code = page.ele('.login__type__container__scan__qrcode', timeout=20)
                            qr_b64 = qr_code.get_screenshot(as_base64=True)
                            image_data = base64.b64decode(qr_b64)
                            tick = datetime.datetime.now()
                            # 计算MD5值
                            md5_hash = hashlib.md5(image_data).hexdigest()
                            send_image_msg(key=self.qy_key, base64_str=qr_b64,
                                           md5_str=md5_hash)
                            send_txt_msg(key=self.qy_key,
                                         content=f'微信自动发布机器人{self.name}启动，二维码超时，新微信公众号二维码已发送，请尽快扫描登陆！',
                                         mentioned_list=[])
                            continue
                        logger.debug('码未被扫或超时，继续等待扫码登录')
                        time.sleep(10)
                except:
                    logger.debug('码未被扫或超时，继续等待扫码登录')
                    time.sleep(1)
    def delete_draft(self):
        assert self.token is not None
        draft_page_url = f'https://mp.weixin.qq.com/cgi-bin/appmsg?begin=0&count=200&type=77&action=list_card&token={self.token}&lang=zh_CN'  # 默认不超过200条草稿待发
        page = self.page
        page.get(draft_page_url)
        gzh_name = page.ele('.acount_box-nickname').text
        logger.debug(f'公众号名称:{gzh_name}')
        logger.debug('等待加载完成')
        page.wait.doc_loaded(timeout=10)
        page.ele('#js_listview').wait.clickable(timeout=5, raise_err=True).click()
        tbody=page.ele('.weui-desktop-table__bd')
        trs=tbody.eles('tag:tr')
        print(len(trs))
        for tr in trs:
            tr.ele('.:weui-desktop-link-group').set.attr('style','display:block')
            btn1=tr.ele('text:删除').prev()
            btn1.wait.displayed().click()
            btn2=tr.ele('text:确定').click()


    def public_draft(self,mode:list=['原创','留言']):
        assert self.token is not None
        draft_page_url = f'https://mp.weixin.qq.com/cgi-bin/appmsg?begin=0&count=200&type=77&action=list_card&token={self.token}&lang=zh_CN'  # 默认不超过200条草稿待发
        page = self.page
        page.get(draft_page_url)
        gzh_name = page.ele('.acount_box-nickname').text
        logger.debug(f'公众号名称:{gzh_name}')
        logger.debug('等待加载完成')
        page.wait.doc_loaded(timeout=10)
        html=page.html

        req = r'window.wx.cgiData = (\{\s*?"item":.+?\});'
        res = re.findall(req, html, re.S)[0]
        res = json.loads(res)['item']
        links=[]
        for item in res:
            appid = item['app_id']
            url = f'https://mp.weixin.qq.com/cgi-bin/appmsg?t=media/appmsg_edit&action=edit&type=10&appmsgid={appid}&fromview=list'
            links.append(url)

        logger.debug(f'共{len(links)}篇草稿待发')
        n = 0
        n1 = 0
        ac = Actions(page)
        for link in links[:]:

            # 点击发表后的链接
            # page.get(link + f'&isSend=1&token={self.token}&reprint_confirm=0&timestamp={int(time.time() * 1000)}&lang=zh_CN')、
            # 点击编辑后的链接
            page.get(link + f'&token={self.token}&timestamp={int(time.time() * 1000)}&lang=zh_CN')
            try:
                if '原创' in mode:
                    logger.debug('设为原创')
                    previews_container=page.ele('#js_appmsg_preview')
                    previews_container.wait.has_rect(timeout=10)
                    previews=previews_container.eles('#appmsgItem')
                    for preview in previews:
                        preview.click()
                        # 确保滚动到最底部
                        page.ele('#auto_save_container').click()
                        page.scroll.to_bottom()
                        try:
                            origin=page.ele('#js_original',timeout=3)

                            origin.wait.clickable()
                            origin.click()
                            name_input = page.ele('.:frm_input js_counter', timeout=10).wait.has_rect()
                            # 这样才能正确输入字
                            name_input.focus()
                            name_input.clear()
                            # 原创作者
                            # ac.input(gzh_name[:8])
                            ac.input('Grace')
                            ac.input(Keys.ENTER)
                        except:
                            logger.error('无原创选项，设置原创失败')
                            break
                        agree = page.ele('.original_agreement', timeout=1)
                        time.sleep(0.5)
                        while True:
                            logger.debug('点击确定，设置原创')
                            agree.next().click()
                            try:
                                # 原创框找不到了，则说明点击成功
                                page.ele('.original_agreement',timeout=1).wait.deleted()
                                logger.debug('确定失败，稍后重试')
                                time.sleep(3)
                                logger.debug('勾选原创协议')
                                agree.ele('.:weui-desktop-icon-checkbox').wait.clickable(timeout=5).click()
                            except:
                                break
                        if '留言' in mode:
                            logger.debug('开启留言')
                            try:
                                page.ele('#js_comment_and_fansmsg_area').click()
                                btn = page.ele('xpath://*[@class="weui-desktop-btn_wrp"]/button[text()="确定"]',
                                               timeout=10).click()
                            except:
                                logger.debug(f'开启留言失败')
            except Exception as e:
                logger.error(f'设置原创失败，{e}')
                pass
            logger.debug('点击发表')
            pub_btn = page.ele('.mass_send')
            pub_btn.wait.clickable()
            pub_btn.click()


            for i in range(3):
                try:
                    btn = page.ele('xpath://*[@class="weui-desktop-btn_wrp"]/button[text()="发表"]', timeout=10).click()
                    break
                except:
                    try:
                        page.ele('xpath://*[@class="weui-desktop-btn_wrp"]/button[text()="稍后再说"]',timeout=1).click()
                        logger.debug('关闭带货弹窗')
                    except:
                        logger.debug('关闭带货弹窗失败，未检测到稍后再说按钮')
                        pass
                    logger.debug('等待发表窗口')
                    time.sleep(3)
            else:
                logger.error('无法点击发表按钮,选择下一篇发送')
                continue

            try:
                btn = page.ele('xpath://*[@class="weui-desktop-btn_wrp"]/button[text()="继续发表"]', timeout=5)
                btn.hover().click()
                time.sleep(1)
                n += 1
            except:
                pass
            # if mode == '原创':
            if 1 == 1:
                logger.debug('原创检测处理')
                try:
                    logger.debug('点击下一步，如果非原创')
                    btn = page.ele('xpath://*[@class="weui-desktop-btn_wrp"]/button[text()="下一步"]', timeout=5)
                    btn.click()
                    time.sleep(1)
                    btns = page.eles('xpath://*[@class="weui-desktop-btn_wrp"]/button[text()="继续群发"]', timeout=5)
                    # 目前第0个是分享操作的
                    try:
                        btn = btns[1]
                        btn.click()
                    except:
                        btns = page.eles('xpath://*[@class="weui-desktop-btn_wrp"]/button[text()="继续发表"]', timeout=5)
                        btn = btns[1]
                        btn.click()
                        for btn in btns:
                            try:
                                btn.click()
                                break
                            except:
                                logger.debug('点击继续发表失败,正在重试')
                    time.sleep(1)
                except Exception as e:
                    logger.debug(f'继续发表点击失败，或许已经直接发表成功{e}')
                    pass
            # 等待发布完后的跳转
            try:
                page.wait.url_change(text='home', timeout=20, raise_err=True)
                n1 += 1
            except:
                logger.debug('有可能是发布太频繁了，休息一分钟')

        t = f'{gzh_name}: {len(links)}篇草稿,成功{n}篇,跳转{n1}篇'
        logger.debug(t)
        self.msg_content+=t+'\n\n'
    @relogin
    def change_account(self,assigns:list=[]):
        page = self.page
        while True:
            try:
                gzh_name = page.ele('.acount_box-nickname').text
                break
            except Exception as e:
                logger.error(f'获取公众号名称失败,尝试刷新页面重试:{e}')
                page.get('https://mp.weixin.qq.com/')
                time.sleep(5)
                continue
        try:
            page.ele('.account_box-body').click()
            panel = page.ele('.account_box-panel')
            gzh_name = panel.ele('.account_box-panel-head__nickname').text.strip()
            a = panel.ele('tag:a@text():切换账号')
            # 处理切换窗口已经打开的情况
            try:
                a.click()
            except:
                pass
            dialog = page.ele('.switch-account-dialog')
            section = dialog.ele('.switch-account-dialog_section')
            sec_name = section.ele('.switch-account-dialog_section-title').text
            if '公众号' in sec_name:
                bds = section.eles('.section-item__bd')
                for bd in bds:
                    name = bd.ele('.section-item__nickname').text
                    if name not in self.handled_gzhs:
                        if len(assigns) == 0:
                            if name == gzh_name:
                                # logger.debug('当前公众号无需切换')
                                self.token = re.findall(r'token=(\d+)', page.url)[0]
                                print(f'token:{self.token}')
                            else:
                                bd.click()
                                page.ele(f'@@class:acount_box-nickname@text():{name}')
                                self.token = re.findall(r'token=(\d+)', page.url)[0]
                                print(f'token:{self.token}')
                            return name
                        else:
                            if name in assigns:
                                if name == gzh_name:
                                    # logger.debug('当前公众号无需切换')
                                    self.token = re.findall(r'token=(\d+)', page.url)[0]
                                    print(f'token:{self.token}')
                                    pass
                                else:
                                    bd.click()
                                    page.ele(f'@@class:acount_box-nickname@text():{name}')
                                    self.token = re.findall(r'token=(\d+)', page.url)[0]
                                    print(f'token:{self.token}')
                                return name
                            else:
                                # logger.debug(f'{name}不是指定公众号，下一个')
                                continue

                else:
                    logger.debug('没有可切换的公众号，全部处理完毕')
                    return None
        except:
            logger.error('切换账号失败')
            raise Exception('切换账号失败')
    def auto_delete(self,assigns=[]):
        self.assigns = assigns
        # 自动发布
        try:
            while True:
                name = self.change_account(assigns)
                if name is not None:
                    logger.debug(f'公众号:{name}')
                    self.delete_draft()
                    self.handled_gzhs.append(name)
                else:
                    break
        except Exception as e:
            logger.error(f'切换账号失败:{e}')
            raise Exception(f'切换账号失败')
        self.page.close()
        self.__del__()

    # 自动发布
    def auto_publish(self,assigns=[]):
        self.assigns=assigns
        try:
            while True:
                name=self.change_account(assigns)
                if name is not None:
                    logger.debug(f'公众号:{name}')
                    self.public_draft(mode=['原创','留言'])
                    self.handled_gzhs.append(name)
                else:
                    break
        except Exception as e:
            logger.error(f'切换账号失败:{e}')
            raise Exception(f'切换账号失败')
        send_txt_msg(self.qy_key, f'微信自动发布机器人执行完毕\n{self.msg_content}\n指定：{assigns}', mentioned_list=[])
        self.page.close()
        self.__del__()



