package com.lazhu.gxc.publish.platform.wechat;

import cn.hutool.core.util.URLUtil;
import com.lazhu.gxc.publish.browser.BrowserManager;
import com.lazhu.gxc.publish.cookie.CookieStorage;
import com.lazhu.gxc.publish.model.AuthenticationInfo;
import com.lazhu.gxc.publish.model.LoginResult;
import com.lazhu.gxc.publish.model.LoginSession;
import com.lazhu.gxc.publish.model.PlatformType;
import com.lazhu.gxc.publish.service.AuthService;
import com.microsoft.playwright.BrowserContext;
import com.microsoft.playwright.FrameLocator;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.options.Cookie;
import com.microsoft.playwright.options.LoadState;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 微信视频号认证服务实现
 * 使用BrowserManager统一管理浏览器实例和CookieStorage抽象存储层，支持多种存储方式
 * 参考Python版本的cookie_auth和get_tencent_cookie功能
 */
@Slf4j
public class WeChatVideoAuthService implements AuthService {

    private static final String WECHAT_LOGIN_URL = "https://channels.weixin.qq.com/login.html";
    private static final String WECHAT_UPLOAD_URL = "https://channels.weixin.qq.com/platform/post/create";

    // 会话超时时间：3分钟
    private static final long SESSION_TIMEOUT_MS = 3 * 60 * 1000L;

    private final CookieStorage cookieStorage;
    private final BrowserManager browserManager;
    private final String platformIdentifier;
    // 登录会话管理
    private final Map<String, LoginSession> loginSessions = new ConcurrentHashMap<>();
    // 定时任务调度器
    private final ScheduledExecutorService sessionCleanupScheduler = Executors.newSingleThreadScheduledExecutor(
            r -> new Thread(r, "session-cleanup-thread"));

    public WeChatVideoAuthService(CookieStorage cookieStorage, BrowserManager browserManager) {
        this.cookieStorage = cookieStorage;
        this.browserManager = browserManager;
        this.platformIdentifier = getSupportedPlatform().name().toLowerCase();

        log.info("微信视频号认证服务初始化 - 存储类型: {}", cookieStorage.getStorageType());

        // 启动会话清理任务，每5分钟执行一次
        startSessionCleanupTask();
    }


    /**
     * 开始登录流程：获取二维码并保持浏览器打开等待扫码
     *
     * @param userId 用户ID
     * @return 登录会话信息，包含二维码URL和会话ID
     */
    public LoginSession startLogin(String userId) {
        log.info("开始登录流程，获取二维码 - 用户: {}", userId);

        String sessionId = "login-session-" + UUID.randomUUID();
        String browserId = "login-browser-" + UUID.randomUUID();
        String contextId = "login-context-" + UUID.randomUUID();

        try {
            // 1. 创建浏览器实例，非无头模式
            browserManager.createBrowser(browserId);
            BrowserContext context = browserManager.createContext(browserId, contextId, null);
            Page page = context.newPage();

            // 2. 导航到登录页面
            page.navigate(WECHAT_LOGIN_URL);
            log.info("已打开微信视频号登录页面");

            // 3. 获取二维码URL
            String qrCodeUrl;
            try {
                // 等待iframe出现
                FrameLocator iframe = page.frameLocator("iframe").first();
                Locator imgLocator = iframe.locator("img").first();
                qrCodeUrl = imgLocator.getAttribute("src");
                log.info("获取到登录二维码URL");
            } catch (Exception e) {
                log.warn("获取二维码URL失败: {}", e.getMessage());
                // 清理资源
                cleanResources(browserId, contextId);
                return new LoginSession(this.getSupportedPlatform(), sessionId, userId, null, "获取二维码失败: " + e.getMessage());
            }
            // 5. 创建登录会话并保存
            LoginSession session = new LoginSession(this.getSupportedPlatform(), sessionId, userId, browserId, contextId, qrCodeUrl);
            loginSessions.put(sessionId, session);

            log.info("登录会话创建成功 - 会话ID: {}, 用户: {}", sessionId, userId);
            return session;

        } catch (Exception e) {
            log.error("启动登录流程异常 - 用户: {}", userId, e);
            cleanResources(browserId, contextId);
            return new LoginSession(this.getSupportedPlatform(), sessionId, userId, null, "登录流程启动异常: " + e.getMessage());
        }
    }

    /**
     * 检查登录状态：查看用户是否已扫码登录
     *
     * @param sessionId 登录会话ID
     * @return 登录状态结果
     */
    public LoginResult checkLoginStatus(String sessionId) {
        LoginSession session = loginSessions.get(sessionId);
        if (session == null) {
            return LoginResult.failed("登录会话不存在或已过期");
        }
        try {
            // 获取浏览器上下文来检查页面状态
            BrowserContext context = browserManager.getContext(session.getContextId());
            if (context == null) {
                throw new IllegalStateException("浏览器上下文不存在 - 会话: " + sessionId);
            }
            // 获取当前页面
            List<Page> pages = context.pages();
            if (pages.isEmpty()) {
                throw new IllegalStateException("浏览器页面已关闭 - 会话: " + sessionId);
            }
            Page page = pages.getFirst(); // 获取第一个页面
            String currentUrl = page.url();
            log.debug("检查登录状态 - 会话: {}, 当前URL: {}", sessionId, currentUrl);

            if (currentUrl.contains("platform") || URLUtil.getPath(currentUrl).equals("/")) {
                //跳转到首页
                page.waitForLoadState(LoadState.DOMCONTENTLOADED);
                // 等待用户信息加载完成
                page.waitForSelector(".finder-info-container", new Page.WaitForSelectorOptions().setTimeout(120000));
                // 获取用户信息
                String avatar = page.locator(".finder-info-container .avatar").first().getAttribute("src");
                String nickname = page.locator(".finder-info-container .finder-nickname").first().innerText();
                String account = page.locator(".finder-info-container .finder-uniq-id").first().innerText();
                LoginResult.UserInfo userInfo = new LoginResult.UserInfo(account, nickname, avatar);
                log.info("登录成功 - 会话: {}, 用户信息: {}", sessionId, userInfo);

                // 获取Cookie并保存
                Map<String, String> cookies = getCookiesFromContext(context);
                // 转换为认证信息格式
                AuthenticationInfo authInfo = new AuthenticationInfo(getSupportedPlatform(), convertCookiesToAuthInfoFormat(cookies), null, null, null);
                // 保存认证信息
                boolean isSaved = saveAuthInfo(authInfo, session.getUserId());
                if (!isSaved) {
                    throw new IllegalStateException("保存Cookie失败 - 用户: " + session.getUserId());
                }
                log.info("登录会话已保存Cookie - 用户: {}, 平台: {}, Cookie数量: {}", session.getUserId(), platformIdentifier, cookies.size());
                // 登录成功清理资源
                loginSessions.remove(sessionId);
                cleanResources(session.getBrowserId(), session.getContextId());
                return LoginResult.success(userInfo);
            }

            if (currentUrl.contains("login")) {
                try {
                    // 还在登录页面，检查是否已扫码
                    FrameLocator iframe = page.frameLocator("iframe").first();
                    // 1. 检查是否过期 - 查找包含过期信息的显示的mask
                    boolean isExpired = iframe.locator(".mask.show p.refresh-tip:has-text('二维码已过期')").count() > 0;

                    if (isExpired) {
                        cleanResources(session.getBrowserId(), session.getContextId());
                        loginSessions.remove(sessionId);
                        return LoginResult.expired("二维码已过期，请刷新二维码");
                    }

                    // 2. 检查是否已扫码但未确认 - 查找包含成功图标和已扫码提示的显示的mask
                    boolean isScanned = iframe.locator(".mask.show .qr-tip:has-text('已扫码')").count() > 0;

                    return isScanned ? LoginResult.scanned("已扫码，请在手机上确认登录") : LoginResult.waiting("请扫码登录");
                } catch (Exception ex) {
                    return LoginResult.waiting("登录中");
                }
            } else {
                // 未知页面，可能是登录成功后的跳转页面，也可能是其他页面 TODO 可能做其他处理
                throw new IllegalStateException("未知页面：" + currentUrl);
            }
        } catch (Exception e) {
            log.error("检查登录状态异常 - 会话: {}", sessionId, e);
            cleanResources(session.getBrowserId(), session.getContextId());
            loginSessions.remove(sessionId);
            return LoginResult.failed("检查登录状态异常：" + e.getMessage());
        }
    }

    /**
     * 验证Cookie是否有效
     */
    @Override
    public boolean validateCookies(String userId) {
        log.info("开始验证微信视频号Cookie - 用户: {}", userId);

        // 从存储中加载Cookie
        Map<String, String> cookies = cookieStorage.loadCookies(platformIdentifier, userId);
        if (cookies == null || cookies.isEmpty()) {
            log.warn("Cookie不存在 - 用户: {}", userId);
            return false;
        }

        String browserId = "auth-validation-" + UUID.randomUUID();
        String contextId = "auth-context-" + UUID.randomUUID();

        try {
            // 通过BrowserManager创建浏览器实例
            browserManager.createBrowser(browserId);
            BrowserContext context = browserManager.createContext(browserId, contextId, null);
            // 设置Cookie
            setCookiesToContext(context, cookies);
            Page page = context.newPage();
            // 访问上传页面
            page.navigate(WECHAT_UPLOAD_URL);
            page.waitForLoadState(LoadState.DOMCONTENTLOADED);
            try {
                // 等待跳转到登录页面（如果会跳转的话）
                page.waitForURL(url -> url.contains("login"), new Page.WaitForURLOptions().setTimeout(3000));
                log.warn("Cookie已失效 - 已跳转到登录页，用户: {}", userId);
                return false;
            } catch (Exception e) {
                // 没有跳转到登录页，继续检查当前URL
                String currentUrl = page.url();
                if (currentUrl.contains("login")) {
                    log.warn("Cookie已失效 - url:{},已跳转到登录页，用户: {}", currentUrl, userId);
                    return false;
                }
                log.info("Cookie有效：用户：{}，当前URL：{}", userId, currentUrl);
                return true;
            }
        } catch (Exception e) {
            log.error("验证Cookie时发生异常 - 用户: {}", userId, e);
            return false;
        } finally {
            // 确保清理资源
            cleanResources(browserId, contextId);
        }
    }

    /**
     * 启动会话清理定时任务
     */
    private void startSessionCleanupTask() {
        sessionCleanupScheduler.scheduleWithFixedDelay(
                this::cleanExpiredSessions,
                5, 5,
                TimeUnit.MINUTES
        );
        log.info("会话清理定时任务已启动，每5分钟执行一次");
    }

    /**
     * 清理过期的登录会话
     */
    private void cleanExpiredSessions() {
        if (loginSessions.isEmpty()) {
            return;
        }

        log.debug("开始清理过期登录会话，当前会话数: {}", loginSessions.size());

        int cleanedCount = 0;
        for (Map.Entry<String, LoginSession> entry : loginSessions.entrySet()) {
            String sessionId = entry.getKey();
            LoginSession session = entry.getValue();

            if (session.isExpired(SESSION_TIMEOUT_MS)) {
                log.info("清理过期会话 - 会话ID: {}, 用户: {}, 创建时间: {}",
                        sessionId, session.getUserId(),
                        java.time.Instant.ofEpochMilli(session.getCreateTime()));

                // 清理浏览器资源
                cleanResources(session.getBrowserId(), session.getContextId());

                // 从会话管理中移除
                loginSessions.remove(sessionId);
                cleanedCount++;
            }
        }

        if (cleanedCount > 0) {
            log.info("会话清理完成，清理了 {} 个过期会话，剩余会话数: {}", cleanedCount, loginSessions.size());
        }
    }


    /**
     * 服务销毁时清理资源
     */
    @PreDestroy
    public void destroy() {
        log.info("开始销毁微信视频号认证服务...");

        // 停止定时任务
        sessionCleanupScheduler.shutdown();
        try {
            if (!sessionCleanupScheduler.awaitTermination(10, TimeUnit.SECONDS)) {
                sessionCleanupScheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            sessionCleanupScheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }

        // 清理所有会话
        int sessionCount = loginSessions.size();
        for (LoginSession session : loginSessions.values()) {
            cleanResources(session.getBrowserId(), session.getContextId());
        }
        loginSessions.clear();

        log.info("微信视频号认证服务销毁完成，清理了 {} 个会话", sessionCount);
    }

    /**
     * 清理登录会话资源
     */
    private void cleanResources(String browserId, String contextId) {
        try {
            if (contextId != null) {
                browserManager.closeContext(contextId);
            }
            if (browserId != null) {
                browserManager.closeBrowser(browserId);
            }
            log.debug("登录会话资源已清理");
        } catch (Exception e) {
            log.warn("清理登录会话资源时发生异常", e);
        }
    }

    /**
     * 加载认证信息
     */
    @Override
    public AuthenticationInfo loadAuthInfo(String userId) {
        log.debug("加载认证信息 - 用户: {}", userId);

        Map<String, String> cookies = cookieStorage.loadCookies(platformIdentifier, userId);
        if (cookies == null || cookies.isEmpty()) {
            log.warn("认证信息不存在 - 用户: {}", userId);
            return null;
        }

        try {
            // 转换Cookie格式并创建AuthenticationInfo实例
            List<Map<String, String>> cookieList = convertCookiesToAuthInfoFormat(cookies);

            AuthenticationInfo authInfo = new AuthenticationInfo(getSupportedPlatform(), cookieList, null,  // origins
                    null,  // localStorage
                    null   // sessionStorage
            );

            log.debug("认证信息加载成功 - 用户: {}", userId);
            return authInfo;
        } catch (Exception e) {
            log.error("加载认证信息失败 - 用户: {}", userId, e);
            return null;
        }
    }

    /**
     * 保存认证信息
     */
    @Override
    public boolean saveAuthInfo(AuthenticationInfo authInfo, String userId) {
        log.debug("保存认证信息 - 用户: {}", userId);

        try {
            if (authInfo == null || authInfo.getCookies() == null) {
                log.warn("认证信息或Cookie为空 - 用户: {}", userId);
                return false;
            }

            // 转换Cookie格式并保存
            Map<String, String> cookies = convertAuthInfoCookiesToMapFormat(authInfo.getCookies());
            cookieStorage.saveCookies(platformIdentifier, userId, cookies);

            log.debug("认证信息保存成功 - 用户: {}", userId);
            return true;
        } catch (Exception e) {
            log.error("保存认证信息失败 - 用户: {}", userId, e);
            return false;
        }
    }

    /**
     * 获取支持的平台类型
     */
    @Override
    public PlatformType getSupportedPlatform() {
        return PlatformType.WECHAT_VIDEO;
    }


    /**
     * 清除认证信息
     */
    @Override
    public boolean clearAuthInfo(String userId) {
        try {
            boolean deleted = cookieStorage.deleteCookies(platformIdentifier, userId);
            if (deleted) {
                log.info("认证信息已清除 - 用户: {}", userId);
            }
            return deleted;
        } catch (Exception e) {
            log.error("清除认证信息失败 - 用户: {}", userId, e);
            return false;
        }
    }


    /**
     * 将Cookie设置到浏览器上下文
     */
    private void setCookiesToContext(BrowserContext context, Map<String, String> cookies) {
        List<Cookie> browserCookies = cookies.entrySet().stream().map(entry -> new Cookie(entry.getKey(), entry.getValue()).setDomain(".weixin.qq.com").setPath("/")).toList();
        context.addCookies(browserCookies);
    }

    /**
     * 从浏览器上下文获取Cookie
     */
    private Map<String, String> getCookiesFromContext(BrowserContext context) {
        List<Cookie> browserCookies = context.cookies();
        Map<String, String> cookies = new HashMap<>();
        browserCookies.forEach(cookie -> cookies.put(cookie.name, cookie.value));
        return cookies;
    }

    /**
     * 将Map<String, String>格式的cookies转换为AuthenticationInfo需要的List<Map<String, Object>>格式
     */
    private List<Map<String, String>> convertCookiesToAuthInfoFormat(Map<String, String> cookies) {
        return cookies.entrySet().stream().map(entry -> {
            Map<String, String> cookie = new HashMap<>();
            cookie.put("name", entry.getKey());
            cookie.put("value", entry.getValue());
            cookie.put("domain", ".weixin.qq.com");
            cookie.put("path", "/");
            return cookie;
        }).toList();
    }

    /**
     * 将AuthenticationInfo的List<Map<String, Object>>格式的cookies转换为Map<String, String>格式
     */
    private Map<String, String> convertAuthInfoCookiesToMapFormat(List<Map<String, String>> cookies) {
        return cookies.stream().filter(cookie -> cookie.get("name") != null && cookie.get("value") != null).collect(HashMap::new, (map, cookie) -> map.put(cookie.get("name").toString(), cookie.get("value").toString()), HashMap::putAll);
    }
}