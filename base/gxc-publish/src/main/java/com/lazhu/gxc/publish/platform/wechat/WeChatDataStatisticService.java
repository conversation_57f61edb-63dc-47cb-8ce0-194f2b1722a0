package com.lazhu.gxc.publish.platform.wechat;

import com.lazhu.gxc.publish.browser.BrowserManager;
import com.lazhu.gxc.publish.browser.PageInteractionService;
import com.lazhu.gxc.publish.config.PublisherConfig;
import com.lazhu.gxc.publish.cookie.CookieStorage;
import com.lazhu.gxc.publish.exception.DataStatisticException;
import com.lazhu.gxc.publish.model.*;
import com.lazhu.gxc.publish.platform.AbstractDataStatisticService;
import com.lazhu.gxc.publish.service.AuthService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 微信视频号数据统计服务
 * 实现微信视频号关注者数据的抓取和统计
 */
public class WeChatDataStatisticService extends AbstractDataStatisticService {

    private static final Logger logger = LoggerFactory.getLogger(WeChatDataStatisticService.class);

    private static final String FOLLOWER_STATISTIC_URL = "https://channels.weixin.qq.com/platform/statistic/follower";

    private final WeChatStatisticPageInteraction statisticPageInteraction;

    public WeChatDataStatisticService(BrowserManager browserManager,
                                      PageInteractionService pageInteractionService,
                                      AuthService authService) {
        super(browserManager, authService);
        this.statisticPageInteraction = new WeChatStatisticPageInteraction(pageInteractionService);
    }

    @Override
    public StatisticResult getFollowerStatistics(StatisticRequest request) {
        logger.info("开始获取微信视频号关注者统计数据");

        try {
            // 验证请求参数
            validateRequest(request);

            // 初始化浏览器环境
            initializeBrowser(request.getUserId());

            // 导航到关注者统计页面
            navigateToFollowerStatisticPage();

            // 抓取关注者数据
            List<FollowerData> followerDataList = scrapeFollowerData(request);

            logger.info("成功获取关注者统计数据，共 {} 条记录", followerDataList.size());
            return StatisticResult.success(followerDataList, getSupportedPlatform());

        } catch (DataStatisticException e) {
            logger.error("数据统计异常 - 平台: {}, 错误代码: {}, 消息: {}",
                        e.getPlatformType(), e.getErrorCode(), e.getMessage(), e);
            return StatisticResult.failure("数据统计失败: " + e.getMessage(), getSupportedPlatform());
        } catch (Exception e) {
            logger.error("获取关注者统计数据失败 - 未知异常", e);
            return StatisticResult.failure("获取数据失败: " + e.getMessage(), getSupportedPlatform());
        } finally {
            cleanupBrowserContext();
        }
    }

    /**
     * 导航到关注者统计页面
     */
    private void navigateToFollowerStatisticPage() {
        try {
            logger.info("导航到关注者统计页面: {}", FOLLOWER_STATISTIC_URL);
            currentPage.navigate(FOLLOWER_STATISTIC_URL);

            // 等待页面加载
            statisticPageInteraction.waitForPageLoad(currentPage);

            logger.info("关注者统计页面加载完成");
        } catch (Exception e) {
            throw new DataStatisticException.PageLoadException(getSupportedPlatform(),
                    "导航到统计页面失败: " + e.getMessage(), e);
        }
    }

    /**
     * 抓取关注者数据
     */
    private List<FollowerData> scrapeFollowerData(StatisticRequest request) {
        try {
            logger.info("开始抓取关注者数据，日期范围: {} 至 {}", request.getStartDate(), request.getEndDate());

            // 设置日期范围
            statisticPageInteraction.setDateRange(currentPage, request.getStartDate(), request.getEndDate());

            // 获取表格数据
            return statisticPageInteraction.getFollowerDataFromTable(currentPage, request);
        } catch (Exception e) {
            throw new DataStatisticException.DataParseException(getSupportedPlatform(),
                    "抓取数据失败: " + e.getMessage(), e);
        }
    }

    @Override
    public PlatformType getSupportedPlatform() {
        return PlatformType.WECHAT_VIDEO;
    }
}