package com.lazhu.gxc.publish.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.regex.Pattern;

/**
 * 字符串工具类
 * 提供字符串验证、清理和格式化功能
 */
public class StringUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(StringUtils.class);
    
    // 允许的特殊字符（基于Python版本）
    private static final String ALLOWED_SPECIAL_CHARS = "《》\"\":+?%°";
    
    // 短标题长度限制
    private static final int SHORT_TITLE_MAX_LENGTH = 16;
    private static final int SHORT_TITLE_MIN_LENGTH = 6;
    
    // 常用的正则表达式模式
    private static final Pattern WHITESPACE_PATTERN = Pattern.compile("\\s+");
    private static final Pattern HTML_TAG_PATTERN = Pattern.compile("<[^>]+>");
    
    /**
     * 检查字符串是否为空或null
     * 
     * @param str 待检查的字符串
     * @return 是否为空
     */
    public static boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }
    
    /**
     * 检查字符串是否不为空
     * 
     * @param str 待检查的字符串
     * @return 是否不为空
     */
    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }
    
    /**
     * 安全地获取字符串，如果为null则返回空字符串
     * 
     * @param str 原字符串
     * @return 非null的字符串
     */
    public static String safeString(String str) {
        return str == null ? "" : str;
    }
    
    /**
     * 格式化字符串用于短标题（参考Python版本的format_str_for_short_title）
     * 
     * @param originTitle 原始标题
     * @return 格式化后的短标题
     */
    public static String formatStrForShortTitle(String originTitle) {
        if (isEmpty(originTitle)) {
            logger.warn("原始标题为空，使用默认短标题");
            return "默认标题".concat(" ".repeat(Math.max(0, SHORT_TITLE_MIN_LENGTH - 4)));
        }
        
        StringBuilder filteredChars = new StringBuilder();
        
        // 移除不允许的特殊字符
        for (char ch : originTitle.toCharArray()) {
            if (Character.isLetterOrDigit(ch) || ALLOWED_SPECIAL_CHARS.indexOf(ch) != -1) {
                filteredChars.append(ch);
            } else if (ch == ',') {
                // 逗号替换为空格
                filteredChars.append(' ');
            }
            // 其他不允许的字符直接忽略
        }
        
        String formattedString = filteredChars.toString();
        
        // 调整字符串长度
        if (formattedString.length() > SHORT_TITLE_MAX_LENGTH) {
            // 截断字符串
            formattedString = formattedString.substring(0, SHORT_TITLE_MAX_LENGTH);
            logger.debug("标题被截断到{}字符: {}", SHORT_TITLE_MAX_LENGTH, formattedString);
        } else if (formattedString.length() < SHORT_TITLE_MIN_LENGTH) {
            // 使用空格来填充字符串
            int paddingLength = SHORT_TITLE_MIN_LENGTH - formattedString.length();
            formattedString += " ".repeat(paddingLength);
            logger.debug("标题被填充到{}字符: {}", SHORT_TITLE_MIN_LENGTH, formattedString);
        }
        
        return formattedString;
    }
    
    /**
     * 清理字符串，移除多余的空白字符
     * 
     * @param str 原字符串
     * @return 清理后的字符串
     */
    public static String cleanWhitespace(String str) {
        if (isEmpty(str)) {
            return "";
        }
        
        // 将多个连续的空白字符替换为单个空格
        return WHITESPACE_PATTERN.matcher(str.trim()).replaceAll(" ");
    }
    
    /**
     * 移除HTML标签
     * 
     * @param str 包含HTML标签的字符串
     * @return 移除HTML标签后的字符串
     */
    public static String removeHtmlTags(String str) {
        if (isEmpty(str)) {
            return "";
        }
        
        return HTML_TAG_PATTERN.matcher(str).replaceAll("");
    }
    
    /**
     * 验证标题长度是否在允许范围内
     * 
     * @param title 标题
     * @param maxLength 最大长度
     * @return 是否有效
     */
    public static boolean isValidTitleLength(String title, int maxLength) {
        if (isEmpty(title)) {
            logger.warn("标题为空");
            return false;
        }
        
        boolean isValid = title.length() <= maxLength;
        
        if (!isValid) {
            logger.warn("标题长度超出限制: {} 字符 (最大: {} 字符)", title.length(), maxLength);
        }
        
        return isValid;
    }
    
    /**
     * 验证描述长度是否在允许范围内
     * 
     * @param description 描述
     * @param maxLength 最大长度
     * @return 是否有效
     */
    public static boolean isValidDescriptionLength(String description, int maxLength) {
        if (isEmpty(description)) {
            // 描述可以为空
            return true;
        }
        
        boolean isValid = description.length() <= maxLength;
        
        if (!isValid) {
            logger.warn("描述长度超出限制: {} 字符 (最大: {} 字符)", description.length(), maxLength);
        }
        
        return isValid;
    }
    
    /**
     * 截断字符串到指定长度
     * 
     * @param str 原字符串
     * @param maxLength 最大长度
     * @return 截断后的字符串
     */
    public static String truncate(String str, int maxLength) {
        if (isEmpty(str) || maxLength <= 0) {
            return "";
        }
        
        if (str.length() <= maxLength) {
            return str;
        }
        
        String truncated = str.substring(0, maxLength);
        logger.debug("字符串被截断: 原长度={}, 新长度={}", str.length(), truncated.length());
        
        return truncated;
    }
    
    /**
     * 截断字符串并添加省略号
     * 
     * @param str 原字符串
     * @param maxLength 最大长度（包括省略号）
     * @return 截断后的字符串
     */
    public static String truncateWithEllipsis(String str, int maxLength) {
        if (isEmpty(str) || maxLength <= 0) {
            return "";
        }
        
        if (str.length() <= maxLength) {
            return str;
        }
        
        if (maxLength <= 3) {
            return "...".substring(0, maxLength);
        }
        
        String truncated = str.substring(0, maxLength - 3) + "...";
        logger.debug("字符串被截断并添加省略号: 原长度={}, 新长度={}", str.length(), truncated.length());
        
        return truncated;
    }
    
    /**
     * 验证字符串是否包含非法字符
     * 
     * @param str 待验证的字符串
     * @return 是否包含非法字符
     */
    public static boolean containsIllegalCharacters(String str) {
        if (isEmpty(str)) {
            return false;
        }
        
        // 检查是否包含控制字符或其他非法字符
        for (char ch : str.toCharArray()) {
            if (Character.isISOControl(ch) && ch != '\n' && ch != '\r' && ch != '\t') {
                logger.warn("发现非法字符: {} (Unicode: {})", ch, (int) ch);
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 清理字符串，移除非法字符
     * 
     * @param str 原字符串
     * @return 清理后的字符串
     */
    public static String removeIllegalCharacters(String str) {
        if (isEmpty(str)) {
            return "";
        }
        
        StringBuilder cleaned = new StringBuilder();
        
        for (char ch : str.toCharArray()) {
            // 保留正常字符和常见的空白字符
            if (!Character.isISOControl(ch) || ch == '\n' || ch == '\r' || ch == '\t') {
                cleaned.append(ch);
            }
        }
        
        return cleaned.toString();
    }
    
    /**
     * 格式化标签字符串，移除多余空格并用逗号分隔
     * 
     * @param tags 标签字符串
     * @return 格式化后的标签字符串
     */
    public static String formatTags(String tags) {
        if (isEmpty(tags)) {
            return "";
        }
        
        // 分割标签，清理空白，过滤空标签
        String[] tagArray = tags.split("[,，;；]");
        StringBuilder formattedTags = new StringBuilder();
        
        for (String tag : tagArray) {
            String cleanTag = cleanWhitespace(tag);
            if (isNotEmpty(cleanTag)) {
                if (formattedTags.length() > 0) {
                    formattedTags.append(",");
                }
                formattedTags.append(cleanTag);
            }
        }
        
        return formattedTags.toString();
    }
    
    /**
     * 计算字符串的显示宽度（中文字符算2个宽度）
     * 
     * @param str 字符串
     * @return 显示宽度
     */
    public static int getDisplayWidth(String str) {
        if (isEmpty(str)) {
            return 0;
        }
        
        int width = 0;
        for (char ch : str.toCharArray()) {
            // 中文字符、全角字符等占2个宽度
            if (ch >= 0x4E00 && ch <= 0x9FFF || // CJK统一汉字
                ch >= 0x3400 && ch <= 0x4DBF || // CJK扩展A
                ch >= 0xFF00 && ch <= 0xFFEF) { // 全角字符
                width += 2;
            } else {
                width += 1;
            }
        }
        
        return width;
    }
}