package com.lazhu.gxc.publish.browser.impl;

import com.lazhu.gxc.publish.browser.PageInteractionService;
import com.microsoft.playwright.ElementHandle;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.TimeoutError;
import com.microsoft.playwright.options.LoadState;
import com.microsoft.playwright.options.WaitForSelectorState;
import com.microsoft.playwright.options.MouseButton;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.file.Path;
import java.time.Duration;
import java.util.List;

/**
 * PageInteractionService的基本实现
 * 提供通用的页面交互功能实现
 */
public class BasicPageInteractionService implements PageInteractionService {
    
    private static final Logger logger = LoggerFactory.getLogger(BasicPageInteractionService.class);
    
    @Override
    public void navigateTo(Page page, String url, Duration timeout) throws PageInteractionException {
        try {
            page.navigate(url, new Page.NavigateOptions().setTimeout(timeout.toMillis()));
        } catch (Exception e) {
            throw new PageInteractionException("导航到URL失败: " + url, e);
        }
    }
    
    @Override
    public void waitForPageLoad(Page page, Duration timeout) throws PageInteractionException {
        try {
            page.waitForLoadState(LoadState.DOMCONTENTLOADED, new Page.WaitForLoadStateOptions().setTimeout(timeout.toMillis()));
        } catch (Exception e) {
            throw new PageInteractionException("等待页面加载超时", e);
        }
    }
    
    @Override
    public ElementHandle waitForElement(Page page, String selector, Duration timeout) throws PageInteractionException {
        try {
            return page.waitForSelector(selector, new Page.WaitForSelectorOptions().setTimeout(timeout.toMillis()));
        } catch (Exception e) {
            throw new PageInteractionException("等待元素失败: " + selector, e);
        }
    }
    
    @Override
    public ElementHandle waitForElementVisible(Page page, String selector, Duration timeout) throws PageInteractionException {
        try {
            return page.waitForSelector(selector, new Page.WaitForSelectorOptions()
                .setTimeout(timeout.toMillis())
                .setState(WaitForSelectorState.VISIBLE));
        } catch (Exception e) {
            throw new PageInteractionException("等待元素可见失败: " + selector, e);
        }
    }
    
    @Override
    public ElementHandle waitForElementClickable(Page page, String selector, Duration timeout) throws PageInteractionException {
        try {
            ElementHandle element = waitForElementVisible(page, selector, timeout);
            if (element == null || !element.isEnabled()) {
                throw new PageInteractionException("元素不可点击: " + selector);
            }
            return element;
        } catch (Exception e) {
            throw new PageInteractionException("等待元素可点击失败: " + selector, e);
        }
    }
    
    @Override
    public void waitForElementDisappear(Page page, String selector, Duration timeout) throws PageInteractionException {
        try {
            page.waitForSelector(selector, new Page.WaitForSelectorOptions()
                .setTimeout(timeout.toMillis())
                .setState(WaitForSelectorState.DETACHED));
        } catch (Exception e) {
            throw new PageInteractionException("等待元素消失失败: " + selector, e);
        }
    }
    
    @Override
    public void clickElement(Page page, String selector, Duration timeout) throws PageInteractionException {
        try {
            ElementHandle element = waitForElementClickable(page, selector, timeout);
            element.click();
        } catch (Exception e) {
            throw new PageInteractionException("点击元素失败: " + selector, e);
        }
    }
    
    @Override
    public void doubleClickElement(Page page, String selector, Duration timeout) throws PageInteractionException {
        try {
            ElementHandle element = waitForElementClickable(page, selector, timeout);
            element.dblclick();
        } catch (Exception e) {
            throw new PageInteractionException("双击元素失败: " + selector, e);
        }
    }
    
    @Override
    public void rightClickElement(Page page, String selector, Duration timeout) throws PageInteractionException {
        try {
            ElementHandle element = waitForElementClickable(page, selector, timeout);
            element.click(new ElementHandle.ClickOptions().setButton(MouseButton.RIGHT));
        } catch (Exception e) {
            throw new PageInteractionException("右键点击元素失败: " + selector, e);
        }
    }
    
    @Override
    public void typeText(Page page, String selector, String text, Duration timeout, boolean clearFirst) throws PageInteractionException {
        try {
            ElementHandle element = waitForElementVisible(page, selector, timeout);
            if (clearFirst) {
                element.fill("");
            }
            element.type(text);
        } catch (Exception e) {
            throw new PageInteractionException("输入文本失败: " + selector, e);
        }
    }
    
    @Override
    public void fillText(Page page, String selector, String text, Duration timeout) throws PageInteractionException {
        try {
            ElementHandle element = waitForElementVisible(page, selector, timeout);
            element.fill(text);
        } catch (Exception e) {
            throw new PageInteractionException("填充文本失败: " + selector, e);
        }
    }
    
    @Override
    public void clearText(Page page, String selector, Duration timeout) throws PageInteractionException {
        try {
            fillText(page, selector, "", timeout);
        } catch (Exception e) {
            throw new PageInteractionException("清空文本失败: " + selector, e);
        }
    }
    
    @Override
    public String getElementText(Page page, String selector, Duration timeout) throws PageInteractionException {
        try {
            ElementHandle element = waitForElement(page, selector, timeout);
            return element.textContent();
        } catch (Exception e) {
            throw new PageInteractionException("获取元素文本失败: " + selector, e);
        }
    }
    
    @Override
    public String getElementAttribute(Page page, String selector, String attributeName, Duration timeout) throws PageInteractionException {
        try {
            ElementHandle element = waitForElement(page, selector, timeout);
            return element.getAttribute(attributeName);
        } catch (Exception e) {
            throw new PageInteractionException("获取元素属性失败: " + selector + "@" + attributeName, e);
        }
    }
    
    @Override
    public boolean isElementPresent(Page page, String selector) {
        try {
            ElementHandle element = page.querySelector(selector);
            return element != null;
        } catch (Exception e) {
            return false;
        }
    }
    
    @Override
    public boolean isElementVisible(Page page, String selector) {
        try {
            ElementHandle element = page.querySelector(selector);
            return element != null && element.isVisible();
        } catch (Exception e) {
            return false;
        }
    }
    
    @Override
    public boolean isElementClickable(Page page, String selector) {
        try {
            ElementHandle element = page.querySelector(selector);
            return element != null && element.isVisible() && element.isEnabled();
        } catch (Exception e) {
            return false;
        }
    }
    
    @Override
    public void selectOptionByValue(Page page, String selector, String value, Duration timeout) throws PageInteractionException {
        try {
            ElementHandle element = waitForElement(page, selector, timeout);
            element.selectOption(value);
        } catch (Exception e) {
            throw new PageInteractionException("通过值选择选项失败: " + selector + "=" + value, e);
        }
    }
    
    @Override
    public void selectOptionByText(Page page, String selector, String text, Duration timeout) throws PageInteractionException {
        try {
            ElementHandle element = waitForElement(page, selector, timeout);
            element.selectOption(new String[]{text});
        } catch (Exception e) {
            throw new PageInteractionException("通过文本选择选项失败: " + selector + "=" + text, e);
        }
    }
    
    @Override
    public void uploadFile(Page page, String selector, Path filePath, Duration timeout) throws PageInteractionException {
        try {
            ElementHandle element = waitForElement(page, selector, timeout);
            element.setInputFiles(filePath);
        } catch (Exception e) {
            throw new PageInteractionException("上传文件失败: " + selector + " -> " + filePath, e);
        }
    }
    
    @Override
    public void uploadFiles(Page page, String selector, List<Path> filePaths, Duration timeout) throws PageInteractionException {
        try {
            ElementHandle element = waitForElement(page, selector, timeout);
            element.setInputFiles(filePaths.toArray(new Path[0]));
        } catch (Exception e) {
            throw new PageInteractionException("上传多个文件失败: " + selector, e);
        }
    }
    
    @Override
    public void scrollToElement(Page page, String selector, Duration timeout) throws PageInteractionException {
        try {
            ElementHandle element = waitForElement(page, selector, timeout);
            element.scrollIntoViewIfNeeded();
        } catch (Exception e) {
            throw new PageInteractionException("滚动到元素失败: " + selector, e);
        }
    }
    
    @Override
    public void scrollPage(Page page, int deltaX, int deltaY) throws PageInteractionException {
        try {
            page.mouse().wheel(deltaX, deltaY);
        } catch (Exception e) {
            throw new PageInteractionException("页面滚动失败", e);
        }
    }
    
    @Override
    public Object executeScript(Page page, String script) throws PageInteractionException {
        try {
            return page.evaluate(script);
        } catch (Exception e) {
            throw new PageInteractionException("执行JavaScript失败: " + script, e);
        }
    }
    
    @Override
    public void sleep(Duration duration) {
        try {
            Thread.sleep(duration.toMillis());
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.warn("Sleep interrupted");
        }
    }
    
    @Override
    public void takeScreenshot(Page page, Path filePath, boolean fullPage) throws PageInteractionException {
        try {
            page.screenshot(new Page.ScreenshotOptions()
                .setPath(filePath)
                .setFullPage(fullPage));
        } catch (Exception e) {
            throw new PageInteractionException("截取页面截图失败: " + filePath, e);
        }
    }
    
    @Override
    public void waitForNetworkIdle(Page page, Duration timeout) throws PageInteractionException {
        try {
            page.waitForLoadState(LoadState.NETWORKIDLE, 
                new Page.WaitForLoadStateOptions().setTimeout(timeout.toMillis()));
        } catch (Exception e) {
            throw new PageInteractionException("等待网络空闲超时", e);
        }
    }
}