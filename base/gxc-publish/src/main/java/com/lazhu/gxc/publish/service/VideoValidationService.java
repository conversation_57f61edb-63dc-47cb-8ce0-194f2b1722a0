package com.lazhu.gxc.publish.service;

import com.lazhu.common.media.VideoInfo;
import com.lazhu.gxc.publish.model.PlatformType;

import java.util.List;

/**
 * 视频验证服务接口
 * 不同平台对视频的验证规则可能存在差异
 */
public interface VideoValidationService {

    /**
     * 验证视频文件
     *
     * @param filePath 视频文件路径
     * @return 验证结果
     */
    ValidationResult validateVideo(String filePath);


    /**
     * 获取支持的平台类型
     *
     * @return 平台类型
     */
    PlatformType getSupportedPlatform();

    /**
     * 验证结果类
     */
    record ValidationResult(boolean valid, List<String> errors, List<String> warnings, VideoInfo videoInfo) {

        public boolean hasWarnings() {
            return !warnings.isEmpty();
        }

        public boolean hasErrors() {
            return !errors.isEmpty();
        }
    }
}