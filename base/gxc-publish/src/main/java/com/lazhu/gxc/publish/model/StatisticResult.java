package com.lazhu.gxc.publish.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 数据统计结果
 * 包含平台数据统计的返回信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StatisticResult {
    
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 错误信息（如果失败）
     */
    private String errorMessage;
    
    /**
     * 平台类型
     */
    private PlatformType platformType;
    
    /**
     * 关注者数据列表
     */
    private List<FollowerData> followerDataList;
    
    /**
     * 账号标识
     */
    private String accountId;
    
    /**
     * 创建成功结果
     */
    public static StatisticResult success(List<FollowerData> data, PlatformType platformType) {
        return StatisticResult.builder()
                .success(true)
                .followerDataList(data)
                .platformType(platformType)
                .build();
    }
    
    /**
     * 创建失败结果
     */
    public static StatisticResult failure(String errorMessage, PlatformType platformType) {
        return StatisticResult.builder()
                .success(false)
                .errorMessage(errorMessage)
                .platformType(platformType)
                .build();
    }
}