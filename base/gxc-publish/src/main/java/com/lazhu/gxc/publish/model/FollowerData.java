package com.lazhu.gxc.publish.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * 关注者数据统计
 * 包含微信视频号关注者数据的详细信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FollowerData {
    
    /**
     * 统计日期
     */
    private LocalDate date;
    
    /**
     * 新增关注数量
     */
    private Integer newFollowers;
    
    /**
     * 取消关注数量
     */
    private Integer unfollowed;
    
    /**
     * 净增关注数量（新增-取消）
     */
    private Integer netFollowers;
    
    /**
     * 关注者总数
     */
    private Integer totalFollowers;
    
    /**
     * 平台类型
     */
    private PlatformType platformType;
    
    /**
     * 计算净增关注数
     */
    public Integer calculateNetFollowers() {
        if (newFollowers != null && unfollowed != null) {
            return newFollowers - unfollowed;
        }
        return netFollowers;
    }
}