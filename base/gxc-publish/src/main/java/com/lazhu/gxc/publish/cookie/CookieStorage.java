package com.lazhu.gxc.publish.cookie;

import java.util.Map;

/**
 * Cookie存储接口
 * 支持多种存储方式：文件、数据库、Redis等
 */
public interface CookieStorage {

    /**
     * 保存Cookie
     *
     * @param platform 平台标识（如：wechat_channels）
     * @param userId   用户ID（可选，用于多用户场景）
     * @param cookies  Cookie键值对
     */
    void saveCookies(String platform, String userId, Map<String, String> cookies);

    /**
     * 保存Cookie（单用户场景）
     *
     * @param platform 平台标识
     * @param cookies  Cookie键值对
     */
    default void saveCookies(String platform, Map<String, String> cookies) {
        saveCookies(platform, null, cookies);
    }

    /**
     * 加载Cookie
     *
     * @param platform 平台标识
     * @param userId   用户ID（可选）
     * @return Cookie键值对，如果不存在返回null
     */
    Map<String, String> loadCookies(String platform, String userId);

    /**
     * 加载Cookie（单用户场景）
     *
     * @param platform 平台标识
     * @return Cookie键值对，如果不存在返回null
     */
    default Map<String, String> loadCookies(String platform) {
        return loadCookies(platform, null);
    }

    /**
     * 删除Cookie
     *
     * @param platform 平台标识
     * @param userId   用户ID（可选）
     * @return 是否删除成功
     */
    boolean deleteCookies(String platform, String userId);

    /**
     * 删除Cookie（单用户场景）
     *
     * @param platform 平台标识
     * @return 是否删除成功
     */
    default boolean deleteCookies(String platform) {
        return deleteCookies(platform, null);
    }

    /**
     * 检查Cookie是否存在
     *
     * @param platform 平台标识
     * @param userId   用户ID（可选）
     * @return 是否存在
     */
    boolean existsCookies(String platform, String userId);

    /**
     * 检查Cookie是否存在（单用户场景）
     *
     * @param platform 平台标识
     * @return 是否存在
     */
    default boolean existsCookies(String platform) {
        return existsCookies(platform, null);
    }

    /**
     * 清理过期的Cookie
     *
     * @return 清理的数量
     */
    int clearExpiredCookies();

    /**
     * 获取存储类型标识
     *
     * @return 存储类型（如：file、redis、database）
     */
    String getStorageType();
}