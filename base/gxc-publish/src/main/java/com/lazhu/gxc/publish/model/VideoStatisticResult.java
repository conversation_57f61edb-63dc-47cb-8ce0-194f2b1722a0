package com.lazhu.gxc.publish.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 视频数据统计结果类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VideoStatisticResult {

    /**
     * 操作是否成功
     */
    private boolean success;

    /**
     * 错误消息（失败时）
     */
    private String errorMessage;

    /**
     * 平台类型
     */
    private PlatformType platformType;

    /**
     * 视频数据列表
     */
    private List<VideoData> videoDataList;

    /**
     * 总记录数
     */
    private Integer totalCount;

    /**
     * 当前页码
     */
    private Integer currentPage;

    /**
     * 页面大小
     */
    private Integer pageSize;

    /**
     * 总页数
     */
    private Integer totalPages;

    /**
     * 数据获取时间
     */
    private LocalDateTime fetchTime;

    /**
     * 统计摘要信息
     */
    private VideoStatisticSummary summary;

    /**
     * 创建成功结果
     */
    public static VideoStatisticResult success(List<VideoData> videoDataList, PlatformType platformType) {
        return VideoStatisticResult.builder()
                .success(true)
                .videoDataList(videoDataList)
                .platformType(platformType)
                .totalCount(videoDataList != null ? videoDataList.size() : 0)
                .fetchTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建成功结果（带分页信息）
     */
    public static VideoStatisticResult success(List<VideoData> videoDataList, 
                                             PlatformType platformType,
                                             Integer totalCount,
                                             Integer currentPage,
                                             Integer pageSize) {
        int totalPages = totalCount != null && pageSize != null && pageSize > 0 
                        ? (int) Math.ceil((double) totalCount / pageSize) 
                        : 1;
        
        return VideoStatisticResult.builder()
                .success(true)
                .videoDataList(videoDataList)
                .platformType(platformType)
                .totalCount(totalCount)
                .currentPage(currentPage)
                .pageSize(pageSize)
                .totalPages(totalPages)
                .fetchTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建失败结果
     */
    public static VideoStatisticResult failure(String errorMessage, PlatformType platformType) {
        return VideoStatisticResult.builder()
                .success(false)
                .errorMessage(errorMessage)
                .platformType(platformType)
                .fetchTime(LocalDateTime.now())
                .build();
    }

    /**
     * 计算统计摘要
     */
    public VideoStatisticSummary calculateSummary() {
        if (videoDataList == null || videoDataList.isEmpty()) {
            return VideoStatisticSummary.empty();
        }

        long totalPlay = 0;
        long totalLike = 0;
        long totalComment = 0;
        long totalShare = 0;
        long totalFollow = 0;
        double totalEngagementRate = 0;
        int validEngagementCount = 0;

        for (VideoData video : videoDataList) {
            if (video.getPlayCount() != null) totalPlay += video.getPlayCount();
            if (video.getLikeCount() != null) totalLike += video.getLikeCount();
            if (video.getCommentCount() != null) totalComment += video.getCommentCount();
            if (video.getShareCount() != null) totalShare += video.getShareCount();
            if (video.getFollowCount() != null) totalFollow += video.getFollowCount();
            
            Double engagementRate = video.calculateEngagementRate();
            if (engagementRate != null && engagementRate > 0) {
                totalEngagementRate += engagementRate;
                validEngagementCount++;
            }
        }

        double averageEngagementRate = validEngagementCount > 0 
                                     ? totalEngagementRate / validEngagementCount 
                                     : 0.0;

        return VideoStatisticSummary.builder()
                .totalVideos(videoDataList.size())
                .totalPlayCount(totalPlay)
                .totalLikeCount(totalLike)
                .totalCommentCount(totalComment)
                .totalShareCount(totalShare)
                .totalFollowCount(totalFollow)
                .averageEngagementRate(averageEngagementRate)
                .build();
    }

    /**
     * 设置统计摘要
     */
    public void setSummary() {
        this.summary = calculateSummary();
    }

    /**
     * 视频统计摘要类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VideoStatisticSummary {
        
        /**
         * 视频总数
         */
        private Integer totalVideos;
        
        /**
         * 总播放量
         */
        private Long totalPlayCount;
        
        /**
         * 总点赞数
         */
        private Long totalLikeCount;
        
        /**
         * 总评论数
         */
        private Long totalCommentCount;
        
        /**
         * 总分享数
         */
        private Long totalShareCount;
        
        /**
         * 总关注数
         */
        private Long totalFollowCount;
        
        /**
         * 平均互动率
         */
        private Double averageEngagementRate;

        /**
         * 创建空摘要
         */
        public static VideoStatisticSummary empty() {
            return VideoStatisticSummary.builder()
                    .totalVideos(0)
                    .totalPlayCount(0L)
                    .totalLikeCount(0L)
                    .totalCommentCount(0L)
                    .totalShareCount(0L)
                    .totalFollowCount(0L)
                    .averageEngagementRate(0.0)
                    .build();
        }
    }
}
