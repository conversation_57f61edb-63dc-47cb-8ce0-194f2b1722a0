package com.lazhu.gxc.publish.platform;

import com.lazhu.gxc.publish.config.PublisherConfig;
import com.lazhu.gxc.publish.model.UploadRequest;
import com.lazhu.gxc.publish.model.UploadResult;
import com.lazhu.gxc.publish.service.AuthService;
import com.lazhu.gxc.publish.service.VideoUploadService;
import com.lazhu.gxc.publish.service.VideoValidationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 抽象平台服务基类
 * 使用模板方法模式定义通用的上传流程，实现错误处理和重试逻辑
 */
public abstract class AbstractVideoUploadService implements VideoUploadService {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    protected final PublisherConfig config;
    protected final AuthService authService;
    protected final VideoValidationService videoValidationService;

    /**
     * 构造函数
     *
     * @param config                 发布器配置
     * @param authService            认证服务
     * @param videoValidationService 视频验证服务
     */
    protected AbstractVideoUploadService(PublisherConfig config,
                                         AuthService authService,
                                         VideoValidationService videoValidationService) {
        this.config = config;
        this.authService = authService;
        this.videoValidationService = videoValidationService;
    }

    /**
     * 上传视频
     *
     * @return 上传结果
     */
    @Override
    public final UploadResult uploadVideo(UploadRequest uploadRequest) {
        logger.info("开始上传视频到平台: {}, 文件: {}", getSupportedPlatform().getDisplayName(), uploadRequest.getVideoPath());

        // 1. 预检查
        UploadResult preCheckResult = performPreChecks(uploadRequest);
        if (!preCheckResult.isSuccess()) {
            return preCheckResult;
        }
        // 2. 执行上传流程
        return performUpload(uploadRequest);
    }


    /**
     * 取消上传任务（默认实现）
     *
     * @param taskId 任务ID
     * @return 取消结果
     */
    @Override
    public boolean cancelUpload(String taskId) {
        logger.warn("平台 {} 不支持取消上传功能", getSupportedPlatform().getDisplayName());
        return false;
    }

    // ========== 抽象方法，子类必须实现 ==========

    /**
     * 验证视频元数据字段
     */
    protected abstract boolean validateMetadata(UploadRequest.VideoMetadata metadata) throws Exception;

    /**
     * 导航到上传页面
     */
    protected abstract void navigateToUploadPage(UploadRequest request);

    /**
     * 上传视频文件
     *
     * @param videoFilePath 视频文件路径
     */
    protected abstract void uploadVideoFile(String videoFilePath);

    /**
     * 填写视频元数据
     *
     * @param metadata 视频元数据
     */
    protected abstract void fillMetadata(UploadRequest.VideoMetadata metadata);

    /**
     * 提交上传
     *
     * @return 上传结果
     */
    protected abstract UploadResult submitUpload();


    // ========== 模板方法的具体步骤 ==========

    /**
     * 执行预检查
     *
     * @return 检查结果
     */
    protected UploadResult performPreChecks(UploadRequest request) {
        try {
            // 1. 验证元数据
            try {
                validateMetadata(request.getVideoMetadata());
            } catch (Exception ex) {
                return UploadResult.failure("视频元数据验证失败: " + ex.getMessage(), getSupportedPlatform());
            }

            // 2. 验证视频文件
            VideoValidationService.ValidationResult validationResult = videoValidationService.validateVideo(request.getVideoPath());
            if (!validationResult.valid()) {
                return UploadResult.failure("视频文件验证失败", getSupportedPlatform());
            }

            // 3. 验证认证信息
            if (!authService.validateCookies(request.getUserId())) {
                return UploadResult.failure("认证信息无效，请重新登录", getSupportedPlatform());
            }

            logger.info("预检查通过");
            return UploadResult.success("预检查通过", getSupportedPlatform());

        } catch (Exception e) {
            logger.error("预检查失败: {}", e.getMessage(), e);
            return UploadResult.failure("预检查失败: " + e.getMessage(), e, getSupportedPlatform());
        }
    }

    /**
     * 执行上传流程
     *
     * @param request 上传请求
     * @return 上传结果
     */
    protected UploadResult performUpload(UploadRequest request) {
        try {
            logger.info("开始执行上传流程");

            // 1. 导航到上传页面
            navigateToUploadPage(request);
            logger.info("已导航到上传页面");

            // 2. 上传视频文件
            uploadVideoFile(request.getVideoPath());
            logger.info("视频文件上传完成");

            // 3. 填写元数据
            fillMetadata(request.getVideoMetadata());
            logger.info("元数据填写完成");

            // 4. 提交上传
            UploadResult result = submitUpload();
            logger.info("上传提交完成，结果: {}", result.isSuccess() ? "成功" : "失败");

            return result;

        } catch (Exception e) {
            logger.error("上传流程执行失败: {}", e.getMessage(), e);
            return UploadResult.failure("上传失败: " + e.getMessage(), e, getSupportedPlatform());
        }
    }
}