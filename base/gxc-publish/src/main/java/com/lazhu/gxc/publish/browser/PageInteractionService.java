package com.lazhu.gxc.publish.browser;

import com.microsoft.playwright.ElementHandle;
import com.microsoft.playwright.Page;

import java.nio.file.Path;
import java.time.Duration;
import java.util.List;

/**
 * 页面交互服务接口
 * 定义通用的页面交互方法接口，包含元素等待、点击、输入等基础操作
 */
public interface PageInteractionService {
    
    /**
     * 导航到指定URL
     * 
     * @param page 页面实例
     * @param url 目标URL
     * @param timeout 超时时间
     * @throws PageInteractionException 导航失败时抛出
     */
    void navigateTo(Page page, String url, Duration timeout) throws PageInteractionException;
    
    /**
     * 等待页面加载完成
     * 
     * @param page 页面实例
     * @param timeout 超时时间
     * @throws PageInteractionException 等待超时时抛出
     */
    void waitForPageLoad(Page page, Duration timeout) throws PageInteractionException;
    
    /**
     * 等待元素出现
     * 
     * @param page 页面实例
     * @param selector 元素选择器
     * @param timeout 超时时间
     * @return 元素句柄
     * @throws PageInteractionException 元素未找到时抛出
     */
    ElementHandle waitForElement(Page page, String selector, Duration timeout) throws PageInteractionException;
    
    /**
     * 等待元素可见
     * 
     * @param page 页面实例
     * @param selector 元素选择器
     * @param timeout 超时时间
     * @return 元素句柄
     * @throws PageInteractionException 元素不可见时抛出
     */
    ElementHandle waitForElementVisible(Page page, String selector, Duration timeout) throws PageInteractionException;
    
    /**
     * 等待元素可点击
     * 
     * @param page 页面实例
     * @param selector 元素选择器
     * @param timeout 超时时间
     * @return 元素句柄
     * @throws PageInteractionException 元素不可点击时抛出
     */
    ElementHandle waitForElementClickable(Page page, String selector, Duration timeout) throws PageInteractionException;
    
    /**
     * 等待元素消失
     * 
     * @param page 页面实例
     * @param selector 元素选择器
     * @param timeout 超时时间
     * @throws PageInteractionException 等待超时时抛出
     */
    void waitForElementDisappear(Page page, String selector, Duration timeout) throws PageInteractionException;
    
    /**
     * 点击元素
     * 
     * @param page 页面实例
     * @param selector 元素选择器
     * @param timeout 等待超时时间
     * @throws PageInteractionException 点击失败时抛出
     */
    void clickElement(Page page, String selector, Duration timeout) throws PageInteractionException;
    
    /**
     * 双击元素
     * 
     * @param page 页面实例
     * @param selector 元素选择器
     * @param timeout 等待超时时间
     * @throws PageInteractionException 双击失败时抛出
     */
    void doubleClickElement(Page page, String selector, Duration timeout) throws PageInteractionException;
    
    /**
     * 右键点击元素
     * 
     * @param page 页面实例
     * @param selector 元素选择器
     * @param timeout 等待超时时间
     * @throws PageInteractionException 右键点击失败时抛出
     */
    void rightClickElement(Page page, String selector, Duration timeout) throws PageInteractionException;
    
    /**
     * 在输入框中输入文本
     * 
     * @param page 页面实例
     * @param selector 输入框选择器
     * @param text 要输入的文本
     * @param timeout 等待超时时间
     * @param clearFirst 是否先清空输入框
     * @throws PageInteractionException 输入失败时抛出
     */
    void typeText(Page page, String selector, String text, Duration timeout, boolean clearFirst) 
            throws PageInteractionException;
    
    /**
     * 填充输入框（快速填充，不模拟键盘输入）
     * 
     * @param page 页面实例
     * @param selector 输入框选择器
     * @param text 要填充的文本
     * @param timeout 等待超时时间
     * @throws PageInteractionException 填充失败时抛出
     */
    void fillText(Page page, String selector, String text, Duration timeout) throws PageInteractionException;
    
    /**
     * 清空输入框
     * 
     * @param page 页面实例
     * @param selector 输入框选择器
     * @param timeout 等待超时时间
     * @throws PageInteractionException 清空失败时抛出
     */
    void clearText(Page page, String selector, Duration timeout) throws PageInteractionException;
    
    /**
     * 获取元素文本内容
     * 
     * @param page 页面实例
     * @param selector 元素选择器
     * @param timeout 等待超时时间
     * @return 元素文本内容
     * @throws PageInteractionException 获取失败时抛出
     */
    String getElementText(Page page, String selector, Duration timeout) throws PageInteractionException;
    
    /**
     * 获取元素属性值
     * 
     * @param page 页面实例
     * @param selector 元素选择器
     * @param attributeName 属性名
     * @param timeout 等待超时时间
     * @return 属性值
     * @throws PageInteractionException 获取失败时抛出
     */
    String getElementAttribute(Page page, String selector, String attributeName, Duration timeout) 
            throws PageInteractionException;
    
    /**
     * 检查元素是否存在
     * 
     * @param page 页面实例
     * @param selector 元素选择器
     * @return true如果元素存在，否则false
     */
    boolean isElementPresent(Page page, String selector);
    
    /**
     * 检查元素是否可见
     * 
     * @param page 页面实例
     * @param selector 元素选择器
     * @return true如果元素可见，否则false
     */
    boolean isElementVisible(Page page, String selector);
    
    /**
     * 检查元素是否可点击
     * 
     * @param page 页面实例
     * @param selector 元素选择器
     * @return true如果元素可点击，否则false
     */
    boolean isElementClickable(Page page, String selector);
    
    /**
     * 选择下拉框选项（通过值）
     * 
     * @param page 页面实例
     * @param selector 下拉框选择器
     * @param value 选项值
     * @param timeout 等待超时时间
     * @throws PageInteractionException 选择失败时抛出
     */
    void selectOptionByValue(Page page, String selector, String value, Duration timeout) 
            throws PageInteractionException;
    
    /**
     * 选择下拉框选项（通过文本）
     * 
     * @param page 页面实例
     * @param selector 下拉框选择器
     * @param text 选项文本
     * @param timeout 等待超时时间
     * @throws PageInteractionException 选择失败时抛出
     */
    void selectOptionByText(Page page, String selector, String text, Duration timeout) 
            throws PageInteractionException;
    
    /**
     * 上传文件
     * 
     * @param page 页面实例
     * @param selector 文件输入框选择器
     * @param filePath 文件路径
     * @param timeout 等待超时时间
     * @throws PageInteractionException 上传失败时抛出
     */
    void uploadFile(Page page, String selector, Path filePath, Duration timeout) throws PageInteractionException;
    
    /**
     * 上传多个文件
     * 
     * @param page 页面实例
     * @param selector 文件输入框选择器
     * @param filePaths 文件路径列表
     * @param timeout 等待超时时间
     * @throws PageInteractionException 上传失败时抛出
     */
    void uploadFiles(Page page, String selector, List<Path> filePaths, Duration timeout) 
            throws PageInteractionException;
    
    /**
     * 滚动到元素位置
     * 
     * @param page 页面实例
     * @param selector 元素选择器
     * @param timeout 等待超时时间
     * @throws PageInteractionException 滚动失败时抛出
     */
    void scrollToElement(Page page, String selector, Duration timeout) throws PageInteractionException;
    
    /**
     * 滚动页面
     * 
     * @param page 页面实例
     * @param deltaX 水平滚动距离
     * @param deltaY 垂直滚动距离
     * @throws PageInteractionException 滚动失败时抛出
     */
    void scrollPage(Page page, int deltaX, int deltaY) throws PageInteractionException;
    
    /**
     * 执行JavaScript代码
     * 
     * @param page 页面实例
     * @param script JavaScript代码
     * @return 执行结果
     * @throws PageInteractionException 执行失败时抛出
     */
    Object executeScript(Page page, String script) throws PageInteractionException;
    
    /**
     * 等待指定时间
     * 
     * @param duration 等待时间
     */
    void sleep(Duration duration);
    
    /**
     * 截取页面截图
     * 
     * @param page 页面实例
     * @param filePath 截图保存路径
     * @param fullPage 是否截取整个页面
     * @throws PageInteractionException 截图失败时抛出
     */
    void takeScreenshot(Page page, Path filePath, boolean fullPage) throws PageInteractionException;
    
    /**
     * 等待网络空闲
     * 
     * @param page 页面实例
     * @param timeout 超时时间
     * @throws PageInteractionException 等待超时时抛出
     */
    void waitForNetworkIdle(Page page, Duration timeout) throws PageInteractionException;
    
    /**
     * 页面交互异常类
     */
    class PageInteractionException extends Exception {
        public PageInteractionException(String message) {
            super(message);
        }
        
        public PageInteractionException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}