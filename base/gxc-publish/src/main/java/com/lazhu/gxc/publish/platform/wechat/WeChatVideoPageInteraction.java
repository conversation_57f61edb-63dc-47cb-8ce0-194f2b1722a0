package com.lazhu.gxc.publish.platform.wechat;

import com.alibaba.fastjson.JSONObject;
import com.lazhu.gxc.publish.browser.PageInteractionService;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.Response;
import com.microsoft.playwright.options.LoadState;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.file.Path;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 微信视频号页面交互类
 * 实现微信视频号特定的页面操作，包括导航、文件上传、标题填写、标签设置等
 */
public class WeChatVideoPageInteraction {

    private static final Logger logger = LoggerFactory.getLogger(WeChatVideoPageInteraction.class);

    // 微信视频号相关URL和选择器
    // 上传视频页面
    private static final String WECHAT_UPLOAD_URL = "https://channels.weixin.qq.com/platform/post/create";
    // 视频列表页面
    private static final String WECHAT_VIDEO_LIST_URL = "https://channels.weixin.qq.com/platform/post/list";

    // 页面元素选择器
    private static final String FILE_UPLOAD_INPUT = "input[type=\"file\"][accept*=\"video\"]";

    // 表单元素选择器 - 根据实际页面结构更新
    private static final String VIDEO_TITLE_INPUT = "input[placeholder*=\"概括视频主要内容\"]"; // 短标题输入框
    private static final String VIDEO_DESCRIPTION_INPUT = "div[contenteditable=\"\"][data-placeholder=\"添加描述\"]"; // 视频描述输入框
    // 位置相关选择器
    private static final String LOCATION_SELECTOR = ".post-position-wrap";
    // 合集选择器
    private static final String COLLECTION_SELECTOR = ".post-album-display-wrap";

    // 标签和话题选择器
    private static final String TAG_BUTTON = ".finder-tag-wrap";
    private static final String AT_BUTTON = ".finder-tag-wrap";

    // 发布相关按钮
    private static final String PUBLISH_BUTTON = "button.weui-desktop-btn_primary:has-text('发表')";
    private static final String SCHEDULE_BUTTON = "input[type=\"radio\"][value=\"1\"]";

    // 上传状态检测选择器
    private static final String UPLOAD_STATUS_PROCESSING = ".media-progress, .upload-progress, .ant-progress, .uploading, [class*='upload'][class*='progress']";
    private static final String UPLOAD_STATUS_FAILED = ".status-msg.error";
    private static final String VIDEO_PREVIEW = ".media-video-wrap, .video-preview, .upload-preview, .ant-upload-list-item-info";
    private static final String UPLOAD_PERCENT = ".ant-progress-text, .upload-percent";

    // 成功和错误消息选择器
    private static final String SUCCESS_MESSAGE = ".success-msg, .upload-success, [class*='success']";
    private static final String ERROR_MESSAGE = ".status-msg.error .msg-tip, .error-msg, .upload-error, [class*='error']";


    private final PageInteractionService pageInteraction;
    private final Duration defaultTimeout;
    private final Duration uploadTimeout;

    public WeChatVideoPageInteraction(PageInteractionService pageInteraction) {
        this.pageInteraction = pageInteraction;
        this.defaultTimeout = Duration.ofSeconds(30);
        this.uploadTimeout = Duration.ofMinutes(10); // 视频上传可能需要更长时间
    }

    /**
     * 导航到上传页面
     */
    public void navigateToUploadPage(Page page) throws PageInteractionService.PageInteractionException {
        pageInteraction.navigateTo(page, WECHAT_UPLOAD_URL, defaultTimeout);
        pageInteraction.waitForPageLoad(page, defaultTimeout);
        logger.info("成功导航到上传页面");
    }

    /**
     * 上传视频文件
     */
    public void uploadVideoFile(Page page, Path videoFilePath) throws PageInteractionService.PageInteractionException {

        try {
            // 等待上传区域出现
            pageInteraction.waitForElement(page, ".ant-upload-drag-container", defaultTimeout);
            page.locator(FILE_UPLOAD_INPUT).setInputFiles(videoFilePath);
            // 等待上传开始的指示
            pageInteraction.sleep(Duration.ofSeconds(2));

        } catch (Exception e) {
            throw new PageInteractionService.PageInteractionException("上传视频文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 等待视频上传完成并检测上传状态
     */
    public UploadStatus waitForUploadComplete(Page page) throws PageInteractionService.PageInteractionException {
        logger.info("等待视频上传完成");

        try {
            long startTime = System.currentTimeMillis();
            long maxWaitTime = uploadTimeout.toMillis();

            while (System.currentTimeMillis() - startTime < maxWaitTime) {

                // 检查是否上传失败
                if (pageInteraction.isElementPresent(page, UPLOAD_STATUS_FAILED)) {
                    String errorMsg = "上传失败";
                    try {
                        if (pageInteraction.isElementPresent(page, ERROR_MESSAGE)) {
                            errorMsg = pageInteraction.getElementText(page, ERROR_MESSAGE, Duration.ofSeconds(5));
                        }
                    } catch (Exception e) {
                        // 忽略获取错误消息的异常
                    }
                    logger.error("视频上传失败: {}", errorMsg);
                    return UploadStatus.FAILED;
                }

                // 检查上传进度
                if (pageInteraction.isElementPresent(page, UPLOAD_STATUS_PROCESSING)) {
                    // 尝试获取上传进度
                    String progressText = "";
                    try {
                        if (pageInteraction.isElementPresent(page, UPLOAD_PERCENT)) {
                            progressText = pageInteraction.getElementText(page, UPLOAD_PERCENT, Duration.ofSeconds(2));
                        }
                    } catch (Exception e) {
                        // 忽略获取进度文本的异常
                    }

                    logger.info("视频上传中... {}", progressText);
                    pageInteraction.sleep(Duration.ofSeconds(3));
                    continue;
                }

                // 检查是否成功
                if (pageInteraction.isElementPresent(page, "#fullScreenVideo")) {
                    String src = pageInteraction.getElementAttribute(page, "#fullScreenVideo", "src", Duration.ofSeconds(2));
                    if (src != null) {
                        logger.info("检测到上传成功状态 - 视频预览可用");
                        return UploadStatus.SUCCESS;
                    }
                } else {
                    logger.error("未知状态");
                    return UploadStatus.FAILED;
                }

                // 继续等待
                pageInteraction.sleep(Duration.ofSeconds(2));
            }

            logger.warn("视频上传等待超时");
            return UploadStatus.TIMEOUT;

        } catch (Exception e) {
            logger.error("检测上传状态时发生异常", e);
            throw new PageInteractionService.PageInteractionException("检测上传状态失败: " + e.getMessage(), e);
        }
    }

    /**
     * 填写视频标题（短标题）
     */
    public void fillVideoTitle(Page page, String title) throws PageInteractionService.PageInteractionException {
        logger.info("填写视频短标题: {}", title);

        try {
            // 等待短标题输入框出现
            pageInteraction.waitForElement(page, VIDEO_TITLE_INPUT, defaultTimeout);

            // 清空并填写短标题
            pageInteraction.clearText(page, VIDEO_TITLE_INPUT, defaultTimeout);
            pageInteraction.typeText(page, VIDEO_TITLE_INPUT, title, defaultTimeout, false);

            logger.info("视频短标题填写完成");

        } catch (Exception e) {
            logger.error("填写视频短标题失败", e);
            throw new PageInteractionService.PageInteractionException("填写视频短标题失败: " + e.getMessage(), e);
        }
    }

    /**
     * 填写视频描述
     */
    public void fillVideoDescription(Page page, String description) throws PageInteractionService.PageInteractionException {

        logger.info("填写视频描述");

        try {
            // 等待描述输入框出现
            pageInteraction.waitForElement(page, VIDEO_DESCRIPTION_INPUT, defaultTimeout);

            // 点击输入框获取焦点
            pageInteraction.clickElement(page, VIDEO_DESCRIPTION_INPUT, defaultTimeout);

            pageInteraction.typeText(page, VIDEO_DESCRIPTION_INPUT, description, defaultTimeout, false);

            logger.info("视频描述填写完成");

        } catch (Exception e) {
            logger.error("填写视频描述失败", e);
            throw new PageInteractionService.PageInteractionException("填写视频描述失败: " + e.getMessage(), e);
        }
    }

    /**
     * 填写位置信息
     */
    public void fillLocation(Page page, String location) throws PageInteractionService.PageInteractionException {
        if (location == null || location.trim().isEmpty()) {
            logger.info("跳过位置填写（位置为空）");
            return;
        }

        logger.info("填写位置信息: {}", location);

        try {
            // 等待位置选择器出现
            pageInteraction.waitForElement(page, LOCATION_SELECTOR, defaultTimeout);

            // 点击位置显示区域打开位置选择
            pageInteraction.clickElement(page, LOCATION_SELECTOR, defaultTimeout);

            // 等待位置选择面板出现
            pageInteraction.sleep(Duration.ofMillis(500));

            // 检查是否有搜索输入框
            String searchInput = ".search-input input[placeholder*=\"搜索\"]";
            if (pageInteraction.isElementPresent(page, searchInput)) {
                // 在搜索框中输入位置
                pageInteraction.typeText(page, searchInput, location, defaultTimeout, false);

                // 等待搜索结果
                pageInteraction.sleep(Duration.ofSeconds(1));

                // 点击第二个搜索结果（第一个通常是"不显示位置"）
                String secondResult = ".option-item:nth-child(2) .location-item";
                if (pageInteraction.isElementPresent(page, secondResult)) {
                    pageInteraction.clickElement(page, secondResult, defaultTimeout);
                    logger.info("已选择搜索到的位置: {}", location);
                } else {
                    logger.warn("未搜索到位置：{}，跳过填写", location);
                }
            }
        } catch (Exception e) {
            logger.error("填写位置信息失败", e);
            throw new PageInteractionService.PageInteractionException("填写位置信息失败: " + e.getMessage(), e);
        }
    }


    /**
     * 选择合集
     */
    public void selectCollection(Page page, String collectionName) throws PageInteractionService.PageInteractionException {
        if (collectionName == null || collectionName.trim().isEmpty()) {
            logger.info("跳过合集选择（合集名称为空）");
            return;
        }

        logger.info("选择合集: {}", collectionName);

        try {
            // 检查合集选择器是否存在
            if (!pageInteraction.isElementPresent(page, COLLECTION_SELECTOR)) {
                logger.info("未找到合集选择器，跳过合集选择");
                return;
            }

            // 等待合集选择器
            pageInteraction.waitForElementClickable(page, COLLECTION_SELECTOR, defaultTimeout);

            // 点击合集选择器打开下拉菜单
            pageInteraction.clickElement(page, COLLECTION_SELECTOR, defaultTimeout);

            // 等待下拉菜单出现
            pageInteraction.sleep(Duration.ofMillis(500));

            // 尝试选择指定的合集
            String collectionOption = String.format("li:has-text(\"%s\"), option:has-text(\"%s\")", collectionName, collectionName);
            if (pageInteraction.isElementPresent(page, collectionOption)) {
                pageInteraction.clickElement(page, collectionOption, defaultTimeout);
                logger.info("合集选择完成: {}", collectionName);
            } else {
                logger.warn("未找到指定的合集: {}", collectionName);
            }

        } catch (Exception e) {
            logger.error("选择合集失败", e);
            throw new PageInteractionService.PageInteractionException("选择合集失败: " + e.getMessage(), e);
        }
    }

    /**
     * 设置定时发布
     */
    public void setScheduleTime(Page page, LocalDateTime scheduleTime) throws PageInteractionService.PageInteractionException {
        if (scheduleTime == null) {
            logger.info("跳过定时发布设置");
            return;
        }

        logger.info("设置定时发布时间: {}", scheduleTime);

        try {
            // 检查定时发布按钮是否存在
            if (!pageInteraction.isElementPresent(page, SCHEDULE_BUTTON)) {
                logger.info("未找到定时发布选项，跳过定时设置");
                return;
            }

            // 点击定时发布按钮
            pageInteraction.clickElement(page, SCHEDULE_BUTTON, defaultTimeout);

            // 等待定时设置界面出现
            pageInteraction.sleep(Duration.ofSeconds(1));

            // 设置日期和时间（具体的选择器需要根据实际页面结构调整）
            DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm");

            String dateStr = scheduleTime.format(dateFormatter);
            String timeStr = scheduleTime.format(timeFormatter);

            // 填写日期
            String dateInput = "input[type=\"date\"], .date-picker input";
            if (pageInteraction.isElementPresent(page, dateInput)) {
                pageInteraction.fillText(page, dateInput, dateStr, defaultTimeout);
            }

            // 填写时间
            String timeInput = "input[type=\"time\"], .time-picker input";
            if (pageInteraction.isElementPresent(page, timeInput)) {
                pageInteraction.fillText(page, timeInput, timeStr, defaultTimeout);
            }

            // 确认定时设置
            String confirmButton = ".confirm-schedule, button:has-text(\"确定\")";
            if (pageInteraction.isElementPresent(page, confirmButton)) {
                pageInteraction.clickElement(page, confirmButton, defaultTimeout);
            }

            logger.info("定时发布设置完成");

        } catch (Exception e) {
            logger.error("设置定时发布失败", e);
            throw new PageInteractionService.PageInteractionException("设置定时发布失败: " + e.getMessage(), e);
        }
    }

    /**
     * 发布视频
     */
    public void publishVideo(Page page) throws PageInteractionService.PageInteractionException {

        try {
            // 等待发布按钮可点击（确保不是disabled状态）
            pageInteraction.waitForElement(page, PUBLISH_BUTTON, defaultTimeout);
            // 点击发布按钮
            pageInteraction.clickElement(page, PUBLISH_BUTTON, defaultTimeout);

            // 等待发布完成
            pageInteraction.sleep(Duration.ofSeconds(2));

            logger.info("视频发布请求已提交");

        } catch (Exception e) {
            throw new PageInteractionService.PageInteractionException("发布视频失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检查发布结果
     */
    public PublishResult checkPublishResult(Page page) throws PageInteractionService.PageInteractionException {
        logger.info("检查发布结果");

        try {
            // 等待跳转到视频列表页
            page.waitForURL(url -> url.contains(WECHAT_VIDEO_LIST_URL), new Page.WaitForURLOptions().setTimeout(3000));

            // 发布成功后会自动跳转到视频列表页面
            String currentUrl = page.url();
            logger.info("发布结果：当前URL: {}", currentUrl);

            // 检查是否跳转到视频列表页面
            if (currentUrl.contains("post/list")) {
                logger.info("已跳转到视频列表页面");
                page.waitForLoadState(LoadState.DOMCONTENTLOADED);
                // 等待视频列表API响应
                Response response = page.waitForResponse(resp -> resp.url().contains("/mmfinderassistant-bin/post/post_list"), new Page.WaitForResponseOptions().setTimeout(30000), () -> {
                    logger.info("视频列表接口响应成功");
                });
                String responseText = response.text();
                logger.info("API响应内容: {}", responseText);
                JSONObject object = JSONObject.parseObject(responseText);
                JSONObject data = object.getJSONObject("data");
                JSONObject video = data.getJSONArray("list").getJSONObject(0);
                String videoId = video.getString("exportId");
                return PublishResult.success("视频发布成功", videoId);
            } else {
                // 没跳转到首页，说明发布失败
                return PublishResult.failure("视频发布失败");
            }
        } catch (Exception e) {
            throw new PageInteractionService.PageInteractionException("检查发布结果失败: " + e.getMessage(), e);
        }
    }


    /**
     * 上传状态枚举
     */
    public enum UploadStatus {
        SUCCESS,    // 上传成功
        FAILED,     // 上传失败
        PROCESSING, // 处理中
        TIMEOUT     // 超时
    }

    /**
     * 发布结果类
     */
    @Getter
    public static class PublishResult {
        private final boolean success;
        private final String message;
        private final String objectId;

        private PublishResult(boolean success, String message, String objectId) {
            this.success = success;
            this.message = message;
            this.objectId = objectId;
        }

        public static PublishResult success(String message, String objectId) {
            return new PublishResult(true, message, objectId);
        }

        public static PublishResult failure(String message) {
            return new PublishResult(false, message, null);
        }

        public static PublishResult unknown(String message) {
            return new PublishResult(false, message, null);
        }

        @Override
        public String toString() {
            return String.format("PublishResult{success=%s, message='%s'}", success, message);
        }
    }
}