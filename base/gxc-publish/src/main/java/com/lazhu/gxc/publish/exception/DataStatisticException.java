package com.lazhu.gxc.publish.exception;

import com.lazhu.gxc.publish.model.PlatformType;

/**
 * 数据统计异常类
 * 用于封装数据统计过程中的各种异常情况
 */
public class DataStatisticException extends RuntimeException {

    private final PlatformType platformType;
    private final String errorCode;

    public DataStatisticException(String message) {
        super(message);
        this.platformType = null;
        this.errorCode = "UNKNOWN";
    }

    public DataStatisticException(String message, Throwable cause) {
        super(message, cause);
        this.platformType = null;
        this.errorCode = "UNKNOWN";
    }

    public DataStatisticException(PlatformType platformType, String errorCode, String message) {
        super(message);
        this.platformType = platformType;
        this.errorCode = errorCode;
    }

    public DataStatisticException(PlatformType platformType, String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.platformType = platformType;
        this.errorCode = errorCode;
    }

    public PlatformType getPlatformType() {
        return platformType;
    }

    public String getErrorCode() {
        return errorCode;
    }

    /**
     * 页面加载异常
     */
    public static class PageLoadException extends DataStatisticException {
        public PageLoadException(PlatformType platformType, String message) {
            super(platformType, "PAGE_LOAD_ERROR", message);
        }

        public PageLoadException(PlatformType platformType, String message, Throwable cause) {
            super(platformType, "PAGE_LOAD_ERROR", message, cause);
        }
    }

    /**
     * 数据解析异常
     */
    public static class DataParseException extends DataStatisticException {
        public DataParseException(PlatformType platformType, String message) {
            super(platformType, "DATA_PARSE_ERROR", message);
        }

        public DataParseException(PlatformType platformType, String message, Throwable cause) {
            super(platformType, "DATA_PARSE_ERROR", message, cause);
        }
    }

    /**
     * 认证异常
     */
    public static class AuthenticationException extends DataStatisticException {
        public AuthenticationException(PlatformType platformType, String message) {
            super(platformType, "AUTH_ERROR", message);
        }

        public AuthenticationException(PlatformType platformType, String message, Throwable cause) {
            super(platformType, "AUTH_ERROR", message, cause);
        }
    }

    /**
     * 网络异常
     */
    public static class NetworkException extends DataStatisticException {
        public NetworkException(PlatformType platformType, String message) {
            super(platformType, "NETWORK_ERROR", message);
        }

        public NetworkException(PlatformType platformType, String message, Throwable cause) {
            super(platformType, "NETWORK_ERROR", message, cause);
        }
    }

    /**
     * 数据验证异常
     */
    public static class DataValidationException extends DataStatisticException {
        public DataValidationException(PlatformType platformType, String message) {
            super(platformType, "DATA_VALIDATION_ERROR", message);
        }

        public DataValidationException(PlatformType platformType, String message, Throwable cause) {
            super(platformType, "DATA_VALIDATION_ERROR", message, cause);
        }
    }
}
