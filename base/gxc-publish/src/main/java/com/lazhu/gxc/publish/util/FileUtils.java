package com.lazhu.gxc.publish.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;

/**
 * 文件工具类
 * 提供文件存在性检查、路径处理、文件大小和格式验证功能
 */
public class FileUtils {

    private static final Logger logger = LoggerFactory.getLogger(FileUtils.class);

    /**
     * 检查文件是否存在
     *
     * @param filePath 文件路径
     * @return 文件是否存在
     */
    public static boolean exists(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            logger.warn("文件路径为空或null");
            return false;
        }

        File file = new File(filePath);
        boolean exists = file.exists();

        if (!exists) {
            logger.warn("文件不存在: {}", filePath);
        }

        return exists;
    }

    /**
     * 检查文件是否存在且为文件（非目录）
     *
     * @param filePath 文件路径
     * @return 是否为有效文件
     */
    public static boolean isValidFile(String filePath) {
        if (!exists(filePath)) {
            return false;
        }

        File file = new File(filePath);
        boolean isFile = file.isFile();

        if (!isFile) {
            logger.warn("路径不是文件: {}", filePath);
        }

        return isFile;
    }

    /**
     * 获取文件大小（字节）
     *
     * @param filePath 文件路径
     * @return 文件大小，如果文件不存在返回-1
     */
    public static long getFileSize(String filePath) {
        if (!isValidFile(filePath)) {
            return -1;
        }

        try {
            Path path = Paths.get(filePath);
            long size = Files.size(path);
            logger.debug("文件大小: {} bytes, 文件: {}", size, filePath);
            return size;
        } catch (IOException e) {
            logger.error("获取文件大小失败: {}", filePath, e);
            return -1;
        }
    }

    /**
     * 验证文件大小是否在允许范围内
     *
     * @param filePath 文件路径
     * @return 文件大小是否有效
     */
    public static boolean isValidFileSize(String filePath, long maxFileSize) {
        long fileSize = getFileSize(filePath);

        if (fileSize == -1) {
            return false;
        }

        boolean isValid = fileSize <= maxFileSize;

        if (!isValid) {
            logger.warn("文件大小超出限制: {} bytes (最大: {} bytes), 文件: {}", fileSize, maxFileSize, filePath);
        }

        return isValid;
    }

    /**
     * 获取文件扩展名
     *
     * @param filePath 文件路径
     * @return 文件扩展名（小写），如果没有扩展名返回空字符串
     */
    public static String getFileExtension(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            return "";
        }

        String fileName = Paths.get(filePath).getFileName().toString();
        int lastDotIndex = fileName.lastIndexOf('.');

        if (lastDotIndex == -1 || lastDotIndex == fileName.length() - 1) {
            return "";
        }

        return fileName.substring(lastDotIndex + 1).toLowerCase();
    }

    /**
     * 验证文件格式是否为支持的视频格式
     *
     * @param filePath 文件路径
     * @return 文件格式是否支持
     */
    public static boolean isValidVideoFormat(String filePath, List<String> supportedFormats) {
        String extension = getFileExtension(filePath);

        if (extension.isEmpty()) {
            logger.warn("文件没有扩展名: {}", filePath);
            return false;
        }

        boolean isValid = supportedFormats.contains(extension);

        if (!isValid) {
            logger.warn("不支持的视频格式: {}, 支持的格式: {}", extension, supportedFormats);
        }

        return isValid;
    }

    /**
     * 规范化文件路径
     *
     * @param filePath 原始文件路径
     * @return 规范化后的文件路径
     */
    public static String normalizePath(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            return "";
        }

        try {
            Path path = Paths.get(filePath);
            return path.normalize().toString();
        } catch (Exception e) {
            logger.error("路径规范化失败: {}", filePath, e);
            return filePath;
        }
    }

    /**
     * 获取文件名（不包含路径）
     *
     * @param filePath 文件路径
     * @return 文件名
     */
    public static String getFileName(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            return "";
        }

        try {
            Path path = Paths.get(filePath);
            return path.getFileName().toString();
        } catch (Exception e) {
            logger.error("获取文件名失败: {}", filePath, e);
            return "";
        }
    }

    /**
     * 获取文件名（不包含扩展名）
     *
     * @param filePath 文件路径
     * @return 不包含扩展名的文件名
     */
    public static String getFileNameWithoutExtension(String filePath) {
        String fileName = getFileName(filePath);

        if (fileName.isEmpty()) {
            return "";
        }

        int lastDotIndex = fileName.lastIndexOf('.');

        if (lastDotIndex == -1) {
            return fileName;
        }

        return fileName.substring(0, lastDotIndex);
    }

    /**
     * 创建目录（如果不存在）
     *
     * @param dirPath 目录路径
     * @return 创建是否成功
     */
    public static boolean createDirectoryIfNotExists(String dirPath) {
        if (dirPath == null || dirPath.trim().isEmpty()) {
            logger.warn("目录路径为空");
            return false;
        }

        try {
            Path path = Paths.get(dirPath);

            if (Files.exists(path)) {
                return Files.isDirectory(path);
            }

            Files.createDirectories(path);
            logger.info("创建目录: {}", dirPath);
            return true;
        } catch (IOException e) {
            logger.error("创建目录失败: {}", dirPath, e);
            return false;
        }
    }

    /**
     * 格式化文件大小为可读字符串
     *
     * @param bytes 字节数
     * @return 格式化后的文件大小字符串
     */
    public static String formatFileSize(long bytes) {
        if (bytes < 0) {
            return "未知";
        }

        if (bytes < 1024) {
            return bytes + " B";
        }

        if (bytes < 1024 * 1024) {
            return String.format("%.2f KB", bytes / 1024.0);
        }

        if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", bytes / (1024.0 * 1024.0));
        }

        return String.format("%.2f GB", bytes / (1024.0 * 1024.0 * 1024.0));
    }
}