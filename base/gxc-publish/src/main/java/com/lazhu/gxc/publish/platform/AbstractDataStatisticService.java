package com.lazhu.gxc.publish.platform;

import com.lazhu.gxc.publish.browser.BrowserManager;
import com.lazhu.gxc.publish.browser.PageInteractionService;
import com.lazhu.gxc.publish.config.PublisherConfig;
import com.lazhu.gxc.publish.cookie.CookieStorage;
import com.lazhu.gxc.publish.model.AuthenticationInfo;
import com.lazhu.gxc.publish.model.StatisticRequest;
import com.lazhu.gxc.publish.service.AuthService;
import com.lazhu.gxc.publish.service.DataStatisticService;
import com.microsoft.playwright.BrowserContext;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.options.Cookie;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 数据统计服务抽象基类
 * 提供通用的数据统计功能实现
 */
public abstract class AbstractDataStatisticService implements DataStatisticService {

    protected static final Logger logger = LoggerFactory.getLogger(AbstractDataStatisticService.class);

    protected final BrowserManager browserManager;
    protected final AuthService authService;

    protected BrowserContext currentBrowserContext;
    protected Page currentPage;

    protected AbstractDataStatisticService(BrowserManager browserManager, AuthService authService) {
        this.browserManager = browserManager;
        this.authService = authService;
    }

    /**
     * 初始化浏览器环境
     */
    protected void initializeBrowser(String userId) {
        try {
            String browserId = "statistic-browser-" + UUID.randomUUID() + "-" + System.currentTimeMillis();
            String contextId = "statistic-context-" + UUID.randomUUID() + "-" + System.currentTimeMillis();
            browserManager.createBrowser(browserId);
            currentBrowserContext = browserManager.createContext(browserId, contextId, null);
            currentPage = currentBrowserContext.newPage();

            // 加载cookie
            AuthenticationInfo authInfo = authService.loadAuthInfo(userId);
            List<Map<String, String>> cookies = authInfo.getCookies();
            List<Cookie> cookieList = new ArrayList<>();
            for (Map<String, String> cookieMap : cookies) {
                Cookie cookie = new Cookie(cookieMap.get("name"), cookieMap.get("value"));
                cookie.setDomain(".weixin.qq.com");
                cookie.setPath("/");
                cookieList.add(cookie);
            }
            currentBrowserContext.addCookies(cookieList);

            logger.info("浏览器环境初始化完成");
        } catch (Exception e) {
            logger.error("浏览器环境初始化失败", e);
            throw new RuntimeException("浏览器初始化失败: " + e.getMessage(), e);
        }
    }


    /**
     * 清理浏览器资源
     */
    protected void cleanupBrowserContext() {
        if (currentBrowserContext != null) {
            try {
                currentBrowserContext.close();
                logger.info("浏览器上下文已清理");
            } catch (Exception e) {
                logger.warn("清理浏览器上下文时发生异常", e);
            } finally {
                currentBrowserContext = null;
                currentPage = null;
            }
        }
    }

    /**
     * 验证请求参数
     */
    protected void validateRequest(StatisticRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("统计请求不能为空");
        }
        if (!request.isValid()) {
            throw new IllegalArgumentException("统计请求参数无效");
        }
        if (request.getPlatformType() != getSupportedPlatform()) {
            throw new IllegalArgumentException("不支持的平台类型: " + request.getPlatformType());
        }
    }
}