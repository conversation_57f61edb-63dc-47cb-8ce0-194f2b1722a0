package com.lazhu.gxc.publish.platform.wechat;

import com.lazhu.common.media.VideoInfo;
import com.lazhu.gxc.publish.model.PlatformType;
import com.lazhu.gxc.publish.platform.AbstractVideoValidationService;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;

/**
 * 微信视频号视频验证服务实现
 * 继承抽象验证服务，使用微信视频号专用配置
 */
@Getter
public class WeChatVideoValidationService extends AbstractVideoValidationService {

    private static final Logger logger = LoggerFactory.getLogger(WeChatVideoValidationService.class);

    /**
     * 构造函数
     */
    public WeChatVideoValidationService() {
        super(
                Arrays.asList("mp4"),                    // supportedFormats
                Arrays.asList("h264"),                   // supportedCodecs
                1024L * 1024 * 1024 * 20,               // maxFileSize (20GB)
                8 * 60 * 60,                            // maxDurationSeconds (8小时)
                720,                                    // minWidth
                1280,                                   // minHeight
                1080,                                   // recommendedWidth
                1920                                    // recommendedHeight
        );
        logger.debug("微信视频号验证参数: 格式={}, 最大大小={}MB, 最大时长={}分钟, 最小分辨率={}x{}, 推荐分辨率={}x{}", supportedFormats, maxFileSize, maxDurationSeconds, minWidth, minHeight, recommendedWidth, recommendedHeight);
    }


    @Override
    public PlatformType getSupportedPlatform() {
        return PlatformType.WECHAT_VIDEO;
    }

    @Override
    protected void validatePlatformSpecificProperties(VideoInfo videoInfo, List<String> errors, List<String> warnings) {

    }

}