package com.lazhu.gxc.publish;

import com.lazhu.gxc.publish.browser.BrowserManager;
import com.lazhu.gxc.publish.browser.PageInteractionService;
import com.lazhu.gxc.publish.browser.impl.BasicPageInteractionService;
import com.lazhu.gxc.publish.config.PublisherConfig;
import com.lazhu.gxc.publish.cookie.impl.FileCookieStorage;
import com.lazhu.gxc.publish.model.*;
import com.lazhu.gxc.publish.service.PlatformServiceFactory;
import com.lazhu.gxc.publish.service.VideoUploadService;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Closeable;
import java.io.IOException;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * VideoPublisher主入口类
 * 提供统一的多平台视频上传API，集成配置加载、服务工厂和错误处理
 * 实现简单易用的公共接口方法
 */
public class VideoPublisher implements Closeable {

    private static final Logger logger = LoggerFactory.getLogger(VideoPublisher.class);


    @Getter
    private final PublisherConfig config;
    private final BrowserManager browserManager;
    private final PageInteractionService pageInteractionService;
    private final PlatformServiceFactory platformServiceFactory;
    private final ExecutorService executorService;

    private volatile boolean initialized = false;
    private volatile boolean closed = false;


    /**
     * Spring Boot依赖注入构造函数
     *
     * @param config                 发布器配置
     * @param platformServiceFactory 平台服务工厂
     * @param browserManager         浏览器管理器
     * @param pageInteractionService 页面交互服务
     */
    public VideoPublisher(PublisherConfig config,
                          PlatformServiceFactory platformServiceFactory,
                          BrowserManager browserManager,
                          PageInteractionService pageInteractionService) {

        try {
            this.config = config;
            this.browserManager = browserManager;
            this.pageInteractionService = pageInteractionService;
            this.platformServiceFactory = platformServiceFactory;

            this.executorService = Executors.newFixedThreadPool(
                    Runtime.getRuntime().availableProcessors()
            );

            this.initialized = true;
            logger.info("VideoPublisher初始化完成（Spring Boot模式）");

        } catch (Exception e) {
            logger.error("VideoPublisher初始化失败", e);
            throw new RuntimeException("VideoPublisher初始化失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用自定义配置构造VideoPublisher
     *
     * @param config 发布器配置
     */
    public VideoPublisher(PublisherConfig config) {

        try {
            this.config = config;

            // 初始化核心服务
            this.browserManager = new BrowserManager(config);
            this.pageInteractionService = createPageInteractionService();

            this.platformServiceFactory = new PlatformServiceFactory(
                    config,
                    browserManager,
                    pageInteractionService,
                    new FileCookieStorage(config.getCookiePath())
            );

            this.executorService = Executors.newFixedThreadPool(
                    Runtime.getRuntime().availableProcessors()
            );

            this.initialized = true;
            logger.info("VideoPublisher初始化完成");

        } catch (Exception e) {
            logger.error("VideoPublisher初始化失败", e);
            throw new RuntimeException("VideoPublisher初始化失败: " + e.getMessage(), e);
        }
    }

    // ========== 公共API方法 ==========

    /**
     * 登录
     */
    public LoginSession startLogin(String account, PlatformType platformType) {
        return platformServiceFactory.getAuthService(platformType).startLogin(account);
    }

    /**
     * 检查登录状态
     */
    public LoginResult checkLoginStatus(PlatformType platformType, String sessionId) {
        return platformServiceFactory.getAuthService(platformType).checkLoginStatus(sessionId);
    }

    /**
     * 验证cookie
     */
    public boolean validateCookies(PlatformType platformType, String account) {
        return platformServiceFactory.getAuthService(platformType).validateCookies(account);
    }


    /**
     * 上传视频到指定平台
     *
     * @param request 上传请求
     * @return 上传结果
     */
    public UploadResult uploadVideo(UploadRequest request) {
        validateInitialization();
        String videoFilePath = request.getVideoPath();
        PlatformType platformType = request.getPlatformType();

        logger.info("开始上传视频到平台: {}, 文件: {}", platformType.getDisplayName(), videoFilePath);

        try {
            // 1. 检查平台支持
            if (!platformServiceFactory.isPlatformSupported(platformType)) {
                String message = "不支持的平台类型: " + platformType.getDisplayName();
                return UploadResult.failure(message, platformType);
            }

            // 2. 获取上传服务
            VideoUploadService uploadService = platformServiceFactory.getVideoUploadService(platformType);
            if (uploadService == null) {
                String message = "无法获取平台上传服务: " + platformType.getDisplayName();
                return UploadResult.failure(message, platformType);
            }

            // 3. 执行上传
            UploadResult result = uploadService.uploadVideo(request);

            logger.info("视频上传完成，结果: {}", result.isSuccess() ? "成功" : "失败");
            return result;

        } catch (Exception e) {
            logger.error("上传视频时发生异常", e);
            return UploadResult.failure("上传失败: " + e.getMessage(), e, platformType);
        }
    }

    /**
     * 异步上传视频到指定平台
     *
     * @return CompletableFuture包装的上传结果
     */
    public CompletableFuture<UploadResult> uploadVideoAsync(UploadRequest request) {
        validateInitialization();

        return CompletableFuture.supplyAsync(() -> uploadVideo(request), executorService);
    }


    // ========== 辅助方法 ==========


    /**
     * 取消正在进行的上传任务
     *
     * @param platformType 平台类型
     * @param taskId       任务ID（如果支持）
     * @return 取消结果
     */
    public boolean cancelUpload(PlatformType platformType, String taskId) {
        validateInitialization();

        logger.info("取消上传任务: 平台={}, 任务ID={}", platformType.getDisplayName(), taskId);

        try {
            VideoUploadService uploadService = platformServiceFactory.getVideoUploadService(platformType);
            return uploadService.cancelUpload(taskId);

        } catch (Exception e) {
            logger.error("取消上传任务时发生异常", e);
            return false;
        }
    }

    /**
     * 获取所有支持的平台类型
     *
     * @return 支持的平台类型数组
     */
    public PlatformType[] getSupportedPlatforms() {
        validateInitialization();
        return platformServiceFactory.getSupportedPlatforms();
    }


    /**
     * 验证VideoPublisher是否已正确初始化
     */
    private void validateInitialization() {
        if (!initialized) {
            throw new IllegalStateException("VideoPublisher未初始化");
        }

        if (closed) {
            throw new IllegalStateException("VideoPublisher已关闭");
        }
    }

    /**
     * 创建页面交互服务实例
     */
    private PageInteractionService createPageInteractionService() {
        return new BasicPageInteractionService();
    }

    // ========== 私有辅助方法 ==========

    @Override
    public void close() throws IOException {
        if (closed) {
            return;
        }

        logger.info("关闭VideoPublisher，清理资源");

        try {
            // 关闭线程池
            if (executorService != null && !executorService.isShutdown()) {
                executorService.shutdown();
                try {
                    if (!executorService.awaitTermination(10, TimeUnit.SECONDS)) {
                        executorService.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    executorService.shutdownNow();
                }
            }

            // 清理服务工厂
            if (platformServiceFactory != null) {
                platformServiceFactory.destroy();
            }

            // 关闭浏览器管理器
            if (browserManager != null) {
                browserManager.shutdown();
            }

            closed = true;
            logger.info("VideoPublisher关闭完成");

        } catch (Exception e) {
            logger.error("关闭VideoPublisher时发生异常", e);
            throw new IOException("关闭VideoPublisher失败", e);
        }
    }


}