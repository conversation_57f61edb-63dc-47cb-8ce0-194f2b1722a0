package com.lazhu.gxc.publish.cookie.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.lazhu.gxc.publish.cookie.CookieStorage;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 文件Cookie存储实现
 * 将Cookie以JSON格式存储到本地文件系统
 */
@Slf4j
public class FileCookieStorage implements CookieStorage {

    private final String basePath;
    private final ObjectMapper objectMapper;
    private final Map<String, Object> lockMap = new ConcurrentHashMap<>();

    public FileCookieStorage(String basePath) {
        this.basePath = basePath;
        this.objectMapper = new ObjectMapper();
        this.objectMapper.registerModule(new JavaTimeModule());
        initializeStorageDirectory();
    }

    private void initializeStorageDirectory() {
        try {
            Path basePathObj = Paths.get(basePath);
            if (!Files.exists(basePathObj)) {
                Files.createDirectories(basePathObj);
                log.info("创建Cookie存储目录: {}", basePath);
            }
        } catch (IOException e) {
            log.error("创建Cookie存储目录失败: {}", basePath, e);
            throw new RuntimeException("无法创建Cookie存储目录", e);
        }
    }

    @Override
    public void saveCookies(String platform, String userId, Map<String, String> cookies) {
        if (platform == null || platform.trim().isEmpty()) {
            throw new IllegalArgumentException("平台标识不能为空");
        }

        if (cookies == null || cookies.isEmpty()) {
            log.warn("尝试保存空的Cookie，平台: {}, 用户: {}", platform, userId);
            return;
        }

        String filePath = getCookieFilePath(platform, userId);
        Object lock = lockMap.computeIfAbsent(filePath, k -> new Object());

        synchronized (lock) {
            try {
                CookieData cookieData = new CookieData();
                cookieData.setPlatform(platform);
                cookieData.setUserId(userId);
                cookieData.setCookies(new HashMap<>(cookies));
                cookieData.setLastUpdated(new Date());

                File file = new File(filePath);
                File parentDir = file.getParentFile();
                if (!parentDir.exists() && !parentDir.mkdirs()) {
                    throw new IOException("无法创建父目录: " + parentDir.getAbsolutePath());
                }

                objectMapper.writeValue(file, cookieData);
                log.info("Cookie保存成功 - 平台: {}, 用户: {}, 文件: {}", platform, userId, filePath);

            } catch (IOException e) {
                log.error("保存Cookie失败 - 平台: {}, 用户: {}, 文件: {}", platform, userId, filePath, e);
                throw new RuntimeException("保存Cookie失败", e);
            }
        }
    }

    @Override
    public Map<String, String> loadCookies(String platform, String userId) {
        if (platform == null || platform.trim().isEmpty()) {
            throw new IllegalArgumentException("平台标识不能为空");
        }

        String filePath = getCookieFilePath(platform, userId);
        Object lock = lockMap.computeIfAbsent(filePath, k -> new Object());

        synchronized (lock) {
            try {
                File file = new File(filePath);
                if (!file.exists()) {
                    log.debug("Cookie文件不存在 - 平台: {}, 用户: {}, 文件: {}", platform, userId, filePath);
                    return null;
                }

                CookieData cookieData = objectMapper.readValue(file, CookieData.class);
                log.info("Cookie加载成功 - 平台: {}, 用户: {}, 数量: {}",
                        platform, userId, cookieData.getCookies().size());

                return new HashMap<>(cookieData.getCookies());

            } catch (IOException e) {
                log.error("加载Cookie失败 - 平台: {}, 用户: {}, 文件: {}", platform, userId, filePath, e);
                return null;
            }
        }
    }

    @Override
    public boolean deleteCookies(String platform, String userId) {
        if (platform == null || platform.trim().isEmpty()) {
            throw new IllegalArgumentException("平台标识不能为空");
        }

        String filePath = getCookieFilePath(platform, userId);
        Object lock = lockMap.computeIfAbsent(filePath, k -> new Object());

        synchronized (lock) {
            try {
                File file = new File(filePath);
                if (!file.exists()) {
                    log.debug("Cookie文件不存在，无需删除 - 平台: {}, 用户: {}", platform, userId);
                    return false;
                }

                boolean deleted = file.delete();
                if (deleted) {
                    log.info("Cookie删除成功 - 平台: {}, 用户: {}", platform, userId);
                    lockMap.remove(filePath);
                } else {
                    log.warn("Cookie删除失败 - 平台: {}, 用户: {}", platform, userId);
                }
                return deleted;

            } catch (Exception e) {
                log.error("删除Cookie时发生异常 - 平台: {}, 用户: {}", platform, userId, e);
                return false;
            }
        }
    }

    @Override
    public boolean existsCookies(String platform, String userId) {
        if (platform == null || platform.trim().isEmpty()) {
            return false;
        }

        String filePath = getCookieFilePath(platform, userId);
        File file = new File(filePath);
        return file.exists() && file.isFile() && file.canRead();
    }

    @Override
    public int clearExpiredCookies() {
        // 文件存储暂不支持过期清理，可以在未来版本中添加
        // 可以通过在CookieData中添加过期时间字段来实现
        log.info("文件存储暂不支持过期Cookie清理功能");
        return 0;
    }

    @Override
    public String getStorageType() {
        return "file";
    }

    /**
     * 获取Cookie文件路径
     */
    private String getCookieFilePath(String platform, String userId) {
        StringBuilder pathBuilder = new StringBuilder(basePath);

        if (!basePath.endsWith(File.separator)) {
            pathBuilder.append(File.separator);
        }

        pathBuilder.append(platform);

        if (userId != null && !userId.trim().isEmpty()) {
            pathBuilder.append("_").append(userId);
        }

        pathBuilder.append("_cookies.json");

        return pathBuilder.toString();
    }

    /**
     * Cookie数据结构
     */
    @Setter
    @Getter
    public static class CookieData {
        private String platform;
        private String userId;
        private Map<String, String> cookies;
        private Date lastUpdated;

    }
}