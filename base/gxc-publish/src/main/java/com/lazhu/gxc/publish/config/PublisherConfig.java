package com.lazhu.gxc.publish.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 通用发布器配置类
 * 包含所有平台共用的基础配置项
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PublisherConfig {

    /**
     * 是否启用发布器
     */
    @Builder.Default
    private Boolean enabled = true;

    // ========== 浏览器配置 ==========

    /**
     * Chrome浏览器路径（可选，不指定则使用系统默认）
     */
    private String chromePath;

    /**
     * 是否以无头模式运行浏览器
     */
    @NotNull
    @Builder.Default
    private Boolean headless = false;

    /**
     * 浏览器操作超时时间（毫秒）
     */
    @Min(value = 1000, message = "浏览器超时时间不能少于1000毫秒")
    @Builder.Default
    private Integer browserTimeout = 30000;

    // ========== 上传配置 ==========

    /**
     * 上传操作超时时间（毫秒）
     */
    @Min(value = 10000, message = "上传超时时间不能少于10000毫秒")
    @Builder.Default
    private Integer uploadTimeout = 300000;

    // ========== 存储配置 ==========

    /**
     * Cookie文件存储路径
     */
    @NotBlank(message = "Cookie存储路径不能为空")
    @Builder.Default
    private String cookiePath = "./cookies";

    // ========== 线程池配置 ==========

    /**
     * 异步上传线程池大小
     */
    @Min(value = 1, message = "线程池大小不能少于1")
    @Builder.Default
    private Integer threadPoolSize = Runtime.getRuntime().availableProcessors();

    /**
     * 线程池队列大小
     */
    @Min(value = 1, message = "队列大小不能少于1")
    @Builder.Default
    private Integer queueSize = 100;
}