package com.lazhu.gxc.publish.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 认证信息模型
 * 封装Cookie和会话数据，支持序列化存储
 */
@Data
public class AuthenticationInfo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    private static final ObjectMapper objectMapper = new ObjectMapper().registerModule(new JavaTimeModule());

    // Getter methods
    @JsonProperty("platform")
    private final PlatformType platform;

    @JsonProperty("cookies")
    private final List<Map<String, String>> cookies;

    @JsonProperty("origins")
    private final List<Map<String, Object>> origins;

    @JsonProperty("localStorage")
    private final List<Map<String, Object>> localStorage;

    @JsonProperty("sessionStorage")
    private final List<Map<String, Object>> sessionStorage;

    @JsonProperty("createdTime")
    private final LocalDateTime createdTime;

    @JsonProperty("lastValidatedTime")
    private LocalDateTime lastValidatedTime;

    @JsonProperty("isValid")
    private boolean isValid;

    /**
     * 构造函数
     */
    @JsonCreator
    public AuthenticationInfo(
            @JsonProperty("platform") PlatformType platform,
            @JsonProperty("cookies") List<Map<String, String>> cookies,
            @JsonProperty("origins") List<Map<String, Object>> origins,
            @JsonProperty("localStorage") List<Map<String, Object>> localStorage,
            @JsonProperty("sessionStorage") List<Map<String, Object>> sessionStorage,
            @JsonProperty("createdTime") LocalDateTime createdTime,
            @JsonProperty("lastValidatedTime") LocalDateTime lastValidatedTime,
            @JsonProperty("isValid") boolean isValid) {
        this.platform = platform;
        this.cookies = cookies;
        this.origins = origins;
        this.localStorage = localStorage;
        this.sessionStorage = sessionStorage;
        this.createdTime = createdTime != null ? createdTime : LocalDateTime.now();
        this.lastValidatedTime = lastValidatedTime;
        this.isValid = isValid;
    }

    /**
     * 简化构造函数
     */
    public AuthenticationInfo(PlatformType platform,
                              List<Map<String, String>> cookies,
                              List<Map<String, Object>> origins,
                              List<Map<String, Object>> localStorage,
                              List<Map<String, Object>> sessionStorage) {
        this(platform, cookies, origins, localStorage, sessionStorage, LocalDateTime.now(), null, true);
    }


    /**
     * 从JSON字符串反序列化
     *
     * @param json JSON字符串
     * @return AuthenticationInfo实例
     * @throws JsonProcessingException 反序列化异常
     */
    public static AuthenticationInfo fromJson(String json) throws JsonProcessingException {
        return objectMapper.readValue(json, AuthenticationInfo.class);
    }

    /**
     * 更新验证状态
     *
     * @param isValid 是否有效
     */
    public void updateValidationStatus(boolean isValid) {
        this.isValid = isValid;
        this.lastValidatedTime = LocalDateTime.now();
    }

    /**
     * 检查认证信息是否过期
     *
     * @param expirationHours 过期小时数
     * @return 如果过期返回true
     */
    public boolean isExpired(int expirationHours) {
        if (createdTime == null) {
            return true;
        }
        return createdTime.plusHours(expirationHours).isBefore(LocalDateTime.now());
    }

    /**
     * 检查是否需要重新验证
     *
     * @param validationIntervalHours 验证间隔小时数
     * @return 如果需要重新验证返回true
     */
    public boolean needsRevalidation(int validationIntervalHours) {
        if (lastValidatedTime == null) {
            return true;
        }
        return lastValidatedTime.plusHours(validationIntervalHours).isBefore(LocalDateTime.now());
    }

    /**
     * 序列化为JSON字符串
     *
     * @return JSON字符串
     * @throws JsonProcessingException 序列化异常
     */
    public String toJson() throws JsonProcessingException {
        return objectMapper.writeValueAsString(this);
    }

    /**
     * 检查认证信息是否包含必要的数据
     *
     * @return 如果包含必要数据返回true
     */
    public boolean hasEssentialData() {
        return cookies != null && !cookies.isEmpty() && platform != null;
    }

    /**
     * 获取认证信息摘要
     *
     * @return 认证信息摘要字符串
     */
    public String getSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("Platform: ").append(platform != null ? platform.getDisplayName() : "Unknown");
        summary.append(", Cookies: ").append(cookies != null ? cookies.size() : 0);
        summary.append(", Created: ").append(createdTime);
        summary.append(", Valid: ").append(isValid);
        summary.append(", Last Validated: ").append(lastValidatedTime != null ? lastValidatedTime : "N/A");
        return summary.toString();
    }
}