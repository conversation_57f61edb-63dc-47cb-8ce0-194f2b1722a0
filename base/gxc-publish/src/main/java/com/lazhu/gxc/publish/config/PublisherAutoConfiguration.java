package com.lazhu.gxc.publish.config;

import com.lazhu.gxc.publish.VideoPublisher;
import com.lazhu.gxc.publish.browser.BrowserManager;
import com.lazhu.gxc.publish.browser.PageInteractionService;
import com.lazhu.gxc.publish.browser.impl.BasicPageInteractionService;
import com.lazhu.gxc.publish.cookie.CookieStorage;
import com.lazhu.gxc.publish.cookie.impl.FileCookieStorage;
import com.lazhu.gxc.publish.service.PlatformServiceFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;

/**
 * 视频发布器自动配置类
 * 当满足以下条件时自动启用：
 * 1. 类路径中存在 VideoPublisher 类
 * 2. 配置属性 gxc.publish.enabled 为 true（默认为 true）
 */
@Slf4j
@AutoConfiguration
@ConditionalOnClass(VideoPublisher.class)
@ConditionalOnProperty(prefix = "publish", name = "enabled", havingValue = "true", matchIfMissing = true)
public class PublisherAutoConfiguration {


    @Bean
    @ConditionalOnMissingBean
    @ConfigurationProperties(prefix = "publish")
    public PublisherConfig publisherConfig() {
        return new PublisherConfig();
    }

    /**
     * 配置Cookie存储服务
     * 默认使用文件存储，可以通过自定义Bean覆盖
     */
    @Bean
    @ConditionalOnMissingBean
    public CookieStorage cookieStorage(PublisherConfig config) {
        log.info("创建 FileCookieStorage Bean，存储路径：{}", config.getCookiePath());
        return new FileCookieStorage(config.getCookiePath());
    }

    /**
     * 配置浏览器管理器
     */
    @Bean
    @ConditionalOnMissingBean
    public BrowserManager browserManager(PublisherConfig config) {
        log.info("创建 BrowserManager Bean，配置：{}", config);
        return new BrowserManager(config);
    }

    /**
     * 配置页面交互服务
     */
    @Bean
    @ConditionalOnMissingBean(PageInteractionService.class)
    public PageInteractionService pageInteractionService() {
        log.info("创建 BasicPageInteractionService Bean");
        return new BasicPageInteractionService();
    }


    /**
     * 配置平台服务工厂
     */
    @Bean
    @ConditionalOnMissingBean
    public PlatformServiceFactory platformServiceFactory(PublisherConfig config, BrowserManager browserManager, PageInteractionService pageInteractionService, CookieStorage cookieStorage) {
        log.info("创建 PlatformServiceFactory Bean，Cookie存储类型: {}", cookieStorage.getStorageType());
        return new PlatformServiceFactory(config, browserManager, pageInteractionService, cookieStorage);
    }

    /**
     * 配置视频发布器（主要服务）
     */
    @Bean
    @ConditionalOnMissingBean
    public VideoPublisher videoPublisher(PublisherConfig config, PlatformServiceFactory platformServiceFactory, BrowserManager browserManager, PageInteractionService pageInteractionService) {
        log.info("创建 VideoPublisher Bean - 视频发布器已就绪");
        return new VideoPublisher(config, platformServiceFactory, browserManager, pageInteractionService);
    }
}