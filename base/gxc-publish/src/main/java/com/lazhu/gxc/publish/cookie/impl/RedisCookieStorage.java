package com.lazhu.gxc.publish.cookie.impl;

import com.lazhu.gxc.publish.cookie.CookieStorage;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * Redis Cookie存储实现（基于Redisson）
 * 将Cookie存储到Redis中，支持过期时间设置和集群模式
 * <p>
 * 注意：此实现需要Redisson依赖，使用时需要在项目中添加redisson依赖
 * 并配置Redisson客户端
 */
@Slf4j
public class RedisCookieStorage implements CookieStorage {

    private final RedissonClient redissonClient;
    private final Duration defaultExpiration;
    private final String keyPrefix;

    /**
     * 构造函数
     *
     * @param redissonClient    Redisson客户端
     * @param defaultExpiration 默认过期时间
     * @param keyPrefix         键前缀
     */
    public RedisCookieStorage(RedissonClient redissonClient, Duration defaultExpiration, String keyPrefix) {
        this.redissonClient = redissonClient;
        this.defaultExpiration = defaultExpiration != null ? defaultExpiration : Duration.ofDays(7);
        this.keyPrefix = keyPrefix != null ? keyPrefix : "gxc:publish:cookies:";

        log.info("Redis Cookie存储初始化 - 过期时间: {}, 键前缀: {}", this.defaultExpiration, this.keyPrefix);
    }

    /**
     * 简化构造函数，使用默认配置
     *
     * @param redissonClient Redisson客户端
     */
    public RedisCookieStorage(RedissonClient redissonClient) {
        this(redissonClient, null, null);
    }

    @Override
    public void saveCookies(String platform, String userId, Map<String, String> cookies) {
        if (platform == null || platform.trim().isEmpty()) {
            throw new IllegalArgumentException("平台标识不能为空");
        }

        if (cookies == null || cookies.isEmpty()) {
            log.warn("尝试保存空的Cookie，平台: {}, 用户: {}", platform, userId);
            return;
        }

        try {
            String key = buildRedisKey(platform, userId);
            RMap<String, String> cookieMap = redissonClient.getMap(key);

            // 清空现有数据并设置新数据
            cookieMap.clear();
            cookieMap.putAll(cookies);

            // 设置过期时间
            cookieMap.expire(defaultExpiration);

            log.info("Cookie保存到Redis成功 - 平台: {}, 用户: {}, 键: {}, 数量: {}",
                    platform, userId, key, cookies.size());

        } catch (Exception e) {
            log.error("保存Cookie到Redis失败 - 平台: {}, 用户: {}", platform, userId, e);
            throw new RuntimeException("保存Cookie到Redis失败", e);
        }
    }

    @Override
    public Map<String, String> loadCookies(String platform, String userId) {
        if (platform == null || platform.trim().isEmpty()) {
            throw new IllegalArgumentException("平台标识不能为空");
        }

        try {
            String key = buildRedisKey(platform, userId);
            RMap<String, String> cookieMap = redissonClient.getMap(key);

            if (!cookieMap.isExists()) {
                log.debug("Redis中不存在Cookie - 平台: {}, 用户: {}, 键: {}", platform, userId, key);
                return null;
            }

            Map<String, String> cookies = new HashMap<>(cookieMap.readAllMap());

            if (cookies.isEmpty()) {
                log.debug("Redis中Cookie为空 - 平台: {}, 用户: {}, 键: {}", platform, userId, key);
                return null;
            }

            log.info("从Redis加载Cookie成功 - 平台: {}, 用户: {}, 数量: {}", platform, userId, cookies.size());
            return cookies;

        } catch (Exception e) {
            log.error("从Redis加载Cookie失败 - 平台: {}, 用户: {}", platform, userId, e);
            return null;
        }
    }

    @Override
    public boolean deleteCookies(String platform, String userId) {
        if (platform == null || platform.trim().isEmpty()) {
            throw new IllegalArgumentException("平台标识不能为空");
        }

        try {
            String key = buildRedisKey(platform, userId);
            RMap<String, String> cookieMap = redissonClient.getMap(key);

            boolean existed = cookieMap.isExists();
            if (existed) {
                boolean deleted = cookieMap.delete();
                if (deleted) {
                    log.info("从Redis删除Cookie成功 - 平台: {}, 用户: {}", platform, userId);
                    return true;
                } else {
                    log.warn("从Redis删除Cookie失败 - 平台: {}, 用户: {}", platform, userId);
                    return false;
                }
            } else {
                log.debug("Redis中不存在Cookie，无需删除 - 平台: {}, 用户: {}", platform, userId);
                return false;
            }

        } catch (Exception e) {
            log.error("从Redis删除Cookie时发生异常 - 平台: {}, 用户: {}", platform, userId, e);
            return false;
        }
    }

    @Override
    public boolean existsCookies(String platform, String userId) {
        if (platform == null || platform.trim().isEmpty()) {
            return false;
        }

        try {
            String key = buildRedisKey(platform, userId);
            RMap<String, String> cookieMap = redissonClient.getMap(key);

            return cookieMap.isExists() && !cookieMap.isEmpty();

        } catch (Exception e) {
            log.error("检查Redis中Cookie是否存在时发生异常 - 平台: {}, 用户: {}", platform, userId, e);
            return false;
        }
    }

    @Override
    public int clearExpiredCookies() {
        try {
            // Redisson会自动清理过期的键，但我们也可以手动清理一些过期的键
            Iterable<String> keys = redissonClient.getKeys().getKeysByPattern(keyPrefix + "*");
            int expiredCount = 0;

            for (String key : keys) {
                RMap<String, String> cookieMap = redissonClient.getMap(key);
                if (!cookieMap.isExists()) {
                    expiredCount++;
                }
            }

            log.info("Redis过期Cookie清理完成，清理数量: {}", expiredCount);
            return expiredCount;

        } catch (Exception e) {
            log.error("清理Redis过期Cookie时发生异常", e);
            return 0;
        }
    }

    @Override
    public String getStorageType() {
        return "redis-redisson";
    }

    /**
     * 构建Redis键
     */
    private String buildRedisKey(String platform, String userId) {
        StringBuilder keyBuilder = new StringBuilder(keyPrefix);
        keyBuilder.append(platform);

        if (userId != null && !userId.trim().isEmpty()) {
            keyBuilder.append(":").append(userId);
        }

        return keyBuilder.toString();
    }

    /**
     * 批量保存Cookie（优化版本）
     *
     * @param cookieMap 平台用户到Cookie的映射 (格式: "platform:userId" -> cookies)
     */
    public void batchSaveCookies(Map<String, Map<String, String>> cookieMap) {
        if (cookieMap == null || cookieMap.isEmpty()) {
            return;
        }

        try {
            // 使用批量操作提高性能
            for (Map.Entry<String, Map<String, String>> entry : cookieMap.entrySet()) {
                String[] keyParts = entry.getKey().split(":", 2);
                String platform = keyParts[0];
                String userId = keyParts.length > 1 ? keyParts[1] : null;

                saveCookies(platform, userId, entry.getValue());
            }

            log.info("批量保存Cookie完成，保存数量: {}", cookieMap.size());

        } catch (Exception e) {
            log.error("批量保存Cookie失败", e);
            throw new RuntimeException("批量保存Cookie失败", e);
        }
    }

    /**
     * 批量加载Cookie（优化版本）
     *
     * @param platformUserIds 平台用户ID列表 (格式: "platform:userId")
     * @return 平台用户ID到Cookie的映射
     */
    public Map<String, Map<String, String>> batchLoadCookies(Set<String> platformUserIds) {
        if (platformUserIds == null || platformUserIds.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, Map<String, String>> result = new HashMap<>();

        try {
            for (String platformUserId : platformUserIds) {
                String[] keyParts = platformUserId.split(":", 2);
                String platform = keyParts[0];
                String userId = keyParts.length > 1 ? keyParts[1] : null;

                Map<String, String> cookies = loadCookies(platform, userId);
                if (cookies != null) {
                    result.put(platformUserId, cookies);
                }
            }

            log.info("批量加载Cookie完成，加载数量: {}/{}", result.size(), platformUserIds.size());
            return result;

        } catch (Exception e) {
            log.error("批量加载Cookie失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 设置Cookie过期时间
     *
     * @param platform 平台标识
     * @param userId   用户ID
     * @param duration 过期时间
     */
    public boolean setCookieExpiration(String platform, String userId, Duration duration) {
        try {
            String key = buildRedisKey(platform, userId);
            RMap<String, String> cookieMap = redissonClient.getMap(key);

            if (cookieMap.isExists()) {
                boolean success = cookieMap.expire(duration);
                log.info("设置Cookie过期时间 - 平台: {}, 用户: {}, 过期时间: {}, 结果: {}",
                        platform, userId, duration, success ? "成功" : "失败");
                return success;
            } else {
                log.warn("Cookie不存在，无法设置过期时间 - 平台: {}, 用户: {}", platform, userId);
                return false;
            }

        } catch (Exception e) {
            log.error("设置Cookie过期时间失败 - 平台: {}, 用户: {}", platform, userId, e);
            return false;
        }
    }

    /**
     * 获取Cookie剩余过期时间
     *
     * @param platform 平台标识
     * @param userId   用户ID
     * @return 剩余过期时间（毫秒），-1表示永不过期，-2表示不存在
     */
    public long getCookieRemainingTime(String platform, String userId) {
        try {
            String key = buildRedisKey(platform, userId);
            RMap<String, String> cookieMap = redissonClient.getMap(key);

            return cookieMap.remainTimeToLive();

        } catch (Exception e) {
            log.error("获取Cookie剩余过期时间失败 - 平台: {}, 用户: {}", platform, userId, e);
            return -2;
        }
    }
}