package com.lazhu.gxc.publish.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * 数据统计请求
 * 用于请求平台数据统计信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StatisticRequest {
    
    /**
     * 平台类型
     */
    private PlatformType platformType;
    
    /**
     * 开始日期（包含）
     */
    private LocalDate startDate;
    
    /**
     * 结束日期（包含）
     */
    private LocalDate endDate;
    
    /**
     * 账号标识（可选，用于多账号场景）
     */
    private String userId;
    
    /**
     * 验证请求参数
     */
    public boolean isValid() {
        return platformType != null && startDate != null && endDate != null && !startDate.isAfter(endDate);
    }
}