package com.lazhu.gxc.publish.service;

import com.lazhu.gxc.publish.model.PlatformType;
import com.lazhu.gxc.publish.model.StatisticRequest;
import com.lazhu.gxc.publish.model.StatisticResult;

/**
 * 数据统计服务接口
 * 定义平台数据统计的标准接口
 */
public interface DataStatisticService {
    
    /**
     * 获取关注者统计数据
     * 
     * @param request 统计请求参数
     * @return 统计结果
     */
    StatisticResult getFollowerStatistics(StatisticRequest request);
    
    /**
     * 获取支持的平台类型
     * 
     * @return 平台类型
     */
    PlatformType getSupportedPlatform();

}