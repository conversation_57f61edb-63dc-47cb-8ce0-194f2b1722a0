package com.lazhu.gxc.publish.model;

import lombok.Data;

import java.io.Serializable;

/**
 * 登录会话信息
 * 用于管理前端登录流程中的浏览器会话
 */
@Data
public class LoginSession implements Serializable {

    /**
     * 平台
     */
    private final PlatformType platform;
    /**
     * 会话ID
     */
    private final String sessionId;

    /**
     * 用户ID
     */
    private final String userId;

    /**
     * 浏览器实例ID
     */
    private final String browserId;

    /**
     * 浏览器上下文ID
     */
    private final String contextId;

    /**
     * 二维码图片URL
     */
    private final String qrCodeUrl;

    /**
     * 错误消息（如果有）
     */
    private final String errorMessage;

    /**
     * 会话创建时间
     */
    private final long createTime;

    /**
     * 成功创建会话的构造函数
     */
    public LoginSession(PlatformType platform, String sessionId, String userId, String browserId, String contextId, String qrCodeUrl) {
        this.platform = platform;
        this.sessionId = sessionId;
        this.userId = userId;
        this.browserId = browserId;
        this.contextId = contextId;
        this.qrCodeUrl = qrCodeUrl;
        this.errorMessage = null;
        this.createTime = System.currentTimeMillis();
    }

    /**
     * 失败创建会话的构造函数
     */
    public LoginSession(PlatformType platform, String sessionId, String userId, String qrCodeUrl, String errorMessage) {
        this.platform = platform;
        this.sessionId = sessionId;
        this.userId = userId;
        this.browserId = null;
        this.contextId = null;
        this.qrCodeUrl = qrCodeUrl;
        this.errorMessage = errorMessage;
        this.createTime = System.currentTimeMillis();
    }

    /**
     * 检查会话是否成功创建
     */
    public boolean isSuccess() {
        return errorMessage == null && qrCodeUrl != null;
    }

    /**
     * 检查会话是否已过期
     *
     * @param timeoutMs 超时时间（毫秒）
     * @return 是否已过期
     */
    public boolean isExpired(long timeoutMs) {
        return System.currentTimeMillis() - createTime > timeoutMs;
    }

    @Override
    public String toString() {
        return String.format("LoginSession{sessionId='%s', userId='%s', qrCodeUrl='%s', success=%s}", sessionId, userId, qrCodeUrl != null ? "有" : "无", isSuccess());
    }
}