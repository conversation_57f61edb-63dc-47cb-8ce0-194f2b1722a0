package com.lazhu.gxc.publish.service;

import com.lazhu.gxc.publish.browser.BrowserManager;
import com.lazhu.gxc.publish.browser.PageInteractionService;
import com.lazhu.gxc.publish.config.PublisherConfig;
import com.lazhu.gxc.publish.cookie.CookieStorage;
import com.lazhu.gxc.publish.model.PlatformType;
import com.lazhu.gxc.publish.platform.wechat.WeChatDataStatisticService;
import com.lazhu.gxc.publish.platform.wechat.WeChatVideoAuthService;
import com.lazhu.gxc.publish.platform.wechat.WeChatVideoUploadService;
import com.lazhu.gxc.publish.platform.wechat.WeChatVideoValidationService;
import com.lazhu.gxc.publish.service.AuthService;
import com.lazhu.gxc.publish.service.DataStatisticService;
import com.lazhu.gxc.publish.service.VideoUploadService;
import com.lazhu.gxc.publish.service.VideoValidationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 平台服务工厂类
 * 根据平台类型创建相应的服务实例，实现服务实例的缓存和管理
 */
public class PlatformServiceFactory {

    private static final Logger logger = LoggerFactory.getLogger(PlatformServiceFactory.class);

    private final PublisherConfig config;
    private final BrowserManager browserManager;
    private final PageInteractionService pageInteractionService;
    private final CookieStorage cookieStorage;

    // 服务实例缓存
    private final Map<PlatformType, VideoUploadService> serviceCache = new ConcurrentHashMap<>();
    private final Map<PlatformType, AuthService> authServiceCache = new ConcurrentHashMap<>();
    private final Map<PlatformType, VideoValidationService> validationServiceCache = new ConcurrentHashMap<>();
    private final Map<PlatformType, DataStatisticService> statisticServiceCache = new ConcurrentHashMap<>();


    /**
     * 构造函数（完整版本）
     *
     * @param config                 发布器配置
     * @param browserManager         浏览器管理器
     * @param pageInteractionService 页面交互服务
     * @param cookieStorage          Cookie存储服务
     */
    public PlatformServiceFactory(PublisherConfig config,
                                  BrowserManager browserManager,
                                  PageInteractionService pageInteractionService,
                                  CookieStorage cookieStorage) {
        this.config = config;
        this.browserManager = browserManager;
        this.pageInteractionService = pageInteractionService;
        this.cookieStorage = cookieStorage;

        logger.info("发布服务工厂初始化完成 - Cookie存储类型: {}", cookieStorage != null ? cookieStorage.getStorageType() : "null");
    }

    /**
     * 获取视频上传服务实例
     *
     * @param platformType 平台类型
     * @return 视频上传服务实例
     * @throws UnsupportedOperationException 如果平台不支持
     */
    public VideoUploadService getVideoUploadService(PlatformType platformType) {
        if (platformType == null) {
            throw new IllegalArgumentException("平台类型不能为空");
        }

        return serviceCache.computeIfAbsent(platformType, this::createVideoUploadService);
    }

    /**
     * 获取认证服务实例
     *
     * @param platformType 平台类型
     * @return 认证服务实例
     * @throws UnsupportedOperationException 如果平台不支持
     */
    public AuthService getAuthService(PlatformType platformType) {
        if (platformType == null) {
            throw new IllegalArgumentException("平台类型不能为空");
        }

        return authServiceCache.computeIfAbsent(platformType, this::createAuthService);
    }

    /**
     * 获取视频验证服务实例
     *
     * @param platformType 平台类型
     * @return 视频验证服务实例
     */
    public VideoValidationService getVideoValidationService(PlatformType platformType) {
        if (platformType == null) {
            throw new IllegalArgumentException("平台类型不能为空");
        }

        return validationServiceCache.computeIfAbsent(platformType, this::createVideoValidationService);
    }

    /**
     * 检查平台是否支持
     *
     * @param platformType 平台类型
     * @return 支持状态
     */
    public boolean isPlatformSupported(PlatformType platformType) {
        try {
            return Arrays.asList(getSupportedPlatforms()).contains(platformType);
        } catch (Exception e) {
            logger.error("检查平台支持状态时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取所有支持的平台类型
     *
     * @return 支持的平台类型数组
     */
    public PlatformType[] getSupportedPlatforms() {
        // 目前支持微信视频号和通用平台，后续可扩展
        return new PlatformType[]{PlatformType.WECHAT_VIDEO};
    }

    /**
     * 获取数据统计服务实例
     *
     * @param platformType 平台类型
     * @return 数据统计服务实例
     * @throws UnsupportedOperationException 如果平台不支持
     */
    public DataStatisticService getDataStatisticService(PlatformType platformType) {
        if (platformType == null) {
            throw new IllegalArgumentException("平台类型不能为空");
        }

        return statisticServiceCache.computeIfAbsent(platformType, this::createDataStatisticService);
    }

    /**
     * 清除指定平台的服务缓存
     *
     * @param platformType 平台类型
     */
    public void clearCache(PlatformType platformType) {
        if (platformType != null) {
            serviceCache.remove(platformType);
            authServiceCache.remove(platformType);
            validationServiceCache.remove(platformType);
            statisticServiceCache.remove(platformType);
            logger.info("已清除平台 {} 的服务缓存", platformType.getDisplayName());
        }
    }

    /**
     * 清除所有服务缓存
     */
    public void clearAllCache() {
        serviceCache.clear();
        authServiceCache.clear();
        validationServiceCache.clear();
        statisticServiceCache.clear();
        logger.info("已清除所有平台的服务缓存");
    }

    /**
     * 获取缓存状态信息
     *
     * @return 缓存状态描述
     */
    public String getCacheStatus() {
        StringBuilder status = new StringBuilder();
        status.append("服务缓存状态:\n");
        status.append("- 视频上传服务缓存数量: ").append(serviceCache.size()).append("\n");
        status.append("- 认证服务缓存数量: ").append(authServiceCache.size()).append("\n");
        status.append("- 视频验证服务缓存数量: ").append(validationServiceCache.size()).append("\n");
        status.append("- 数据统计服务缓存数量: ").append(statisticServiceCache.size()).append("\n");
        status.append("- 缓存的平台: ");

        serviceCache.keySet().forEach(platform ->
                status.append(platform.getDisplayName()).append(" "));

        return status.toString();
    }


    /**
     * 创建视频上传服务实例
     *
     * @param platformType 平台类型
     * @return 视频上传服务实例
     */
    private VideoUploadService createVideoUploadService(PlatformType platformType) {
        logger.info("创建平台 {} 的视频上传服务实例", platformType.getDisplayName());

        if (platformType == PlatformType.WECHAT_VIDEO) {
            return createWeChatUploadService();
        }
        throw new UnsupportedOperationException("暂不支持");
    }

    /**
     * 创建认证服务实例
     *
     * @param platformType 平台类型
     * @return 认证服务实例
     */
    private AuthService createAuthService(PlatformType platformType) {
        logger.info("创建平台 {} 的认证服务实例", platformType.getDisplayName());
        if (platformType == PlatformType.WECHAT_VIDEO) {
            return createWeChatAuthService();
        }
        throw new UnsupportedOperationException("暂不支持");
    }

    /**
     * 创建视频验证服务实例
     *
     * @param platformType 平台类型
     * @return 视频验证服务实例
     */
    private VideoValidationService createVideoValidationService(PlatformType platformType) {
        logger.info("创建平台 {} 的视频验证服务实例", platformType.getDisplayName());
        if (platformType == PlatformType.WECHAT_VIDEO) {
            return new WeChatVideoValidationService();
        }
        throw new UnsupportedOperationException("暂不支持");
    }

    /**
     * 创建微信视频号上传服务
     *
     * @return 微信上传服务实例
     */
    private VideoUploadService createWeChatUploadService() {
        AuthService weChatAuthService = createWeChatAuthService();
        VideoValidationService weChatValidationService = getVideoValidationService(PlatformType.WECHAT_VIDEO);
        return new WeChatVideoUploadService(config, weChatAuthService, weChatValidationService, browserManager, pageInteractionService);
    }

    /**
     * 创建微信认证服务
     *
     * @return 微信认证服务实例
     */
    private AuthService createWeChatAuthService() {
        return new WeChatVideoAuthService(cookieStorage, browserManager);
    }

    /**
     * 创建数据统计服务实例
     *
     * @param platformType 平台类型
     * @return 数据统计服务实例
     */
    private DataStatisticService createDataStatisticService(PlatformType platformType) {
        logger.info("创建平台 {} 的数据统计服务实例", platformType.getDisplayName());
        
        if (platformType == PlatformType.WECHAT_VIDEO) {
            AuthService weChatAuthService = createWeChatAuthService();
            return new WeChatDataStatisticService(browserManager, pageInteractionService,weChatAuthService);
        }
        throw new UnsupportedOperationException("暂不支持该平台的数据统计服务: " + platformType.getDisplayName());
    }

    /**
     * 销毁工厂，清理资源
     */
    public void destroy() {
        logger.info("销毁平台服务工厂");

        // 清理缓存
        clearAllCache();

        // 这里可以添加其他清理逻辑，比如关闭连接池等
        logger.info("平台服务工厂销毁完成");
    }
}