package com.lazhu.gxc.publish.platform.wechat;

import com.lazhu.gxc.publish.browser.PageInteractionService;
import com.lazhu.gxc.publish.model.FollowerData;
import com.lazhu.gxc.publish.model.StatisticRequest;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.options.WaitForSelectorState;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 微信视频号统计页面交互类
 * 处理微信视频号统计页面的具体交互逻辑
 */
public class WeChatStatisticPageInteraction {

    private static final Logger logger = LoggerFactory.getLogger(WeChatStatisticPageInteraction.class);

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private final PageInteractionService pageInteractionService;

    public WeChatStatisticPageInteraction(PageInteractionService pageInteractionService) {
        this.pageInteractionService = pageInteractionService;
    }

    /**
     * 等待页面加载完成
     */
    public void waitForPageLoad(Page page) {
        try {
            // 等待统计表格加载 - 基于实际页面分析的选择器
            page.waitForSelector("table", new Page.WaitForSelectorOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(30000));

            // 等待数据加载完成 - 给Ajax请求足够时间
            Thread.sleep(3000);

            logger.info("统计页面加载完成");
        } catch (Exception e) {
            logger.warn("等待页面加载超时，尝试继续执行: {}", e.getMessage());
        }
    }

    /**
     * 设置日期范围
     * 基于实际页面分析的日期筛选组件
     */
    public void setDateRange(Page page, LocalDate startDate, LocalDate endDate) {
        try {
            logger.info("设置日期范围: {} 至 {}", startDate, endDate);

            // 检查是否需要设置自定义日期范围
            long daysBetween = java.time.temporal.ChronoUnit.DAYS.between(startDate, endDate);

            if (daysBetween <= 7) {
                // 使用近7天选项
                logger.info("使用近7天选项");
                String near7DaysSelector = "input[type='radio'][value='近7天'], input[type='radio'] + *:contains('近7天')";
                try {
                    page.locator(near7DaysSelector).first().click();
                } catch (Exception e) {
                    logger.warn("点击近7天选项失败，将使用默认设置");
                }
            } else if (daysBetween <= 30) {
                // 使用近30天选项
                logger.info("使用近30天选项");
                String near30DaysSelector = "input[type='radio'][value='近30天'], input[type='radio'] + *:contains('近30天')";
                try {
                    page.locator(near30DaysSelector).first().click();
                } catch (Exception e) {
                    logger.warn("点击近30天选项失败，将使用默认设置");
                }
            } else {
                // 使用自定义日期范围
                logger.info("使用自定义日期范围");
                String customRadioSelector = "input[type='radio'][value='自定义'], input[type='radio'] + *:contains('自定义')";
                try {
                    page.locator(customRadioSelector).first().click();
                    Thread.sleep(1000);

                    // 这里可以添加自定义日期输入的逻辑
                    logger.warn("自定义日期范围功能待实现");
                } catch (Exception e) {
                    logger.warn("设置自定义日期范围失败: {}", e.getMessage());
                }
            }

            // 等待页面更新
            Thread.sleep(2000);
            logger.info("日期范围设置完成");
        } catch (Exception e) {
            logger.error("设置日期范围失败", e);
            throw new RuntimeException("设置日期范围失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从表格获取关注者数据
     * 基于实际页面分析的表格结构
     */
    public List<FollowerData> getFollowerDataFromTable(Page page, StatisticRequest request) {
        List<FollowerData> dataList = new ArrayList<>();

        try {
            // 等待表格数据加载完成
            Thread.sleep(3000);

            // 基于实际页面分析的表格选择器
            String tableSelector = "table";
            String dataRowsSelector = "table rowgroup:last-child row";

            // 等待表格出现
            page.waitForSelector(tableSelector, new Page.WaitForSelectorOptions().setTimeout(10000));

            // 获取数据行 (排除表头)
            var rows = page.locator(dataRowsSelector);
            int rowCount = rows.count();

            logger.info("找到 {} 行数据", rowCount);

            for (int i = 0; i < rowCount; i++) {
                try {
                    FollowerData data = parseTableRow(rows.nth(i), request);
                    if (data != null) {
                        dataList.add(data);
                    }
                } catch (Exception e) {
                    logger.warn("解析第 {} 行数据失败: {}", i + 1, e.getMessage());
                }
            }

            logger.info("成功解析 {} 条关注者数据", dataList.size());
            return dataList;

        } catch (Exception e) {
            logger.error("获取表格数据失败", e);
            throw new RuntimeException("获取表格数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析表格行数据
     * 基于实际页面分析的表格结构：时间、净增关注、新增关注、取消关注、关注者总数
     */
    private FollowerData parseTableRow(Locator row, StatisticRequest request) {
        try {
            // 基于实际页面分析的列结构
            String dateSelector = "cell:nth-child(1)";
            String netFollowersSelector = "cell:nth-child(2)";
            String newFollowersSelector = "cell:nth-child(3)";
            String unfollowedSelector = "cell:nth-child(4)";
            String totalFollowersSelector = "cell:nth-child(5)";

            String dateText = row.locator(dateSelector).textContent().trim();
            String netFollowersText = row.locator(netFollowersSelector).textContent().trim();
            String newFollowersText = row.locator(newFollowersSelector).textContent().trim();
            String unfollowedText = row.locator(unfollowedSelector).textContent().trim();
            String totalFollowersText = row.locator(totalFollowersSelector).textContent().trim();

            logger.debug("解析行数据: 日期={}, 净增={}, 新增={}, 取消={}, 总数={}",
                        dateText, netFollowersText, newFollowersText, unfollowedText, totalFollowersText);

            // 解析日期 - 处理 "2025/09/07" 格式
            LocalDate date = parseDate(dateText);

            // 解析数字
            Integer newFollowers = parseNumber(newFollowersText);
            Integer unfollowed = parseNumber(unfollowedText);
            Integer totalFollowers = parseNumber(totalFollowersText);
            Integer netFollowers = parseNumber(netFollowersText);

            FollowerData data = FollowerData.builder()
                    .date(date)
                    .newFollowers(newFollowers)
                    .unfollowed(unfollowed)
                    .netFollowers(netFollowers)
                    .totalFollowers(totalFollowers)
                    .platformType(request.getPlatformType())
                    .build();

            // 验证数据合理性
            if (!validateFollowerData(data)) {
                logger.warn("数据验证失败，跳过该行: {}", dateText);
                return null;
            }

            return data;

        } catch (Exception e) {
            logger.warn("解析行数据失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 解析日期字符串，支持多种格式
     */
    private LocalDate parseDate(String dateText) {
        try {
            // 处理 "2025/09/07" 格式
            if (dateText.contains("/")) {
                DateTimeFormatter slashFormatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
                return LocalDate.parse(dateText, slashFormatter);
            }
            // 处理 "2025-09-07" 格式
            return LocalDate.parse(dateText, DATE_FORMATTER);
        } catch (Exception e) {
            logger.warn("解析日期失败: {}", dateText);
            throw new RuntimeException("无法解析日期: " + dateText, e);
        }
    }

    /**
     * 解析数字字符串，支持数据验证和清洗
     */
    private Integer parseNumber(String text) {
        if (text == null || text.trim().isEmpty()) {
            return 0; // 空值默认为0
        }

        try {
            String originalText = text.trim();

            // 处理特殊显示值
            if ("—".equals(originalText) || "——".equals(originalText) || "- -".equals(originalText) || "— —".equals(originalText)) {
                return 0;
            }

            // 移除千分位符号、空格、中文字符等，但保留负号
            String cleanText = originalText
                    .replaceAll("[\\s,，]", "")  // 移除空格和逗号
                    .replaceAll("[^0-9-]", "");  // 只保留数字和负号

            if (cleanText.isEmpty()) {
                logger.warn("清洗后的数字为空: {}", originalText);
                return 0;
            }

            // 验证数字范围（关注者数据通常不会超过这个范围）
            int result = Integer.parseInt(cleanText);
            if (Math.abs(result) > 100_000_000) { // 1亿
                logger.warn("数字超出合理范围: {} -> {}", originalText, result);
                return 0;
            }

            return result;

        } catch (NumberFormatException e) {
            logger.warn("解析数字失败: {} -> {}", text, e.getMessage());
            return 0; // 解析失败默认为0
        }
    }

    /**
     * 验证关注者数据的合理性
     */
    private boolean validateFollowerData(FollowerData data) {
        if (data == null || data.getDate() == null) {
            return false;
        }

        // 验证日期范围（不能是未来日期）
        if (data.getDate().isAfter(LocalDate.now())) {
            logger.warn("数据日期不能是未来: {}", data.getDate());
            return false;
        }

        // 验证数据逻辑一致性
        if (data.getNewFollowers() != null && data.getUnfollowed() != null && data.getNetFollowers() != null) {
            int calculatedNet = data.getNewFollowers() - data.getUnfollowed();
            if (Math.abs(calculatedNet - data.getNetFollowers()) > 1) { // 允许1的误差
                logger.warn("净增关注数据不一致: 新增={}, 取消={}, 净增={}, 计算值={}",
                           data.getNewFollowers(), data.getUnfollowed(), data.getNetFollowers(), calculatedNet);
            }
        }

        // 验证关注者总数的合理性
        if (data.getTotalFollowers() != null && data.getTotalFollowers() < 0) {
            logger.warn("关注者总数不能为负数: {}", data.getTotalFollowers());
            return false;
        }

        return true;
    }
}