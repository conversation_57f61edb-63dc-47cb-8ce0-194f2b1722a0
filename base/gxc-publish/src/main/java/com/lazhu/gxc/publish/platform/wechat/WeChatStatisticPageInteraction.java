package com.lazhu.gxc.publish.platform.wechat;

import com.lazhu.gxc.publish.browser.PageInteractionService;
import com.lazhu.gxc.publish.model.FollowerData;
import com.lazhu.gxc.publish.model.StatisticRequest;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.options.WaitForSelectorState;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 微信视频号统计页面交互类
 * 处理微信视频号统计页面的具体交互逻辑
 */
public class WeChatStatisticPageInteraction {

    private static final Logger logger = LoggerFactory.getLogger(WeChatStatisticPageInteraction.class);

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private final PageInteractionService pageInteractionService;

    public WeChatStatisticPageInteraction(PageInteractionService pageInteractionService) {
        this.pageInteractionService = pageInteractionService;
    }

    /**
     * 等待页面加载完成
     */
    public void waitForPageLoad(Page page) {
        try {
            // 等待统计表格加载
            page.waitForSelector(".follower-growth-detail-wrap", new Page.WaitForSelectorOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(30000));

            logger.info("统计页面加载完成");
        } catch (Exception e) {
            logger.warn("等待页面加载超时，尝试继续执行");
        }
    }

    /**
     * 设置日期范围
     * 支持微信公众号后台的日期筛选组件
     */
    public void setDateRange(Page page, LocalDate startDate, LocalDate endDate) {
        try {
            logger.info("设置日期范围: {} 至 {}", startDate, endDate);

            // 点击"自定义"单选按钮
            String customRadioSelector = ".filter-content input[type='radio'][value='3']";
            pageInteractionService.clickElement(page, customRadioSelector, Duration.ofSeconds(10));

            // 等待自定义日期选择器出现
            Thread.sleep(1000);

            String dateInputSelector = ".data-range-choose input[placeholder*='开始日期'],.data-range-choose input[placeholder*='结束日期']";

            // 获取所有日期输入框
            var dateInputs = page.locator(dateInputSelector);
            if (dateInputs.count() >= 2) {
                // 设置开始日期
                dateInputs.nth(0).fill(startDate.format(DATE_FORMATTER));
                // 设置结束日期
                dateInputs.nth(1).fill(endDate.format(DATE_FORMATTER));
            }
            logger.info("日期范围设置完成");
        } catch (Exception e) {
            logger.error("设置日期范围失败", e);
            throw new RuntimeException("设置日期范围失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从表格获取关注者数据
     */
    public List<FollowerData> getFollowerDataFromTable(Page page, StatisticRequest request) {
        List<FollowerData> dataList = new ArrayList<>();

        try {
            // 等待表格数据加载
            Thread.sleep(3000);

            // 获取表格行

            String tableRowSelector = ".ant-table-content tbody tr";
            var rows = page.locator(tableRowSelector);
            int rowCount = rows.count();

            logger.info("找到 {} 行数据", rowCount);

            for (int i = 0; i < rowCount; i++) {
                try {
                    FollowerData data = parseTableRow(rows.nth(i), request);
                    if (data != null) {
                        dataList.add(data);
                    }
                } catch (Exception e) {
                    logger.warn("解析第 {} 行数据失败: {}", i + 1, e.getMessage());
                }
            }

            logger.info("成功解析 {} 条关注者数据", dataList.size());
            return dataList;

        } catch (Exception e) {
            logger.error("获取表格数据失败", e);
            throw new RuntimeException("获取表格数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析表格行数据
     */
    private FollowerData parseTableRow(Locator row, StatisticRequest request) {
        try {
            // 尝试不同的列选择器
            String dateSelector = "td:nth-child(1)";
            String netFollowersSelector = "td:nth-child(2)";
            String newFollowersSelector = "td:nth-child(3)";
            String unfollowedSelector = "td:nth-child(4)";
            String totalFollowersSelector = "td:nth-child(5)";

            String dateText = row.locator(dateSelector).textContent().trim();
            String netFollowersText = row.locator(netFollowersSelector).textContent().trim();
            String newFollowersText = row.locator(newFollowersSelector).textContent().trim();
            String unfollowedText = row.locator(unfollowedSelector).textContent().trim();
            String totalFollowersText = row.locator(totalFollowersSelector).textContent().trim();

            // 解析日期
            LocalDate date = LocalDate.parse(dateText, DATE_FORMATTER);

            // 解析数字（移除千分位符号等）
            Integer newFollowers = parseNumber(newFollowersText);
            Integer unfollowed = parseNumber(unfollowedText);
            Integer totalFollowers = parseNumber(totalFollowersText);
            Integer netFollowers = parseNumber(netFollowersText);

            return FollowerData.builder()
                    .date(date)
                    .newFollowers(newFollowers)
                    .unfollowed(unfollowed)
                    .netFollowers(netFollowers)
                    .totalFollowers(totalFollowers)
                    .platformType(request.getPlatformType())
                    .build();

        } catch (Exception e) {
            logger.warn("解析行数据失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 解析数字字符串
     */
    private Integer parseNumber(String text) {
        if (text == null || text.trim().isEmpty()) {
            return null;
        }
        try {
            // 移除千分位符号、空格、中文字符等
            String cleanText = text.replaceAll("[\\s,，千kK万wW]|\\s+", "").replaceAll("[^0-9-]", "");

            if (cleanText.isEmpty()) {
                return null;
            }
            return Integer.parseInt(cleanText);
        } catch (NumberFormatException e) {
            logger.warn("解析数字失败: {}", text);
            return null;
        }
    }
}