package com.lazhu.gxc.publish.browser;

import com.lazhu.gxc.publish.config.PublisherConfig;
import com.microsoft.playwright.Browser;
import com.microsoft.playwright.BrowserContext;
import com.microsoft.playwright.BrowserType;
import com.microsoft.playwright.Playwright;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 浏览器管理器
 * 使用Java Playwright创建和管理浏览器实例
 * 实现浏览器上下文的创建和销毁，添加资源清理和异常处理
 */
public class BrowserManager {

    private static final Logger logger = LoggerFactory.getLogger(BrowserManager.class);

    private final PublisherConfig config;
    private final Playwright playwright;
    private final ConcurrentHashMap<String, Browser> browsers;
    private final ConcurrentHashMap<String, BrowserContext> contexts;
    private final AtomicBoolean isShutdown;

    public BrowserManager(PublisherConfig config) {
        this.config = config;
        this.playwright = Playwright.create();
        this.browsers = new ConcurrentHashMap<>();
        this.contexts = new ConcurrentHashMap<>();
        this.isShutdown = new AtomicBoolean(false);

        // 添加JVM关闭钩子，确保资源清理
        Runtime.getRuntime().addShutdownHook(new Thread(this::shutdown));

        logger.info("BrowserManager 初始化: headless={}, timeout={}ms", config.getHeadless(), config.getBrowserTimeout());
    }

    /**
     * 创建浏览器实例（支持自定义选项）
     *
     * @param browserId 浏览器实例ID
     * @param headless  是否为无头模式
     * @return Browser实例
     * @throws BrowserException 浏览器创建失败时抛出
     */
    public Browser createBrowser(String browserId, boolean headless) throws BrowserException {
        if (isShutdown.get()) {
            throw new BrowserException("BrowserManager已关闭，无法创建新的浏览器实例");
        }

        try {
            logger.debug("创建浏览器实例 ID: {}, headless: {}", browserId, headless);

            BrowserType.LaunchOptions launchOptions = new BrowserType.LaunchOptions()
                    .setHeadless(headless)
                    .setTimeout(config.getBrowserTimeout());

            // 如果配置了Chrome路径，使用指定的Chrome
            if (config.getChromePath() != null && !config.getChromePath().trim().isEmpty()) {
                launchOptions.setExecutablePath(Paths.get(config.getChromePath()));
                logger.debug("Using Chrome executable at: {}", config.getChromePath());
            }

            // 对于非无头模式，添加一些额外的启动参数
            if (!headless) {
                launchOptions.setArgs(java.util.List.of("--lang=en-GB"));
            }

            Browser browser = playwright.chromium().launch(launchOptions);
            browsers.put(browserId, browser);

            logger.info("浏览器实例成功  ID: {}, headless: {}", browserId, headless);
            return browser;

        } catch (Exception e) {
            logger.error("浏览器实例失败 ID: {}", browserId, e);
            throw new BrowserException("创建浏览器实例失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建浏览器实例（使用配置中的默认headless设置）
     *
     * @param browserId 浏览器实例ID
     * @return Browser实例
     * @throws BrowserException 浏览器创建失败时抛出
     */
    public Browser createBrowser(String browserId) throws BrowserException {
        return createBrowser(browserId, config.getHeadless());
    }

    /**
     * 创建浏览器上下文
     *
     * @param browserId      浏览器实例ID
     * @param contextId      上下文ID
     * @param cookieFilePath Cookie文件路径（可选）
     * @return BrowserContext实例
     * @throws BrowserException 上下文创建失败时抛出
     */
    public BrowserContext createContext(String browserId, String contextId, String cookieFilePath)
            throws BrowserException {

        Browser browser = browsers.get(browserId);
        if (browser == null) {
            throw new BrowserException("浏览器实例不存在: " + browserId);
        }

        if (!browser.isConnected()) {
            throw new BrowserException("浏览器实例已断开连接: " + browserId);
        }

        try {
            logger.debug("Creating browser context with ID: {} for browser: {}", contextId, browserId);

            Browser.NewContextOptions contextOptions = new Browser.NewContextOptions()
                    .setViewportSize(1920, 1080)
                    .setUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");

            // 如果提供了Cookie文件路径，加载Cookie状态
            if (cookieFilePath != null && !cookieFilePath.trim().isEmpty()) {
                Path cookiePath = Paths.get(cookieFilePath);
                if (cookiePath.toFile().exists()) {
                    contextOptions.setStorageStatePath(cookiePath);
                    logger.debug("Loading cookies from: {}", cookieFilePath);
                }
            }

            BrowserContext context = browser.newContext(contextOptions);
            contexts.put(contextId, context);

            logger.info("Browser context created successfully with ID: {}", contextId);
            return context;

        } catch (Exception e) {
            logger.error("Failed to create browser context with ID: {}", contextId, e);
            throw new BrowserException("创建浏览器上下文失败: " + e.getMessage(), e);
        }
    }

    /**
     * 保存浏览器上下文状态（包括Cookie）
     *
     * @param contextId      上下文ID
     * @param cookieFilePath Cookie文件保存路径
     * @throws BrowserException 保存失败时抛出
     */
    public void saveContextState(String contextId, String cookieFilePath) throws BrowserException {
        BrowserContext context = contexts.get(contextId);
        if (context == null) {
            throw new BrowserException("浏览器上下文不存在: " + contextId);
        }

        try {
            logger.debug("Saving context state for ID: {} to file: {}", contextId, cookieFilePath);

            Path cookiePath = Paths.get(cookieFilePath);
            // 确保父目录存在
            cookiePath.getParent().toFile().mkdirs();

            context.storageState(new BrowserContext.StorageStateOptions().setPath(cookiePath));

            logger.info("Context state saved successfully for ID: {}", contextId);

        } catch (Exception e) {
            logger.error("Failed to save context state for ID: {}", contextId, e);
            throw new BrowserException("保存浏览器上下文状态失败: " + e.getMessage(), e);
        }
    }

    /**
     * 关闭浏览器上下文
     *
     * @param contextId 上下文ID
     */
    public void closeContext(String contextId) {
        BrowserContext context = contexts.remove(contextId);
        if (context != null) {
            try {
                logger.debug("Closing browser context with ID: {}", contextId);
                context.close();
                logger.info("Browser context closed successfully with ID: {}", contextId);
            } catch (Exception e) {
                logger.warn("Error closing browser context with ID: {}", contextId, e);
            }
        }
    }

    /**
     * 关闭浏览器实例
     *
     * @param browserId 浏览器实例ID
     */
    public void closeBrowser(String browserId) {
        // 首先关闭该浏览器的所有上下文
        contexts.entrySet().removeIf(entry -> {
            String contextId = entry.getKey();
            BrowserContext context = entry.getValue();

            // 检查上下文是否属于该浏览器
            try {
                if (context.browser().equals(browsers.get(browserId))) {
                    logger.debug("Closing context {} belonging to browser {}", contextId, browserId);
                    context.close();
                    return true;
                }
            } catch (Exception e) {
                logger.warn("Error checking context ownership for {}", contextId, e);
            }
            return false;
        });

        // 关闭浏览器实例
        Browser browser = browsers.remove(browserId);
        if (browser != null) {
            try {
                logger.debug("Closing browser instance with ID: {}", browserId);
                browser.close();
                logger.info("Browser instance closed successfully with ID: {}", browserId);
            } catch (Exception e) {
                logger.warn("Error closing browser instance with ID: {}", browserId, e);
            }
        }
    }

    /**
     * 获取浏览器实例
     *
     * @param browserId 浏览器实例ID
     * @return Browser实例，如果不存在返回null
     */
    public Browser getBrowser(String browserId) {
        return browsers.get(browserId);
    }

    /**
     * 获取浏览器上下文
     *
     * @param contextId 上下文ID
     * @return BrowserContext实例，如果不存在返回null
     */
    public BrowserContext getContext(String contextId) {
        return contexts.get(contextId);
    }

    /**
     * 检查浏览器实例是否存在且连接正常
     *
     * @param browserId 浏览器实例ID
     * @return true如果存在且连接正常，否则false
     */
    public boolean isBrowserConnected(String browserId) {
        Browser browser = browsers.get(browserId);
        return browser != null && browser.isConnected();
    }

    /**
     * 获取当前活跃的浏览器实例数量
     *
     * @return 活跃的浏览器实例数量
     */
    public int getActiveBrowserCount() {
        return (int) browsers.values().stream()
                .filter(browser -> browser != null && browser.isConnected())
                .count();
    }

    /**
     * 获取当前活跃的上下文数量
     *
     * @return 活跃的上下文数量
     */
    public int getActiveContextCount() {
        return contexts.size();
    }

    /**
     * 关闭所有资源并清理
     */
    public void shutdown() {
        if (isShutdown.compareAndSet(false, true)) {
            logger.info("Shutting down BrowserManager...");

            // 关闭所有上下文
            contexts.keySet().forEach(this::closeContext);

            // 关闭所有浏览器
            browsers.keySet().forEach(this::closeBrowser);

            // 关闭Playwright
            try {
                playwright.close();
                logger.info("Playwright closed successfully");
            } catch (Exception e) {
                logger.warn("Error closing Playwright", e);
            }

            logger.info("BrowserManager shutdown completed");
        }
    }

    /**
     * 浏览器相关异常类
     */
    public static class BrowserException extends Exception {
        public BrowserException(String message) {
            super(message);
        }

        public BrowserException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}