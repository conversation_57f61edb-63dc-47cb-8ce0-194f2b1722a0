package com.lazhu.gxc.publish.service;

import com.lazhu.gxc.publish.model.AuthenticationInfo;
import com.lazhu.gxc.publish.model.LoginResult;
import com.lazhu.gxc.publish.model.LoginSession;
import com.lazhu.gxc.publish.model.PlatformType;

/**
 * 认证服务
 */
public interface AuthService {


    /**
     * 开始登录 获取登录二维码等信息
     *
     * @return 登录会话对象
     */
    LoginSession startLogin(String userId);

    /**
     * 查询登录状态 （结合 startLogin 使用)
     *
     * @param sessionId 登录会话ID
     * @return 登录结果
     */
    LoginResult checkLoginStatus(String sessionId);


    /**
     * 验证Cookie中的认证信息是否有效
     *
     * @param userId 用户ID（可选，用于多用户场景）
     * @return 验证结果，true表示有效
     */
    boolean validateCookies(String userId);

    /**
     * 加载认证信息
     *
     * @param userId 用户ID（可选，用于多用户场景）
     * @return 认证信息对象，如果加载失败返回null
     */
    AuthenticationInfo loadAuthInfo(String userId);


    /**
     * 保存认证信息
     *
     * @param authInfo 认证信息对象
     * @param userId   用户ID（可选，用于多用户场景）
     * @return 保存结果，true表示成功
     */
    boolean saveAuthInfo(AuthenticationInfo authInfo, String userId);


    /**
     * 获取支持的平台类型
     *
     * @return 平台类型
     */
    PlatformType getSupportedPlatform();

    /**
     * 清除认证信息
     *
     * @param userId 用户ID（可选，用于多用户场景）
     * @return 清除结果，true表示成功
     */
    boolean clearAuthInfo(String userId);


}