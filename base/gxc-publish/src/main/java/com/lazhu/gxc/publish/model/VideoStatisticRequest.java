package com.lazhu.gxc.publish.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.Set;

/**
 * 视频数据统计请求类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VideoStatisticRequest {

    /**
     * 平台类型
     */
    private PlatformType platformType;

    /**
     * 开始日期
     */
    private LocalDate startDate;

    /**
     * 结束日期
     */
    private LocalDate endDate;

    /**
     * 用户ID（可选）
     */
    private String userId;

    /**
     * 视频类型（全部视频 或 单篇视频）
     */
    @Builder.Default
    private VideoType videoType = VideoType.SINGLE;

    /**
     * 需要获取的数据类型
     */
    private Set<VideoDataType> dataTypes;

    /**
     * 页面大小（分页查询时使用）
     */
    @Builder.Default
    private Integer pageSize = 20;

    /**
     * 页码（分页查询时使用）
     */
    @Builder.Default
    private Integer pageNumber = 1;

    /**
     * 排序字段
     */
    private VideoSortField sortField;

    /**
     * 排序方向
     */
    @Builder.Default
    private SortDirection sortDirection = SortDirection.DESC;

    /**
     * 验证请求参数的有效性
     */
    public boolean isValid() {
        if (platformType == null) {
            return false;
        }
        
        if (startDate != null && endDate != null && startDate.isAfter(endDate)) {
            return false;
        }
        
        if (pageSize != null && pageSize <= 0) {
            return false;
        }
        
        if (pageNumber != null && pageNumber <= 0) {
            return false;
        }
        
        return true;
    }

    /**
     * 视频类型枚举
     */
    public enum VideoType {
        ALL("全部视频"),
        SINGLE("单篇视频");

        private final String description;

        VideoType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 视频数据类型枚举
     */
    public enum VideoDataType {
        PLAY("播放"),
        LIKE("点赞"),
        COMMENT("评论"),
        SHARE("分享"),
        FOLLOW("关注"),
        FORWARD("转发聊天和朋友圈"),
        RINGTONE("设为铃声"),
        STATUS("设为状态"),
        MOMENTS_COVER("设为朋友圈封面"),
        COMPLETION_RATE("完播率"),
        AVERAGE_PLAY_DURATION("平均播放时长");

        private final String description;

        VideoDataType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 排序字段枚举
     */
    public enum VideoSortField {
        PUBLISH_TIME("发布时间"),
        PLAY_COUNT("播放量"),
        LIKE_COUNT("点赞数"),
        COMMENT_COUNT("评论数"),
        SHARE_COUNT("分享数"),
        COMPLETION_RATE("完播率"),
        AVERAGE_PLAY_DURATION("平均播放时长");

        private final String description;

        VideoSortField(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 排序方向枚举
     */
    public enum SortDirection {
        ASC("升序"),
        DESC("降序");

        private final String description;

        SortDirection(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
