package com.lazhu.gxc.publish.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * 登录状态结果
 */
@Data
@Builder
public class LoginResult implements Serializable {

    /**
     * 登录状态：waiting(等待扫码), scanned(已扫码待确认), success(登录成功), failed(登录失败) expired(二维码过期)
     */
    private final String status;
    /**
     * 状态消息
     */
    private final String message;
    /**
     * 用户信息 (登录成功时返回)
     */
    private UserInfo UserInfo;

    public static LoginResult success(UserInfo userInfo) {
        return LoginResult.builder().status("success").message("登录成功").UserInfo(userInfo).build();
    }

    public static LoginResult failed(String message) {
        return LoginResult.builder().status("failed").message(message).build();
    }

    public static LoginResult waiting(String message) {
        return LoginResult.builder().status("waiting").message(message).build();
    }

    public static LoginResult scanned(String message) {
        return LoginResult.builder().status("scanned").message(message).build();
    }

    public static LoginResult expired(String message) {
        return LoginResult.builder().status("expired").message(message).build();
    }

    public boolean isSuccess() {
        return "success".equals(status);
    }

    /**
     * 用户信息
     */
    @Data
    @AllArgsConstructor
    public static class UserInfo {
        /**
         * 账号名称
         */
        private String account;
        /**
         * 昵称
         */
        private String nickName;

        /**
         * 头像
         */
        private String avatar;
    }
}