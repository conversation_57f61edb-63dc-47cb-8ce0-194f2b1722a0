package com.lazhu.gxc.publish.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 视频数据实体类
 * 包含单篇视频的各项统计数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VideoData {

    /**
     * 视频ID（如果有的话）
     */
    private String videoId;

    /**
     * 视频标题或描述
     */
    private String title;

    /**
     * 视频封面URL
     */
    private String coverUrl;

    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 播放量
     */
    private Long playCount;

    /**
     * 点赞数
     */
    private Long likeCount;

    /**
     * 评论数
     */
    private Long commentCount;

    /**
     * 分享数
     */
    private Long shareCount;

    /**
     * 关注数（通过该视频获得的关注）
     */
    private Long followCount;

    /**
     * 转发聊天和朋友圈数
     */
    private Long forwardCount;

    /**
     * 设为铃声数
     */
    private Long ringtoneCount;

    /**
     * 设为状态数
     */
    private Long statusCount;

    /**
     * 设为朋友圈封面数
     */
    private Long momentsCoverCount;

    /**
     * 完播率（百分比）
     */
    private Double completionRate;

    /**
     * 平均播放时长（秒）
     */
    private Double averagePlayDuration;

    /**
     * 视频时长（秒）
     */
    private Integer videoDuration;

    /**
     * 平台类型
     */
    private PlatformType platformType;

    /**
     * 数据获取时间
     */
    private LocalDateTime dataFetchTime;

    /**
     * 计算互动率
     * 互动率 = (点赞 + 评论 + 分享) / 播放量 * 100%
     */
    public Double calculateEngagementRate() {
        if (playCount == null || playCount == 0) {
            return 0.0;
        }
        
        long totalEngagement = 0;
        if (likeCount != null) totalEngagement += likeCount;
        if (commentCount != null) totalEngagement += commentCount;
        if (shareCount != null) totalEngagement += shareCount;
        
        return (double) totalEngagement / playCount * 100;
    }

    /**
     * 计算总互动数
     */
    public Long calculateTotalEngagement() {
        long total = 0;
        if (likeCount != null) total += likeCount;
        if (commentCount != null) total += commentCount;
        if (shareCount != null) total += shareCount;
        if (followCount != null) total += followCount;
        if (forwardCount != null) total += forwardCount;
        if (ringtoneCount != null) total += ringtoneCount;
        if (statusCount != null) total += statusCount;
        if (momentsCoverCount != null) total += momentsCoverCount;
        
        return total;
    }

    /**
     * 检查数据完整性
     */
    public boolean isDataComplete() {
        return publishTime != null && 
               playCount != null && 
               platformType != null;
    }

    /**
     * 获取视频表现等级
     * 基于播放量和互动率的综合评估
     */
    public VideoPerformanceLevel getPerformanceLevel() {
        if (playCount == null || playCount == 0) {
            return VideoPerformanceLevel.POOR;
        }
        
        Double engagementRate = calculateEngagementRate();
        
        if (playCount >= 100000 && engagementRate >= 5.0) {
            return VideoPerformanceLevel.EXCELLENT;
        } else if (playCount >= 10000 && engagementRate >= 3.0) {
            return VideoPerformanceLevel.GOOD;
        } else if (playCount >= 1000 && engagementRate >= 1.0) {
            return VideoPerformanceLevel.AVERAGE;
        } else {
            return VideoPerformanceLevel.POOR;
        }
    }

    /**
     * 视频表现等级枚举
     */
    public enum VideoPerformanceLevel {
        EXCELLENT("优秀"),
        GOOD("良好"), 
        AVERAGE("一般"),
        POOR("较差");

        private final String description;

        VideoPerformanceLevel(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
