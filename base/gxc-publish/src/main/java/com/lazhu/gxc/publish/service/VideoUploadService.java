package com.lazhu.gxc.publish.service;

import com.lazhu.gxc.publish.model.PlatformType;
import com.lazhu.gxc.publish.model.UploadRequest;
import com.lazhu.gxc.publish.model.UploadResult;

/**
 * 视频上传服务接口
 * 定义视频上传的通用方法，支持多平台扩展
 */
public interface VideoUploadService {

    /**
     * 上传视频到指定平台
     *
     * @return 上传结果
     */
    UploadResult uploadVideo(UploadRequest uploadRequest);

    /**
     * 获取支持的平台类型
     *
     * @return 平台类型
     */
    PlatformType getSupportedPlatform();

    /**
     * 取消正在进行的上传任务
     *
     * @param taskId 任务ID（如果支持）
     * @return 取消结果
     */
    boolean cancelUpload(String taskId);
}