package com.lazhu.gxc.publish.platform.wechat;

import com.lazhu.gxc.publish.browser.BrowserManager;
import com.lazhu.gxc.publish.browser.PageInteractionService;
import com.lazhu.gxc.publish.config.PublisherConfig;
import com.lazhu.gxc.publish.model.AuthenticationInfo;
import com.lazhu.gxc.publish.model.PlatformType;
import com.lazhu.gxc.publish.model.UploadRequest;
import com.lazhu.gxc.publish.model.UploadResult;
import com.lazhu.gxc.publish.platform.AbstractVideoUploadService;
import com.lazhu.gxc.publish.platform.wechat.WeChatVideoPageInteraction.PublishResult;
import com.lazhu.gxc.publish.platform.wechat.WeChatVideoPageInteraction.UploadStatus;
import com.lazhu.gxc.publish.service.AuthService;
import com.lazhu.gxc.publish.service.VideoValidationService;
import com.microsoft.playwright.BrowserContext;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.options.Cookie;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 微信视频号上传服务实现
 */
public class WeChatVideoUploadService extends AbstractVideoUploadService {

    private static final Logger logger = LoggerFactory.getLogger(WeChatVideoUploadService.class);

    // 微信视频号特定配置
    private static final String WECHAT_UPLOAD_URL = "https://channels.weixin.qq.com/platform/post/create";
    private static final int UPLOAD_TIMEOUT_MINUTES = 15; // 视频上传超时时间

    private final BrowserManager browserManager;
    private final WeChatVideoPageInteraction weChatPageInteraction;

    // 当前操作的浏览器上下文和页面（用于状态管理）
    private BrowserContext currentContext;
    private Page currentPage;

    /**
     * 构造函数
     */
    public WeChatVideoUploadService(PublisherConfig config, AuthService authService, VideoValidationService videoValidationService, BrowserManager browserManager, PageInteractionService pageInteractionService) {
        super(config, authService, videoValidationService);
        this.browserManager = browserManager;
        this.weChatPageInteraction = new WeChatVideoPageInteraction(pageInteractionService);
    }

    /**
     * 获取支持的平台类型
     */
    @Override
    public PlatformType getSupportedPlatform() {
        return PlatformType.WECHAT_VIDEO;
    }


    // ========== 模板方法实现 ==========


    @Override
    protected boolean validateMetadata(UploadRequest.VideoMetadata metadata) throws Exception {
        //字段校验
        // 标题6-16 个字
        if (metadata.getTitle() == null || metadata.getTitle().trim().length() < 6 || metadata.getTitle().trim().length() > 16) {
            throw new IllegalArgumentException("视频标题6-16个字");
        }
        // 描述不能为空
        if (metadata.getDescription() == null || metadata.getDescription().trim().isEmpty()) {
            throw new IllegalArgumentException("视频描述不能为空");
        }
        return true;
    }

    /**
     * 导航到上传页面
     */
    @Override
    protected void navigateToUploadPage(UploadRequest request) {
        try {
            logger.info("初始化浏览器并导航到微信视频号上传页面");

            // 创建浏览器上下文和页面
            setupBrowserContext(request.getUserId());

            // 导航到上传页面
            weChatPageInteraction.navigateToUploadPage(currentPage);
            

        } catch (Exception e) {
            logger.error("导航到上传页面失败", e);
            throw new RuntimeException("导航到上传页面失败: " + e.getMessage(), e);
        }
    }

    /**
     * 上传视频文件
     */
    @Override
    protected void uploadVideoFile(String videoFilePath) {
        try {
            logger.info("开始上传视频文件: {}", videoFilePath);

            // 确保页面已准备好
            if (currentPage == null) {
                throw new RuntimeException("页面未初始化，请先导航到上传页面");
            }

            Path videoPath = Paths.get(videoFilePath);

            // 上传视频文件
            weChatPageInteraction.uploadVideoFile(currentPage, videoPath);

            // 等待上传完成并检测上传状态
            UploadStatus uploadStatus = weChatPageInteraction.waitForUploadComplete(currentPage);

            switch (uploadStatus) {
                case SUCCESS:
                    logger.info("视频文件上传成功");
                    break;
                case FAILED:
                    throw new RuntimeException("视频文件上传失败");
                case TIMEOUT:
                    throw new RuntimeException("视频文件上传超时");
                default:
                    throw new RuntimeException("视频文件上传状态未知");
            }

        } catch (Exception e) {
            logger.error("上传视频文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("上传视频文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 填写视频元数据
     */
    @Override
    protected void fillMetadata(UploadRequest.VideoMetadata metadata) {
        try {
            logger.info("开始填写视频元数据");

            if (currentPage == null) {
                throw new RuntimeException("页面未初始化");
            }

            // 1. 填写视频标题（支持短标题格式化）
            if (metadata.getTitle() != null && !metadata.getTitle().trim().isEmpty()) {
                weChatPageInteraction.fillVideoTitle(currentPage, metadata.getTitle());
            }

            // 2. 填写视频描述
            if (metadata.getDescription() != null && !metadata.getDescription().trim().isEmpty()) {
                weChatPageInteraction.fillVideoDescription(currentPage, metadata.getDescription());
            }

            // 3. 填写位置信息
            if (metadata.getLocation() != null && !metadata.getLocation().trim().isEmpty()) {
                weChatPageInteraction.fillLocation(currentPage, metadata.getLocation());
            }

            // 4. 设置定时发布
            if (metadata.isScheduled()) {
                weChatPageInteraction.setScheduleTime(currentPage, metadata.getPublishTime());
            }

            logger.info("视频元数据填写完成");

        } catch (Exception e) {
            logger.error("填写视频元数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("填写视频元数据失败: " + e.getMessage(), e);
        }
    }


    /**
     * 提交上传
     */
    @Override
    protected UploadResult submitUpload() {
        try {
            logger.info("提交视频发布");

            if (currentPage == null) {
                return UploadResult.failure("页面未初始化", getSupportedPlatform());
            }

            // 发布视频
            weChatPageInteraction.publishVideo(currentPage);

            // 检查发布结果
            PublishResult publishResult = weChatPageInteraction.checkPublishResult(currentPage);

            if (publishResult.isSuccess()) {
                logger.info("视频发布成功: {}", publishResult.getMessage());
                return UploadResult.success("视频发布成功: " + publishResult.getMessage(), getSupportedPlatform());
            } else {
                logger.error("视频发布失败: {}", publishResult.getMessage());
                return UploadResult.failure("视频发布失败: " + publishResult.getMessage(), getSupportedPlatform());
            }

        } catch (Exception e) {
            logger.error("提交上传失败: {}", e.getMessage(), e);
            return UploadResult.failure("提交上传失败: " + e.getMessage(), e, getSupportedPlatform());
        } finally {
            // 清理浏览器资源
            cleanupBrowserContext();
        }
    }


    /**
     * 取消上传任务（微信视频号特定实现）
     */
    @Override
    public boolean cancelUpload(String taskId) {
        try {
            logger.info("尝试取消微信视频号上传任务: {}", taskId);

            if (currentPage != null) {
                // 尝试关闭当前页面来取消上传
                currentPage.close();
                logger.info("已关闭上传页面，任务可能已取消");
                return true;
            }

            logger.warn("没有活动的上传任务可以取消");
            return false;

        } catch (Exception e) {
            logger.error("取消上传任务失败: {}", e.getMessage(), e);
            return false;
        } finally {
            cleanupBrowserContext();
        }
    }

    // ========== 私有辅助方法 ==========

    /**
     * 设置浏览器上下文
     */
    private void setupBrowserContext(String userId) {
        try {
            AuthenticationInfo authInfo = authService.loadAuthInfo(userId);
            List<Map<String, String>> cookies = authInfo.getCookies();
            // 创建浏览器上下文，使用默认浏览器ID和上下文ID
            String browserId = "upload-browser-" + UUID.randomUUID();
            String contextId = "upload-context-" + UUID.randomUUID();
            browserManager.createBrowser(browserId);
            currentContext = browserManager.createContext(browserId, contextId, null);
            // 设置上下文cookie
            List<Cookie> cookieList = new ArrayList<>();
            for (Map<String, String> cookieMap : cookies) {
                Cookie cookie = new Cookie(cookieMap.get("name"), cookieMap.get("value"));
                cookie.setDomain(".weixin.qq.com");
                cookie.setPath("/");
                cookieList.add(cookie);
            }
            currentContext.addCookies(cookieList);
            currentPage = currentContext.newPage();

            // 设置页面超时时间 
            currentPage.setDefaultTimeout(30000); // 30秒默认超时

            logger.debug("浏览器上下文设置完成");

        } catch (Exception e) {
            logger.error("设置浏览器上下文失败", e);
            cleanupBrowserContext();
            throw new RuntimeException("设置浏览器上下文失败: " + e.getMessage(), e);
        }
    }

    /**
     * 清理浏览器上下文
     */
    private void cleanupBrowserContext() {
        try {
            if (currentPage != null) {
                currentPage.close();
                currentPage = null;
            }

            if (currentContext != null) {
                currentContext.close();
                currentContext = null;
            }

            logger.debug("浏览器上下文清理完成");

        } catch (Exception e) {
            logger.warn("清理浏览器上下文时发生异常: {}", e.getMessage());
        }
    }

}