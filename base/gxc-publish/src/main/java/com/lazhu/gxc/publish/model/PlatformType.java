package com.lazhu.gxc.publish.model;

/**
 * 平台类型枚举
 * 定义支持的视频发布平台类型
 */
public enum PlatformType {
    /**
     * 微信视频号
     */
    WECHAT_VIDEO("wechat_channels", "微信视频号"),

    /**
     * 抖音
     */
    DOUYIN("douyin", "抖音"),

    /**
     * 小红书
     */
    XIAOHONGSHU("xiaohongshu", "小红书");

    private final String identifier;
    private final String displayName;

    /**
     * 构造函数
     *
     * @param identifier  平台标识符
     * @param displayName 平台显示名称
     */
    PlatformType(String identifier, String displayName) {
        this.identifier = identifier;
        this.displayName = displayName;
    }

    /**
     * 根据标识符获取平台类型
     *
     * @param identifier 平台标识符
     * @return 对应的平台类型
     * @throws IllegalArgumentException 如果标识符不存在
     */
    public static PlatformType fromIdentifier(String identifier) {
        for (PlatformType type : values()) {
            if (type.identifier.equals(identifier)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown platform identifier: " + identifier);
    }

    /**
     * 获取平台标识符
     *
     * @return 平台标识符
     */
    public String getIdentifier() {
        return identifier;
    }

    /**
     * 获取平台显示名称
     *
     * @return 平台显示名称
     */
    public String getDisplayName() {
        return displayName;
    }

    @Override
    public String toString() {
        return displayName;
    }
}