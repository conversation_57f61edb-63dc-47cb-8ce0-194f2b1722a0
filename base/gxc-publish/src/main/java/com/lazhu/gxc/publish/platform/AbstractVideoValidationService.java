package com.lazhu.gxc.publish.platform;

import com.lazhu.common.media.MediaUtil;
import com.lazhu.common.media.VideoInfo;
import com.lazhu.gxc.publish.service.VideoValidationService;
import com.lazhu.gxc.publish.util.FileUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * 通用视频验证服务抽象类
 * 提供基础的视频验证功能，其他平台可以继承并扩展特定规则
 */
@AllArgsConstructor
@Getter
public abstract class AbstractVideoValidationService implements VideoValidationService {

    private static final Logger logger = LoggerFactory.getLogger(AbstractVideoValidationService.class);

    // 支持的视频格式
    protected List<String> supportedFormats;
    // 支持的视频编码
    protected List<String> supportedCodecs;
    // 最大文件大小（字节）
    protected long maxFileSize;
    // 最大时长（秒）
    protected int maxDurationSeconds;
    // 最小宽度
    protected int minWidth;
    // 最小高度
    protected int minHeight;
    // 推荐宽度
    protected int recommendedWidth;
    // 推荐高度
    protected int recommendedHeight;


    @Override
    public ValidationResult validateVideo(String filePath) {
        logger.info("开始验证视频文件: {}", filePath);

        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();

        // 1. 基础文件验证
        if (!validateBasicFile(filePath, errors)) {
            return new ValidationResult(false, errors, warnings, null);
        }

        // 2. 获取视频信息
        VideoInfo videoInfo = MediaUtil.getVideoInfo(filePath);
        if (videoInfo == null) {
            errors.add("无法获取视频信息，可能文件损坏或格式不支持");
            return new ValidationResult(false, errors, warnings, null);
        }

        // 3. 验证通用视频属性
        validateVideoProperties(videoInfo, errors, warnings);

        // 4. 平台特定验证（子类实现）
        validatePlatformSpecificProperties(videoInfo, errors, warnings);

        boolean isValid = errors.isEmpty();

        if (isValid) {
            logger.info("视频验证通过: {}", filePath);
        } else {
            logger.warn("视频验证失败: {}, 错误: {}", filePath, errors);
        }

        if (!warnings.isEmpty()) {
            logger.warn("视频验证警告: {}, 警告: {}", filePath, warnings);
        }

        return new ValidationResult(isValid, errors, warnings, videoInfo);
    }

    /**
     * 平台特定验证逻辑，子类可以重写此方法添加特定规则
     *
     * @param videoInfo 视频信息
     * @param errors    错误列表
     * @param warnings  警告列表
     */
    protected abstract void validatePlatformSpecificProperties(VideoInfo videoInfo, List<String> errors, List<String> warnings);


    /**
     * 验证基础文件属性
     */
    private boolean validateBasicFile(String filePath, List<String> errors) {
        // 检查文件是否存在
        if (!FileUtils.isValidFile(filePath)) {
            errors.add("文件不存在或不是有效文件: " + filePath);
            return false;
        }
        // 检查文件格式
        List<String> supportedFormats = getSupportedFormats();
        if (!FileUtils.isValidVideoFormat(filePath, supportedFormats)) {
            String extension = FileUtils.getFileExtension(filePath);
            errors.add(String.format("不支持的视频格式: %s，支持的格式: %s", extension, supportedFormats.toString()));
            return false;
        }
        // 检查文件大小
        long maxFileSize = getMaxFileSize();
        if (!FileUtils.isValidFileSize(filePath, maxFileSize)) {
            long fileSize = FileUtils.getFileSize(filePath);
            errors.add(String.format("文件大小超出限制: %s (最大: %s)", FileUtils.formatFileSize(fileSize), FileUtils.formatFileSize(maxFileSize)));
            return false;
        }
        return true;
    }

    /**
     * 验证基础视频属性（通用规则）
     */
    protected void validateVideoProperties(VideoInfo videoInfo, List<String> errors, List<String> warnings) {
        // 验证时长
        if (videoInfo.getDuration() > maxDurationSeconds) {
            errors.add(String.format("视频时长超出限制: %s (最大: %s)", videoInfo.getDuration(), maxDurationSeconds));
        }
        // 验证编码
        if (!supportedCodecs.contains(videoInfo.getCodec())) {
            errors.add(String.format("不支持的视频编码: %s，支持的编码: %s", videoInfo.getCodec(), supportedCodecs));
        }

        // 验证分辨率
        if (videoInfo.getWidth() < minWidth || videoInfo.getHeight() < minHeight) {
            errors.add(String.format("视频分辨率低于最低要求: 最低: %dx%d", minWidth, minHeight));
        } else if (videoInfo.getWidth() < recommendedWidth || videoInfo.getHeight() < recommendedHeight) {
            warnings.add(String.format("视频分辨率低于推荐值: 推荐: %dx%d", recommendedWidth, recommendedHeight));
        }

        // 检查宽高比
        String aspectRatio = videoInfo.getAspectRatio();
        if (!"9:16".equals(aspectRatio)) {
            warnings.add(String.format("视频宽高比可能不太合适: %s (推荐9:16)", aspectRatio));
        }
    }
}