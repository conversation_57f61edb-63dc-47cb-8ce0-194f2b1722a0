package com.lazhu.gxc.publish.model;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

@Builder
@Data
public class UploadRequest {

    /**
     * 平台类型
     */
    private PlatformType platformType;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 视频路径
     */
    private String videoPath;

    /**
     * 视频元数据
     */
    private VideoMetadata videoMetadata;

    /**
     * 视频元数据
     */
    @Builder
    @Data
    public static class VideoMetadata {

        /**
         * 视频标题
         */
        private String title;
        /**
         * 视频描述
         */
        private String description;

        /**
         * 位置
         */
        private String location;

        /**
         * 是否定时发布
         */
        private boolean isScheduled;
        /**
         * 定时发布时间
         */
        private LocalDateTime publishTime;
    }

}
