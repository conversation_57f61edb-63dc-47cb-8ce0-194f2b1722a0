package com.lazhu.baseai.llm.dto;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 文本生成请求体
 */
@Slf4j
@Data
public class TextCreationReq {

    private String plat;

    private String title;

    private String content;

    /**
     * 主题
     */
    private String idea;
    /**
     * 提示词
     */
    private String prompt;

    /**
     * 编辑索引 (第一次生成为空)
     */
    private Integer editIndex;

    /**
     * 编辑建议
     */
    private String query;

    /**
     * 内容类型  1 图文  2口播文案
     */
    private String contentType;

    /**
     * 关联的话题
     */
    private String hotTopics;

    /**
     * 参考案例
     */
    private String example;

    /**
     * 字数
     */
    private String wordCount;
}
