package com.lazhu.baseai.tool.audio;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.lazhu.baseai.tool.audio.CosyVoiceTool.AudioInfo;
import com.lazhu.common.utils.RedissonRateLimiter;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.ForkJoinTask;
import java.util.concurrent.TimeUnit;

@Slf4j
public class TxtToAudioService {

    // 文件路径模板常量
    private static final String MP3_TARGET_PATTERN = "{0}/{1}/audio.mp3";
    private static final String SRT_TARGET_PATTERN = "{0}/{1}/audio.srt";
    private static final String MP3_SUB_TAR_PAT = "{0}/{1}/audio_{2}.mp3";
    private static final String FOLDER_PATTERN = "{0}/{1}";

    // SRT 字幕条目模板
    private static final String SRT_ITEM_TEMPLATE = """
            {0}
            {1} --> {2}
            {3}
            """;

    // 并发控制常量
    private static final int MAX_CALLS_PER_SECOND = 3;
    private static final int SHUTDOWN_TIMEOUT_SECONDS = 10;

    // 全局线程池
    private static final ForkJoinPool AUDIO_POOL = new ForkJoinPool();

    // Redisson限流器
    private static volatile RedissonRateLimiter rateLimiter;
    private static RedissonClient redissonClient = SpringUtil.getBean(RedissonClient.class);

    static {
        // 优雅关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            log.info("正在关闭音频处理相关资源...");

            // 关闭主线程池
            AUDIO_POOL.shutdown();
            try {
                if (!AUDIO_POOL.awaitTermination(SHUTDOWN_TIMEOUT_SECONDS, TimeUnit.SECONDS)) {
                    log.warn("主线程池未能在{}s内正常关闭，强制关闭", SHUTDOWN_TIMEOUT_SECONDS);
                    AUDIO_POOL.shutdownNow();
                }
            } catch (InterruptedException e) {
                log.warn("关闭主线程池时被中断，强制关闭");
                AUDIO_POOL.shutdownNow();
                Thread.currentThread().interrupt();
            }

            log.info("音频处理资源关闭完成");
        }));
        initRedissonRateLimiter();
    }


    /**
     * 初始化Redisson限流器
     */
    private static void initRedissonRateLimiter() {
        // 初始化RedissonRateLimiter，设置限流规则：每秒最多3个请求
        rateLimiter = new RedissonRateLimiter(redissonClient, "txt_to_audio_rate_limiter", MAX_CALLS_PER_SECOND, Duration.ofSeconds(1));
    }

    /**
     * Redisson限流控制方法
     * 确保API调用不超过限制频率
     */
    private static void redissonThrottle() {
        if (rateLimiter == null) {
            // 如果Redisson未初始化，回退到原有本地限流
            try {
                Thread.sleep(1000 / MAX_CALLS_PER_SECOND); // 简单延迟模拟限流
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            return;
        }
        try {
            // 尝试获取许可，阻塞直到获取成功
            rateLimiter.acquire();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.warn("Redisson限流器异常，使用备用限流策略", e);
            try {
                Thread.sleep(1000 / MAX_CALLS_PER_SECOND); // 简单延迟模拟限流
            } catch (InterruptedException ie) {
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 文本转音频处理主方法
     *
     * @param id      任务标识
     * @param folder  输出文件夹路径
     * @param txt     待转换文本内容
     * @param voiceId 语音模型ID
     */
    public static void exec(String id, String folder, String txt, String voiceId) {
        try {
            // 文本分割
            String[] textSegments = TxtSplitter.smartSplit(txt);

            // 准备输出目录
            String targetFolder = MessageFormat.format(FOLDER_PATTERN, folder, id);
            FileUtil.del(targetFolder);
            FileUtil.mkdirsSafely(new File(targetFolder), 1, 100);

            // 并行生成音频片段
            long start = System.currentTimeMillis();
            List<ForkJoinTask<AudioInfo>> tasks = new ArrayList<>(textSegments.length);
            for (int i = 0; i < textSegments.length; i++) {
                String t = textSegments[i];
                if (StrUtil.isBlank(txt)) {
                    continue;
                }
                final int n = i;
                ForkJoinTask<AudioInfo> task = AUDIO_POOL.submit(() -> tts(n, voiceId, t, getMp3PartPath(id, folder, n)));
                tasks.add(task);
            }
            List<AudioInfo> audioInfos = new ArrayList<>(tasks.size());
            for (ForkJoinTask<AudioInfo> f : tasks) {
                audioInfos.add(f.get());
            }
            log.info("音频生成完成耗时：{} s", (System.currentTimeMillis() - start) / 1000);

            // 合并音频文件
            audioMerge(id, folder, audioInfos);

            // 生成字幕文件
            generateSubtitleFile(audioInfos, folder, id);

        } catch (Exception e) {
            log.error("文本转音频处理失败: id={}, folder={}, voiceId={}", id, folder, voiceId, e);
        }
    }

    /**
     * 获取音频片段文件路径
     */
    public static String getMp3PartPath(String id, String folder, int index) {
        return MessageFormat.format(MP3_SUB_TAR_PAT, folder, id, index);
    }

    public static String audioMerge(String id, String folder, List<AudioInfo> audioInfos) {
        String mp3TargetPath = MessageFormat.format(MP3_TARGET_PATTERN, folder, id);
        if (FileUtil.exist(mp3TargetPath)) {
            FileUtil.del(mp3TargetPath);
        }
        long startTime = System.currentTimeMillis();

        for (AudioInfo audioInfo : audioInfos) {
            String subFile = getMp3PartPath(id, folder, audioInfo.seq());
            MP3Tool.mergeAndOverwriteFirst(mp3TargetPath, subFile);
            // 删除临时文件
            FileUtil.del(subFile);
        }
        log.info("音频文件合并完成，耗时：{}s", (System.currentTimeMillis() - startTime) / 1000);
        return mp3TargetPath;
    }

    /**
     * 生成字幕文件
     */
    public static String generateSubtitleFile(List<AudioInfo> audioInfos, String folder, String id) {
        String srtTargetPath = MessageFormat.format(SRT_TARGET_PATTERN, folder, id);
        long startTime = System.currentTimeMillis();
        long start = 0L;

        // 初始化字幕文件
        FileUtil.writeString("", srtTargetPath, StandardCharsets.UTF_8);

        // 排序
        audioInfos = audioInfos.stream().sorted(Comparator.comparingInt(AudioInfo::seq)).toList();

        for (int i = 0; i < audioInfos.size(); i++) {
            AudioInfo audioInfo = audioInfos.get(i);

            // 生成字幕条目
            long end = start + audioInfo.timeLong();
            String subtitleEntry = MessageFormat.format(SRT_ITEM_TEMPLATE,
                    i + 1,
                    TimeFormatUtil.formatTime(start),
                    TimeFormatUtil.formatTime(end),
                    audioInfo.txt());

            FileUtil.appendString(subtitleEntry + System.lineSeparator(), srtTargetPath, StandardCharsets.UTF_8);
            start = end + 1;
        }

        log.info("字幕文件生成完成，耗时：{}s", (System.currentTimeMillis() - startTime) / 1000);
        return srtTargetPath;
    }

    /**
     * 生成单个音频片段
     *
     * @param index      片段索引
     * @param voiceId    语音模型ID
     * @param text       文本内容
     * @param outputPath 输出文件路径
     * @return 音频信息对象，失败时返回null
     */
    public static AudioInfo tts(int index, String voiceId, String text, String outputPath) {
        try {
            // 使用Redisson限流控制
            redissonThrottle();

            // 生成音频
            return CosyVoiceTool.createAudio(index, voiceId, text, outputPath);

        } catch (Exception e) {
            log.error("单个音频生成失败: index={}, voiceId={}, text={}, outputPath={}", index, voiceId, text, outputPath, e);
            return null;
        }
    }

    /**
     * 示例方法，展示如何使用该服务
     */
    public static void main(String[] args) {
        String txt = "这两年，继盲盒火爆后，盲卡凭借未知惊喜的卖点，迅速在儿童青少年群体中走红。非理性消费在儿童青少年中屡见不鲜，部分孩子为了追求稀有卡牌，单次消费高达数百元甚至数千元。";
        String SSMLTxt = """
                <speak><break time="400ms"/></speak>
                <speak rate="1.05" volume="52">这两年，继盲盒火爆之后，盲卡靠着"未知惊喜"这个卖点，迅速在儿童和青少年群体里火了起来。</speak>
                <speak><break time="300ms"/></speak>
                <speak rate="1" volume="50">不少孩子出现了非理性消费的现象，</speak>
                <speak rate="1.1" volume="54">有的为了抽到稀有卡牌，一次就花掉几百元，甚至上千元。</speak>
                """;
        exec("1", "d:/test/sra", SSMLTxt, "longxiaochun_v2");
    }

}