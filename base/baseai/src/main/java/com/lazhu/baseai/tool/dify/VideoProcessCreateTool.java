package com.lazhu.baseai.tool.dify;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 视频剪辑流程创建工具类
 * 基于AI接口，根据字幕文件和视频模板生成智能化的视频剪辑流程
 * 支持自动化视频制作流程的生成和配置
 */
@Service
@Slf4j
public class VideoProcessCreateTool {

    @Value("${ai.workflow_url:}")
    private String url;

    @Value("${ai.video_process_create_token:}")
    private String token;


    private static final String request_body = """
            {
               "files": [],
               "inputs": {
                  "srt": "",
                  "video_template": ""
               },
               "response_mode": "blocking",
               "user": "aigc_video"
            }
            """;

    /**
     * 基于AI创建视频剪辑流程
     * 根据输入的字幕文件和视频模板，通过AI接口生成智能化的视频剪辑流程配置
     *
     * @param srt             字幕文件内容（SRT格式）
     * @param processTemplate 视频剪辑流程模板内容
     * @return 生成的视频剪辑流程配置数组，包含剪辑步骤和参数
     */
    public JSONArray createProcess(String srt, String processTemplate) {

        try {
            // 构建请求参数
            JSONObject request = buildRequest(srt, processTemplate);
            String requestJson = JSONObject.toJSONString(request);
            log.info("视频剪辑流程创建接口请求参数：{}", requestJson);

            // 发送HTTP请求
            HttpRequest httpRequest = HttpUtil.createPost(url)
                    .header("Authorization", token)
                    .header("Content-Type", "application/json")
                    .body(requestJson);

            String responseBody;
            try (HttpResponse response = httpRequest.execute()) {
                responseBody = response.body();
            }
            log.info("视频剪辑流程创建接口响应：{}", responseBody);
            // 解析响应
            return parseResponse(responseBody);

        } catch (Exception e) {
            log.error("视频剪辑流程创建异常", e);
            throw e;
        }
    }


    /**
     * 构建视频剪辑流程创建请求对象
     *
     * @param srt 字幕文件内容
     * @param processTemplate 视频剪辑流程模板
     * @return 构建好的请求JSON对象
     */
    private JSONObject buildRequest(String srt, String processTemplate) {
        JSONObject object = JSONObject.parseObject(request_body);

        JSONObject inputs = object.getJSONObject("inputs");

        inputs.put("video_template", processTemplate);
        inputs.put("srt", srt);

        return object;
    }

    /**
     * 解析视频剪辑流程创建接口响应结果
     *
     * @param responseBody 接口响应体
     * @return 解析后的视频剪辑流程配置数组
     */
    private JSONArray parseResponse(String responseBody) {
        if (StrUtil.isBlank(responseBody)) {
            throw new RuntimeException("视频剪辑流程创建接口响应数据为空");
        }
        JSONObject response = JSONObject.parseObject(responseBody);

        // 检查响应状态
        JSONObject data = response.getJSONObject("data");
        if (data == null) {
            throw new RuntimeException("视频剪辑流程创建接口响应数据为空");
        }

        String status = data.getString("status");
        if (!"succeeded".equals(status)) {
            String error = data.getString("error");
            log.error("视频剪辑流程创建失败，状态：{}，错误信息：{}", status, error);
            throw new RuntimeException("视频剪辑流程创建失败");
        }

        // 提取结果
        JSONObject outputs = data.getJSONObject("outputs");
        if (outputs == null) {
            throw new RuntimeException("视频剪辑流程创建接口返回结果为空");
        }
        return outputs.getJSONArray("output");
    }

    /**
     * 测试方法 - 演示如何使用视频剪辑流程创建工具
     */
    public static void main(String[] args) {
        VideoProcessCreateTool tool = new VideoProcessCreateTool();
        tool.url = "http://192.168.33.174/v1/workflows/run";
        tool.token = "Bearer app-Omc4cZp3MdjVQE4QT7yRvJkZ";

        // 示例字幕文件内容
//        String srt = """
//                1\\n00:00:00,000 --> 00:00:16,500\\n老铁们，今天A股上演绝地反击，新能源板块直接暴走，4200只股票集体飘红。为什么说今天是短线课的狂欢日？三大主线藏着暴富密码，马上解析。\\n\\n2\\n00:00:16,500 --> 00:00:20,920\\n第一大主线，新能源车订单报单成本腰斩。\\n\\n3\\n00:00:20,920 --> 00:00:38,120\\n宁德时代放出王炸，钠离子电池量产成本降到0.35元，唯一比一杯奶茶还便宜比亚迪汉依威车型订单直接突破10万辆，阳光电源、海外储能订单环比暴涨210%。欧美市场疯抢中国制造。\\n\\n4\\n00:00:38,120 --> 00:00:39,980\\n第二大主线。\\n\\n5\\n00:00:39,980 --> 00:00:44,060\\n光伏技术突破下出口暴增。\\n\\n6\\n00:00:44,060 --> 00:01:06,040\\n容积率能hpb c pro组件效率突破23.8%，单日成交额冲上120亿。更猛的是，七月光伏出口数据同比暴增67%，通威股份单晶硅片价格上调3%。东方日升海外订单排产到Q4，固德威逆变器出货量创历史新高。\\n\\n7\\n00:01:06,040 --> 00:01:45,640\\n第三大主线氢能源政策落地国家发改委明确2025年氢燃料电池车保有量5万辆目标煤给能源氢能重卡订单翻倍一华通氢燃料电池系统市占率飙升至35%突发催化来了海关总署数据炸裂光伏组件单月出口28GW相当于给全球装了28万个巨型充电宝实战策略看这里主线配置抓光伏设备龙头精胜机电、锂电材料黑马荡生科技氢能源核心部件易华通短线机会钉。\\n\\n8\\n00:01:45,640 --> 00:01:45,640\\n光伏玻璃龙头性异光能补涨空间风险提示，新能源车指数若突破1300点，要警惕回调。单板块仓位别超40%今天你吃到新能源的肉了吗？刘言。\\n\\n
//                """;
        String srt = FileUtil.readString("C:\\Users\\<USER>\\Desktop\\audio (1).srt", "UTF-8");

        // 示例视频剪辑流程模板
        String processTemplate = """
                {"img":{"bg":"clear","limit":3,"pos":"center","ttl":"3-5"},"video":{"bg":"clear","limit":1,"pos":"center"}}
                """;

        // 调用AI接口创建视频剪辑流程
        JSONArray videoProcess = tool.createProcess(srt, processTemplate);
        System.out.println("生成的视频剪辑流程：" + videoProcess);
    }
}
