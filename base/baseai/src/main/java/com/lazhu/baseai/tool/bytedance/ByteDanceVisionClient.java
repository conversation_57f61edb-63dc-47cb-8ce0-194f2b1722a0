package com.lazhu.baseai.tool.bytedance;

import cn.hutool.core.util.StrUtil;
import com.volcengine.ark.runtime.model.completion.chat.*;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionContentPart.ChatCompletionContentPartImageURL;
import com.volcengine.ark.runtime.service.ArkService;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.Dispatcher;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 字节火山引擎视觉理解客户端
 */
@Slf4j
@Component
public class ByteDanceVisionClient {

    private static final String apiKey = "e1a4d884-7ccc-48bb-91f8-b0c2071848de";

    private static final String MODEL = "doubao-seed-1-6";

    private static final String DEFAULT_IMAGE_PROMPT = "请分析图片中的内容，概括图片的主要特征。便于检索";
    private static final String DEFAULT_VIDEO_PROMPT = "请分析视频中的内容，概括视频的主要特征。便于检索";

    private static ArkService service;

    public ByteDanceVisionClient() {
        init();
    }


    private void init() {
        ConnectionPool connectionPool = new ConnectionPool(5, 1, TimeUnit.SECONDS);
        service = ArkService.builder()
                .dispatcher(new Dispatcher())
                .connectionPool(connectionPool)
                .apiKey(apiKey)
                .build();
    }


    /**
     * 图片分析
     *
     * @param imageUrl 图片URL
     * @param prompt   提示词
     * @return 分析结果
     */
    public String analyzeImage(String imageUrl, String prompt) {
        List<ChatCompletionContentPart> parts = List.of(
                ChatCompletionContentPart.builder()
                        .type("image_url")
                        .imageUrl(new ChatCompletionContentPartImageURL(imageUrl, "auto"))
                        .build(),
                ChatCompletionContentPart.builder()
                        .type("text")
                        .text(StrUtil.isBlank(prompt) ? DEFAULT_IMAGE_PROMPT : prompt)
                        .build()
        );
        return callWithContent(parts, MODEL);
    }

    /**
     * 视频分析
     *
     * @param videoUrl 视频URL
     * @param prompt   提示词
     * @return 分析结果
     */
    public String analyzeVideo(String videoUrl, String prompt) {
        List<ChatCompletionContentPart> parts = List.of(
                ChatCompletionContentPart.builder()
                        .type("video_url")
                        .videoUrl(new ChatCompletionContentPart.ChatCompletionContentPartVideoURL(videoUrl, 1))
                        .build(),
                ChatCompletionContentPart.builder()
                        .type("text")
                        .text(StrUtil.isBlank(prompt) ? DEFAULT_VIDEO_PROMPT : prompt)
                        .build()
        );
        return callWithContent(parts, MODEL);
    }


    private static String callWithContent(List<ChatCompletionContentPart> contentParts, String model) {
        List<ChatMessage> messages = List.of(
                ChatMessage.builder()
                        .role(ChatMessageRole.USER)
                        .multiContent(contentParts)
                        .build()
        );
        ChatCompletionRequest req = ChatCompletionRequest.builder()
                .model(model)
                .messages(messages)
                .build();

        try {
            ChatCompletionResult resp = service.createChatCompletion(req);
            if (resp == null || resp.getChoices() == null) {
                throw new RuntimeException("API调用返回空结果");
            }
            return resp.getChoices()
                    .stream()
                    .map(c -> c.getMessage() != null ? c.getMessage().getContent().toString() : "")
                    .collect(Collectors.joining("\n"));
        } catch (Exception e) {
            throw new RuntimeException("调用失败", e);
        }
    }


    @PreDestroy
    public void destroy() {
        service.shutdownExecutor();
    }

    public static void main(String[] args) throws Exception {
        ByteDanceVisionClient client = new ByteDanceVisionClient();
        // 使用新方法处理图片分析
        String result = client.analyzeImage("https://ins-file.lazhuyun.cn/test/image/2025/09/03/1756863961323298463.png",
                "请概括描述图里的内容,以便于准确检索");
        System.out.println(result);

//        String videoResult = client.analyzeVideo("https://ins-file.lazhuyun.cn/test/video/1959942617257340930/video.mp4", "视频里有什么");
//        System.out.println(videoResult);

        // shutdown service after all requests is finished
        service.shutdownExecutor();
    }

}