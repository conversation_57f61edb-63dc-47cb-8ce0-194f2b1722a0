package com.lazhu.baseai.tool.dify;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * TTS优化工具类
 * 用于调用TTS优化接口，将普通文本转换为带有SSML标记的优化文本
 */
@Slf4j
@Component
public class TTSOptimizationTool {

    /**
     * TTS优化接口URL
     */
    @Value("${ai.workflow_url:}")
    private String optimizationUrl;

    /**
     * 认证token
     */
    @Value("${ai.tts_optimization_auth:}")
    private String authToken;


    private static final String request_body = """
            {
                "inputs": {
                    "text": ""
                },
                "response_mode": "blocking",
                "user": "user-123"
            }
            """;


    /**
     * 优化TTS文本
     *
     * @param text 原始文本
     * @return 优化后的TTS文本
     */
    public String optimizeText(String text) {
        String response = optimizeTextWithFullResponse(text);
        if (StrUtil.isNotEmpty(response)) {
            return response;
        }
        log.warn("TTS优化失败，返回原始文本");
        return text; // 如果优化失败，返回原始文本
    }

    /**
     * 优化TTS文本并返回完整响应
     *
     * @param text 原始文本
     * @return 完整的响应对象
     */
    public String optimizeTextWithFullResponse(String text) {
        if (StrUtil.isEmpty(text)) {
            log.warn("输入文本为空，跳过TTS优化");
            return null;
        }
        // 构建请求对象
        JSONObject object = JSONObject.parseObject(request_body);
        object.getJSONObject("inputs").put("text", text);
        // 转换为JSON
        String requestJson = object.toJSONString();
        long startTime = System.currentTimeMillis();
        log.info("TTS优化接口请求参数：{}", requestJson);

        // 创建HTTP请求
        HttpRequest httpRequest = HttpUtil.createPost(optimizationUrl)
                .header("Authorization", authToken)
                .header("Content-Type", "application/json;charset=utf-8")
                .header("Accept-Charset", "UTF-8")
                .body(requestJson);
        // 发送请求
        try (HttpResponse httpResponse = httpRequest.execute()) {
            String responseBody = httpResponse.body();
            log.info("TTS优化接口响应：{},耗时：{} ms", responseBody, System.currentTimeMillis() - startTime);

            if (!httpResponse.isOk()) {
                log.error("TTS优化接口请求失败，状态码：{}, 响应：{}", httpResponse.getStatus(), responseBody);
                return null;
            }

            // 解析响应
            JSONObject response = JSONObject.parseObject(responseBody);
            if (response == null) {
                log.error("TTS优化接口响应解析失败");
                return null;
            }
            JSONObject data = response.getJSONObject("data");

            if (data == null || !"succeeded".equals(data.getString("status"))) {
                log.warn("TTS优化任务执行失败,错误信息：{}", response);
                return null;
            }
            JSONObject outputs = data.getJSONObject("outputs");
            if (outputs == null) {
                log.warn("TTS优化任务执行失败,错误信息：{}", response);
                return null;
            }
            return outputs.getString("text");
        } catch (Exception ex) {
            log.error("TTS优化接口请求失败",ex);
            return null;
        }
    }


    /**
     * 测试方法
     */
    public static void main(String[] args) {
        TTSOptimizationTool tool = new TTSOptimizationTool();
        tool.optimizationUrl = "http://192.168.33.174/v1/workflows/run";
        tool.authToken = "Bearer app-zpaCirB7XVa9qmVdEY0nt814";

        String testText = "上个月/我参加了国寿海外的战略交流会。/作为保险老手/我挖出了分红的真相！/数据说话/连续十年分红实现率98.5%！/十次分红/九次半都能稳稳拿到手。/2023年分红率6.4%/比行业平均高出2个百分点。/秘密就在这'全球三重风控体系'：/40%资金押注欧美国债/安全垫十足；/30%投入亚太不动产/实在又靠谱；/30%布局高分红蓝筹股/精准锁定收益。/地域风险？彻底分散得干干净净！/接下来看傲珑创富产品：/它把保险和信托合二为一/年化分红稳在5.9%到6.3%/保底利率3.5%打底/外加八大增值服务全包圆。/去年我帮一户三口之家配了80万/三年后分红12.8万/孩子教育基金轻松搞定。/朋友们注意了/大家总追高收益/却忘了安全才是王道。/国寿海外靠啥稳？央企1.2万亿资产撑腰！/风控回撤率低于2%/稳如泰山不晃悠。/傲珑创富账户透明/分红随时可查一目了然。/我建议普通家庭/把15%资产放这儿/生活刚需保住了/财富还能稳稳增值。/请务必记住/复利是时间的朋友/暴利是风险的赌徒！/真正的财务安全/源于细水长流的积累。/立即定制专属规划方案/让资产安全增长。";
        String optimizedText = tool.optimizeText(testText);

        System.out.println("原始文本：" + testText);
        System.out.println("优化后文本：" + optimizedText);
    }
}
