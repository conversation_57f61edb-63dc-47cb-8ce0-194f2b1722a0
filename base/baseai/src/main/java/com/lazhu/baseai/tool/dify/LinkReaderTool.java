package com.lazhu.baseai.tool.dify;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * 链接读取工具类
 * 用于调用链接读取接口，根据链接生成摘要
 */
@Slf4j
@Component
public class LinkReaderTool {

    @Value("${ai.link_read_url:}")
    private String url;

    /**
     * @param link 链接
     */
    public String readContent(String link) {
        if (StrUtil.isEmpty(link)) {
            return StrUtil.EMPTY;
        }

        // 创建HTTP请求
        String responseBody = HttpUtil.post(url+"?url="+URLEncoder.encode(link,StandardCharsets.UTF_8)+"&parse_img=false","");
        log.info("链接爬取接口响应：{}", responseBody);
        // 解析响应
        JSONObject response = JSONObject.parseObject(responseBody);

        if (response == null) {
            log.error("链接爬取接口响应解析失败");
            return null;
        }
        String code = response.getString("code");
        if (!"200".equals(code)) {
            log.error("链接爬取接口请求失败，错误码：{}, 错误信息：{}", code, response.getString("msg"));
            return null;
        }
        return response.getString("data");
    }

    /**
     * 测试方法
     */
    public static void main(String[] args) {
        LinkReaderTool tool = new LinkReaderTool();
        tool.url = "http://192.168.33.174:8082/articles/wechat";

        String data = tool.readContent("https://mp.weixin.qq.com/s/KqG00M0XzH3pCgQo2g2ZiA?mpshare=1&scene=1&srcid=08048sL4bXIqzw2eobDPVPbK&sharer_shareinfo=9cf94f7e1f2601813bc94d4f2e17e3dd&sharer_shareinfo_first=9cf94f7e1f2601813bc94d4f2e17e3dd&color_scheme=light%23rd");
        System.out.println(data);
    }
}