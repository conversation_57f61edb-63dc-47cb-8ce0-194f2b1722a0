package com.lazhu.baseai.tool.dify;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 提示词工具类
 */
@Slf4j
@Component
public class PromptUtil {

    @Value("${ai.url:}")
    private  String url;
    @Value("${ai.prompt_create_auth:}")
    private  String auth;


    private static final String REQUEST_BODY = """
            {
            	"inputs": {},
            	"query": "",
            	"response_mode": "blocking",
            	"conversation_id": "",
            	"user": "person_prompt_ai"
            }
            """;

    /**
     * 生成提示词
     *
     * @param profile 提示词描述
     */
    public  String createPrompt(String profile) {
        HttpRequest post = HttpUtil.createPost(url);
        //设置请求头
        post.header("Authorization", auth);
        JSONObject requestBody = JSONObject.parseObject(REQUEST_BODY);
        requestBody.put("query", profile);
        post.body(requestBody.toJSONString());
        log.info("dify提示词优化接口请求参数：{}", requestBody.toJSONString());
        //发起请求
        JSONObject response;
        try (HttpResponse rehttpResponse = post.execute()) {
            String body = rehttpResponse.body();
            log.info("dify提示词优化接口响应参数：{}", body);
            response = JSONObject.parseObject(body);
        } catch (Exception e) {
            log.error("dify提示词优化接口请求异常", e);
            throw new RuntimeException(e);
        }
        return response.getString("answer");
    }
}