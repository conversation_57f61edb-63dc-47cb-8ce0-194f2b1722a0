package com.lazhu.baseai.tool.audio;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;

import com.alibaba.dashscope.audio.ttsv2.SpeechSynthesisAudioFormat;
import com.alibaba.dashscope.audio.ttsv2.SpeechSynthesisParam;
import com.alibaba.dashscope.audio.ttsv2.SpeechSynthesizer;
import com.alibaba.dashscope.audio.ttsv2.enrollment.Voice;
import com.alibaba.dashscope.audio.ttsv2.enrollment.VoiceEnrollmentService;
import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;

public class CosyVoiceTool {

	private static String appkey = "sk-176004f306a44a24b234ce3992db9c9e";

	private static VoiceEnrollmentService service = new VoiceEnrollmentService(appkey);

	private static String MODEL_NAME = "cosyvoice-v2";

	public static String createVoice(String name, String url) throws NoApiKeyException, InputRequiredException {
		Voice myVoice = service.createVoice(MODEL_NAME, name, url);
		return myVoice.getVoiceId();
	}

	public static AudioInfo createAudio(int seq, String voiceId, String word, String fileName) {
		// 请求参数
		SpeechSynthesisParam param = SpeechSynthesisParam.builder()
				// 若没有将API Key配置到环境变量中，需将下面这行代码注释放开，并将your-api-key替换为自己的API Key
				.apiKey(appkey).model(MODEL_NAME) // 模型
				.voice(voiceId) // 音色
				.format(SpeechSynthesisAudioFormat.MP3_44100HZ_MONO_256KBPS).build();

		// 同步模式：禁用回调（第二个参数为null）
		SpeechSynthesizer synthesizer = new SpeechSynthesizer(param, null);
		// 阻塞直至音频返回
		ByteBuffer audio = synthesizer.call(word);
		// 将音频数据保存到本地文件中
		File file = new File(fileName);
		try (FileOutputStream fos = new FileOutputStream(file)) {
			fos.write(audio.array());
			long dur = Mp3DurationReader.getMillDuration(file);
			String s = TxtSplitter.extractTextFromSpeakContent(word);
			return new AudioInfo(seq, s, dur, synthesizer.getFirstPackageDelay());
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}

	public record AudioInfo(int seq, String txt, long timeLong, Long firstDelay) {
	};

	public static void main(String[] args) {
		// 请求参数
		SpeechSynthesisParam param = SpeechSynthesisParam.builder()
				// 若没有将API Key配置到环境变量中，需将下面这行代码注释放开，并将your-api-key替换为自己的API Key
				.apiKey(appkey).model(MODEL_NAME) // 模型
				.voice("longxiaochun_v2") // 音色
				.format(SpeechSynthesisAudioFormat.MP3_44100HZ_MONO_256KBPS).build();

		// 同步模式：禁用回调（第二个参数为null）
		SpeechSynthesizer synthesizer = new SpeechSynthesizer(param, null);
		// 阻塞直至音频返回
		ByteBuffer audio = synthesizer.call(
				"知识图谱");
		// 将音频数据保存到本地文件中
		File file = new File("d:/test/ts.mp3");
		try (FileOutputStream fos = new FileOutputStream(file)) {
			fos.write(audio.array());
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
		System.err.println("1");
	}
}
