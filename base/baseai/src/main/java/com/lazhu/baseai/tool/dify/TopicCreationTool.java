package com.lazhu.baseai.tool.dify;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 选题创作工具类
 * 用于调用选题创作接口，根据热点数据和简介生成选题建议
 */
@Slf4j
@Component
public class TopicCreationTool {

    /**
     * 选题创作接口URL
     */
    @Value("${ai.url:}")
    private String creationUrl;

    /**
     * 认证token
     */
    @Value("${ai.topic_creation_auth:}")
    private String authToken;

    private static final String REQUEST_BODY = """
            {
                "inputs": {
                    "hot_data": "",
                    "profile": ""
                },
                "query": "生成几个选题",
                "files": [],
                "response_mode": "blocking",
                "user": "lz_aigc"
            }
            """;

    /**
     * 创作选题
     *
     * @param hotData 热点数据
     * @param profile 简介
     * @return 选题列表，取响应参数的answer字段
     */
    public List<String> createTopics(String hotData, String profile) {
        List<String> response = createTopicsWithFullResponse(hotData, profile);
        if (CollUtil.isNotEmpty(response)) {
            return response;
        }
        log.warn("选题创作失败，返回空列表");
        return CollUtil.newArrayList(); // 如果创作失败，返回空列表
    }

    /**
     * 创作选题并返回完整响应
     *
     * @param hotData 热点数据
     * @param profile 简介
     * @return 选题列表
     */
    public List<String> createTopicsWithFullResponse(String hotData, String profile) {
        if (StrUtil.isEmpty(hotData)) {
            log.warn("热点数据为空，跳过选题创作");
            return null;
        }

        // 构建请求对象
        JSONObject object = JSONObject.parseObject(REQUEST_BODY);
        JSONObject inputs = object.getJSONObject("inputs");
        inputs.put("hot_data", hotData);
        inputs.put("profile", profile);

        // 转换为JSON
        String requestJson = object.toJSONString();
        log.info("选题创作接口请求参数：{}", requestJson);

        // 创建HTTP请求
        HttpRequest httpRequest = HttpUtil.createPost(creationUrl)
                .header("Authorization", authToken)
                .header("Content-Type", "application/json")
                .body(requestJson);

        // 发送请求
        try (HttpResponse httpResponse = httpRequest.execute()) {
            String responseBody = httpResponse.body();
            log.info("选题创作接口响应：{}", responseBody);

            if (!httpResponse.isOk()) {
                log.error("选题创作接口请求失败，状态码：{}, 响应：{}", httpResponse.getStatus(), responseBody);
                return null;
            }

            // 解析响应
            JSONObject response = JSONObject.parseObject(responseBody);
            if (response == null) {
                log.error("选题创作接口响应解析失败");
                return null;
            }

            // 获取answer字段，假设返回的是字符串数组
            String answerStr = response.getString("answer");
            if (StrUtil.isEmpty(answerStr)) {
                log.warn("响应中未找到answer字段");
                return null;
            }

            // 如果answer是JSON数组格式的字符串，解析为List
            try {
                return JSONObject.parseArray(answerStr, String.class);
            } catch (Exception e) {
                log.warn("answer字段不是JSON数组格式，尝试按换行符分割");
                // 如果不是JSON数组，尝试按换行符分割
                String[] lines = answerStr.split("\n");
                return CollUtil.newArrayList(lines);
            }

        } catch (Exception ex) {
            log.error("选题创作接口请求失败", ex);
            return null;
        }
    }

    /**
     * 测试方法
     */
    public static void main(String[] args) {
        TopicCreationTool tool = new TopicCreationTool();
        tool.creationUrl = "http://192.168.33.174/v1/chat-messages";
        tool.authToken = "Bearer app-DGDyM6AgXvpO2MTyxwWgkoMg";

        String testHotData = "最新热点：人工智能技术在教育领域的应用越来越广泛，ChatGPT等工具正在改变传统的学习方式。";
        String testProfile = "科技教育领域专业分析师，专注于AI技术在教育行业的应用研究。";

        List<String> topicList = tool.createTopics(testHotData, testProfile);

        System.out.println("热点数据：" + testHotData);
        System.out.println("简介：" + testProfile);
        System.out.println("创作选题：" + topicList);
    }
}