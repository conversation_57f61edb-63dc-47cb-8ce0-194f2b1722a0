package com.lazhu.baseai.vector;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lazhu.common.enums.MediaAssetTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * Ai图片与文件识别
 * <AUTHOR>
 * @param[1] null
 * @time 2025/8/12 17:43
 */
@Slf4j
@Component
public class IdentifyTool {


    @Value("${ai.identify_url:}")
    private String identifyUrl;

    @Value("${ai.identify_token:}")
    private String identifyToken;


    private static final String request_body = """
            {
                "model": "qwen-vl-plus",
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": ""
                                },
                                "video_url":{
                                    "url": ""
                                }
                            },
                            {
                                "type": "text",
                                "text": "给此图片取一个描述"
                            }
                        ]
                    }
                ]
            }
            """;



    /**
     * 获取视频或图片描述
     * <AUTHOR>
     * @param[1] profile
     * @param[2] mediaAssetType
     * @return String
     * @time 2025/8/13 9:15
     */
    public  String createIdentify(String profile,String mediaAssetType) {
        try {
            // 构建请求参数
            JSONObject request = buildRequest(profile, mediaAssetType);
            String requestJson = JSONObject.toJSONString(request);

            // 发送HTTP请求
            HttpRequest httpRequest = HttpUtil.createPost(identifyUrl)
                    .header("Authorization", identifyToken)
                    .header("Content-Type", "application/json")
                    .body(requestJson);

            String responseBody;
            try (HttpResponse response = httpRequest.execute()) {
                responseBody = response.body();
            }
            log.info("获取视频或图片描述：{}", responseBody);
            // 解析响应
            return parseResponse(responseBody);

        } catch (Exception e) {
            log.error("获取视频或图片描述异常", e);
            throw e;
        }
    }

    /**
     * 构建请求对象
     */
    private JSONObject buildRequest(String profile, String mediaAssetType) {
        JSONObject object = JSONObject.parseObject(request_body);
        JSONArray messages = object.getJSONArray("messages");
        JSONObject jsonObject = messages.getJSONObject(0);
        JSONArray content = jsonObject.getJSONArray("content");
        JSONObject contentObject = content.getJSONObject(0);
        JSONObject typeObject = content.getJSONObject(1);
        if(MediaAssetTypeEnum.IMAGE.getType().equals(mediaAssetType)){
            object.put("model","qwen-vl-plus");
            typeObject.put("text", "给此图片一个简短的描述");
            JSONObject imageUrl = contentObject.getJSONObject("image_url");
            contentObject.put("type","image_url");
            imageUrl.put("url",profile);
        }else if(MediaAssetTypeEnum.VIDEO.getType().equals(mediaAssetType)){
            object.put("type","qwen-vl-max-latest");
            typeObject.put("text", "给此视频一个简短的描述");
            JSONObject videoUrl = contentObject.getJSONObject("video_url");
            contentObject.put("type","video_url");
            videoUrl.put("url",profile);
        }
        return object;
    }

    /**
     * 解析响应结果
     */
    private String parseResponse(String responseBody) {
        if (StrUtil.isBlank(responseBody)) {
            throw new RuntimeException("获取视频或图片描述接口响应为空");
        }
        JSONObject response = JSONObject.parseObject(responseBody);
        JSONArray choices = response.getJSONArray("choices");
        if(choices != null && !choices.isEmpty()){
            JSONObject jsonObject = choices.getJSONObject(0);
            JSONObject message = jsonObject.getJSONObject("message");
            return message.getString("content");
        }else {
            throw new RuntimeException("获取视频或图片描述接口响应数据为空");
        }
    }
}
