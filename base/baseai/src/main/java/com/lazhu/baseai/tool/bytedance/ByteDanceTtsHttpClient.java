package com.lazhu.baseai.tool.bytedance;

import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.util.Base64;
import java.util.UUID;

@Slf4j
public class ByteDanceTtsHttpClient extends BaseByteDanceClient {

    public static final String API_URL = HOST + "/api/v1/tts";


    /**
     * 语音合成 plain
     *
     * @param text      文本内容
     * @param speakerId 声音ID
     * @return 音频文件路径
     */
    public static void ttsPlain(String text, String speakerId, String fileName) throws IOException {
        TtsRequest ttsRequest = new TtsRequest(text, speakerId, "plain");
        String s = executePostRequest(API_URL, ttsRequest);
        base64ToFile(parseResponse(s), fileName);
    }

    /**
     * 语音合成 ssml
     *
     * @param ssml      ssml内容
     * @param speakerId 声音ID
     * @return 音频文件路径
     */
    public static void ttsSsml(String ssml, String speakerId, String fileName) throws IOException {
        TtsRequest ttsRequest = new TtsRequest(ssml, speakerId, "ssml");
        long startTime = System.currentTimeMillis();
        log.info("开始合成语音,ssml={},speakerId:{},fileName:{}", ssml, speakerId, fileName);
        String s = executePostRequest(API_URL, ttsRequest);
        long endTime = System.currentTimeMillis();
        log.info("合成语音完成,ssml={},speakerId:{},fileName:{},response:{},cost:{}ms", ssml, speakerId, fileName, s, endTime - startTime);
        base64ToFile(parseResponse(s), fileName);
    }


    /**
     * 解析响应
     */
    private static String parseResponse(String response) {
        JSONObject jsonObject = JSON.parseObject(response);
        String code = jsonObject.getString("code");
        // 成功
        if ("3000".equals(code)) {
            // 获取data
            return jsonObject.getString("data");
        } else {
            // 失败
            String errorMsg = "bytedance 语音合成失败:" + code + "," + jsonObject.getString("message");
            throw new RuntimeException(errorMsg);
        }
    }

    private static void base64ToFile(String base64, String fileName) {
        boolean exist = FileUtil.exist(fileName);
        if (exist) {
            FileUtil.del(fileName);
        }
        byte[] audioBytes = Base64.getDecoder().decode(base64);
        FileUtil.writeBytes(audioBytes, new File(fileName));
    }

    @Setter
    @Getter
    public static class TtsRequest {
        public static final String CLUSTER = "volcano_icl";


        /**
         * @param text      文本内容
         * @param speakerId 声音ID
         * @param textType  文本类型 plain/ssml，默认ssml
         */
        public TtsRequest(String text, String speakerId, String textType) {
            this.request.text = text;
            this.audio = new Audio(speakerId);
            this.request.text_type = textType;
        }

        private App app = new App();
        private User user = new User();
        private Audio audio;

        private Request request = new Request();

        @Setter
        @Getter
        public static class App {
            private String appid = APPID;
            private String token = "access_token"; // 目前未生效，填写默认值：access_token
            private String cluster = CLUSTER;
        }

        @Setter
        @Getter
        public static class User {
            private String uid = "388808087185088"; // 目前未生效，填写一个默认值就可以
        }

        @Setter
        @Getter
        public static class Audio {
            private String voice_type = "";
            private String encoding = "mp3";
            private float speed_ratio = 1.0f;
            private float volume_ratio = 10;
            private float pitch_ratio = 10;
            private String emotion = "happy";

            public Audio(String voice_type) {
                this.voice_type = voice_type;
            }
        }

        @Setter
        @Getter
        public static class Request {
            private String reqid = UUID.randomUUID().toString();
            private String text;
            private String text_type = "ssml";
            private String operation = "query";
        }
    }

    // 示例用法
    public static void main(String[] args) throws IOException {
//        String SSMLTxt = """
//                <speak><break time="400ms"/></speak>
//                <speak rate="1.05" volume="52"><break time="400ms"/>这两年，继盲盒火爆之后，盲卡靠着"未知惊喜"这个卖点，迅速在儿童和青少年群体里火了起来。</speak>
//                <speak><break time="300ms"/></speak>
//                <speak rate="1" volume="50">不少孩子出现了非理性消费的现象，</speak>
//                <speak rate="1.1" volume="54">有的为了抽到稀有卡牌，一次就花掉几百元，甚至上千元。</speak>
//                """;
        String SSMLTxt = """
                 <speak rate="1.05" volume="52">这两年，继盲盒火爆之后，盲卡靠着"未知惊喜"这个卖点，迅速在儿童和青少年群体里火了起来。</speak>
                """;
        ttsSsml(SSMLTxt, "S_taXQZzeD1", "D://test.mp3");
    }

}