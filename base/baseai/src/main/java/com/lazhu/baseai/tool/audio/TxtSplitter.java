package com.lazhu.baseai.tool.audio;

import cn.hutool.core.util.StrUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class TxtSplitter {

    /**
     * 将字符串按标点符号拆分成数组（保留标点符号）
     *
     * @param input 输入字符串
     * @return 拆分后的字符串数组
     */
    public static String[] splitByPunctuation(String input) {
        if (input == null || input.isEmpty()) {
            return new String[0];
        }

        // 正则表达式：匹配标点符号或非标点序列
        // \\p{Punct} 匹配任何英文标点：!"#$%&'()*+,-./:;<=>?@[\]^_`{|}~
        // \\p{IsPunctuation} 匹配任何Unicode标点（包括中文标点）
        String regex = "(?<=\\p{Punct}|\\p{IsPunctuation})|(?=\\p{Punct}|\\p{IsPunctuation})";

        // 处理连续标点符号的情况
        List<String> result = new ArrayList<>();
        String[] temp = input.split(regex);

        for (String s : temp) {
            if (!s.trim().isEmpty()) {
                result.add(s);
            }
        }

        return result.toArray(new String[0]);
    }

    /**
     * 将字符串按标点符号拆分成数组（可选择是否保留标点）
     *
     * @param input           输入字符串
     * @param keepPunctuation 是否保留标点符号
     * @return 拆分后的字符串数组
     */
    public static String[] splitByPunctuation(String input, boolean keepPunctuation) {
        if (!keepPunctuation) {
            // 不保留标点：使用标点作为分隔符
            return input.split("[\\p{Punct}\\p{IsPunctuation}]+");
        }
        return splitByPunctuation(input);
    }

    /**
     * 将字符串按句子拆分（以句末标点分割）
     *
     * @param input 输入字符串
     * @return 句子数组
     */
    public static String[] splitIntoSentences(String input) {
        if (input == null || input.isEmpty()) {
            return new String[0];
        }

        // 匹配句末标点：.?!。？！等
        String regex = "(?<=[.!?。？！])";
        String[] sentences = input.split(regex);

        // 移除空句子
        List<String> result = new ArrayList<>();
        for (String s : sentences) {
            if (!s.trim().isEmpty()) {
                result.add(s.trim());
            }
        }

        return result.toArray(new String[0]);
    }

    /**
     * 解析SSML语言，提取speak标签中的文本内容
     *
     * @param ssmlContent SSML格式的字符串
     * @return speak标签中的文本内容列表
     */
    public static List<String> parseSsmlSpeakTexts(String ssmlContent) {
        List<String> speakTexts = new ArrayList<>();

        if (ssmlContent == null || ssmlContent.trim().isEmpty()) {
            return speakTexts;
        }

        String[] split = ssmlContent.split("\\n");
        String temp = "";
        for (String line : split) {
            String s = extractTextFromSpeakContent(line);
            if (StrUtil.isNotBlank(s)) {
                speakTexts.add(temp + line);
                temp = "";
            } else {
                //内容为空是断句，继续循环
                temp = temp+line;
            }
        }
        return speakTexts;
    }

    /**
     * 从speak标签内容中提取纯文本，去除其他XML标签
     *
     * @param speakContent speak标签内的内容
     * @return 提取的纯文本
     */
    public static String extractTextFromSpeakContent(String speakContent) {
        if (speakContent == null) {
            return "";
        }

        // 去除所有XML标签，只保留文本内容
        // 匹配 <标签名...> 或 </标签名> 格式的标签
        String textOnly = speakContent.replaceAll("<[^>]+>", "");

        // 去除多余的空白字符，但保留必要的空格
        textOnly = textOnly.replaceAll("\\s+", " ");

        return textOnly.trim();
    }

    /**
     * 智能文本拆分方法，自动判断输入类型并选择合适的拆分方式
     * 如果是SSML格式，使用parseSsmlSpeakTexts方法拆分
     * 如果是普通文本，使用splitIntoSentences方法拆分
     *
     * @param input 输入文本（可能是SSML或普通文本）
     * @return 拆分后的文本数组
     */
    public static String[] smartSplit(String input) {
        if (input == null || input.trim().isEmpty()) {
            return new String[0];
        }

        // 判断是否为SSML格式
        if (isSsmlFormat(input)) {
            // 使用SSML解析方法
            List<String> speakTexts = parseSsmlSpeakTexts(input);
            return speakTexts.toArray(new String[0]);
        } else {
            // 使用普通句子拆分方法
            return splitIntoSentences(input);
        }
    }

    /**
     * 判断输入文本是否为SSML格式
     *
     * @param input 输入文本
     * @return true表示是SSML格式，false表示是普通文本
     */
    private static boolean isSsmlFormat(String input) {
        if (input == null || input.trim().isEmpty()) {
            return false;
        }

        // 检查是否包含speak标签
        Pattern speakPattern = Pattern.compile("<speak[^>]*>.*?</speak>", Pattern.DOTALL);
        Matcher speakMatcher = speakPattern.matcher(input);

        // 如果找到speak标签，则认为是SSML格式
        return speakMatcher.find();
    }

    // 测试
    public static void main(String[] args) {
//		String text = "你好，世界！这是一个示例。测试多种标点：如！、？；... 结束";
//
//		System.out.println("包含标点的拆分:");
//		for (String s : splitByPunctuation(text)) {
//			System.out.println("[" + s + "]");
//		}
//
//		System.out.println("\n不包含标点的拆分:");
//		for (String s : splitByPunctuation(text, false)) {
//			System.out.println("[" + s + "]");
//		}
//
//		System.out.println("\n按句子拆分:");
//		for (String s : splitIntoSentences(text)) {
//			System.out.println("[" + s + "]");
//		}

        // 测试SSML解析
        String ssmlText = """
                <speak><break time="400ms"/></speak>
                <speak rate="1.05" volume="52">这两年，继盲盒火爆之后，盲卡靠着“未知惊喜”这个卖点，</speak>
                <speak rate="1" volume="50">迅速在儿童和青少年群体里火了起来。</speak>
                <speak><break time="300ms"/></speak>
                <speak rate="0.95" volume="48">可随之而来的，是非理性消费现象越来越常见。</speak>
                <speak rate="1" volume="50">有些孩子为了抽到稀有卡牌，一次就花几百块，甚至上千块钱。</speak>
                """;

        System.out.println("\nSSML解析结果:");
        List<String> speakTexts = parseSsmlSpeakTexts(ssmlText);
        for (int i = 0; i < speakTexts.size(); i++) {
            System.out.println((i + 1) + ". [" + speakTexts.get(i) + "]");
        }
        for(String s:speakTexts){
            System.out.println(extractTextFromSpeakContent(s));
        }
    }
}