package com.lazhu.baseai.tool.bytedance;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

/**
 * MegaTTS 语音克隆工具类
 * 提供音频上传训练和状态查询功能
 */
public class ByteDanceMegaTtsClient extends BaseByteDanceClient {

    public static final String API_URL = HOST + "/api/v1/mega_tts/audio/upload";
    public static final String API_URL_STATUS = HOST + "/api/v1/mega_tts/status";

    /**
     * 训练接口 - 上传音频文件进行声音克隆训练
     *
     * @param audioPath 音频文件路径
     * @param spkId     说话人ID
     * @throws Exception 请求异常
     */
    public static void train(String audioPath, String spkId) throws Exception {

        // 编码音频文件
        AudioData audioData = encodeAudioFile(audioPath);

        // 构建请求体
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("appid", APPID);
        requestBody.put("speaker_id", spkId);
        requestBody.put("source", 2);
        requestBody.put("language", 0);
        requestBody.put("model_type", 1);

        // 音频数据
        List<Map<String, String>> audios = new ArrayList<>();
        Map<String, String> audio = new HashMap<>();
        audio.put("audio_bytes", audioData.encodedData);
        audio.put("audio_format", audioData.format);
        audios.add(audio);
        requestBody.put("audios", audios);

        // 额外参数（可选）
        Map<String, Object> extraParams = new HashMap<>();

        // 发送请求
        String response = executePostRequest(API_URL, requestBody, "volc.megatts.voiceclone");
        System.out.println(response);
    }

    /**
     * 获取训练状态
     *
     * @param spkId 说话人ID
     * @throws Exception 请求异常
     */
    public static void getStatus(String spkId) throws Exception {

        // 构建请求体
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("appid", APPID);
        requestBody.put("speaker_id", spkId);

        // 发送请求
        String response = executePostRequest(API_URL_STATUS, requestBody, "volc.megatts.voiceclone");
        System.out.println(response);
    }

    /**
     * 编码音频文件为Base64格式
     *
     * @param filePath 音频文件路径
     * @return 编码后的音频数据
     * @throws IOException 文件读取异常
     */
    private static AudioData encodeAudioFile(String filePath) throws IOException {
        Path path = Paths.get(filePath);
        byte[] audioBytes = Files.readAllBytes(path);
        String encodedData = Base64.getEncoder().encodeToString(audioBytes);

        // 获取文件扩展名作为音频格式
        String fileName = path.getFileName().toString();
        String format = "";
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            format = fileName.substring(lastDotIndex + 1);
        }

        return new AudioData(encodedData, format);
    }

    /**
     * 音频数据内部类
     */
    private record AudioData(String encodedData, String format) {
    }

    /**
     * 示例用法
     */
    public static void main(String[] args) {
        try {
            String spkId = "S_taXQZzeD1";
            String audioPath = "C:\\Users\\<USER>\\Downloads\\1755499106832741809.mp3";

            // 训练
            ByteDanceMegaTtsClient.train( audioPath, spkId);

            // 查询状态
            ByteDanceMegaTtsClient.getStatus(spkId);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}