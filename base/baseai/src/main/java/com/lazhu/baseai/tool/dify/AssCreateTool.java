package com.lazhu.baseai.tool.dify;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * ASS字幕文件工具类
 * 基于AI接口生成ASS格式字幕文件
 */
@Service
@Slf4j
public class AssCreateTool {

    @Value("${ai.workflow_url:}")
    private String assCreateUrl;

    @Value("${ai.ass_create_token:}")
    private String assCreateToken;


    private static final String request_body = """
            {
               "inputs": {
                  "ass": "",
                  "title": "",
                  "speaker":"",
                  "srt":""
               },
               "response_mode": "blocking",
               "user": "abc-123"
            }
            """;

    /**
     * 创建ASS字幕文件
     *
     * @param title       视频标题
     * @param speaker     演讲者名称
     * @param srt         字幕文件内容
     * @param assTemplate ASS模板内容
     * @param height       视频高度
     * @param width       视频宽度
     * @return 生成的ASS字幕内容，失败返回null
     */
    public String createAss(String title, String speaker, String srt, String assTemplate,int width,int height) {

        try {
            // 构建请求参数
            JSONObject request = buildRequest(title, speaker, srt, assTemplate,width,height);
            String requestJson = JSONObject.toJSONString(request);
            log.info("ASS字幕生成接口请求参数：{}", requestJson);

            // 发送HTTP请求
            HttpRequest httpRequest = HttpUtil.createPost(assCreateUrl)
                    .header("Authorization", assCreateToken)
                    .header("Content-Type", "application/json")
                    .body(requestJson);

            String responseBody;
            try (HttpResponse response = httpRequest.execute()) {
                responseBody = response.body();
            }
            log.info("ASS字幕生成接口响应：{}", responseBody);
            // 解析响应
            return parseResponse(responseBody);

        } catch (Exception e) {
            log.error("ASS字幕生成异常", e);
            throw e;
        }
    }


    /**
     * 构建请求对象
     */
    private JSONObject buildRequest(String title, String speaker, String srt, String assTemplate,int width, int height) {
        JSONObject object = JSONObject.parseObject(request_body);

        JSONObject inputs = object.getJSONObject("inputs");
        inputs.put("title", title);
        inputs.put("speaker", speaker);
        inputs.put("srt", srt);
        inputs.put("width", width);
        inputs.put("height", height);

        inputs.put("ass", assTemplate);

        return object;
    }

    /**
     * 解析响应结果
     */
    private String parseResponse(String responseBody) {
        if (StrUtil.isBlank(responseBody)) {
            throw new RuntimeException("ASS字幕生成接口响应为空");
        }
        JSONObject response = JSONObject.parseObject(responseBody);

        // 检查响应状态
        JSONObject data = response.getJSONObject("data");
        if (data == null) {
            throw new RuntimeException("ASS字幕生成接口响应数据为空");
        }

        String status = data.getString("status");
        if (!"succeeded".equals(status)) {
            String error = data.getString("error");
            throw new RuntimeException("ASS字幕生成失败，状态："+status+"，错误信息："+error);
        }

        // 提取结果
        JSONObject outputs = data.getJSONObject("outputs");
        if (outputs == null) {
            throw new RuntimeException("ASS字幕生成接口返回结果为空");
        }
        return outputs.getString("output");
    }

    public static void main(String[] args) {
        AssCreateTool tool = new AssCreateTool();
        tool.assCreateUrl = "http://**************/v1/workflows/run";
        tool.assCreateToken = "Bearer app-QU8QaVFImSGf68Luk9xQbdjQ";

        String srt = """
                1
                00:00:01,000 --> 00:00:02,000
                测试
                """;
        String assTemplate = """
                [Script Info]
                ; Script generated by Aegisub 3.2.2
                ; http://www.aegisub.org/
                Title: Default Aegisub file
                ScriptType: v4.00+
                WrapStyle: 0
                ScaledBorderAndShadow
                """;

        String ass = tool.createAss("测试", "测试", "测试", assTemplate, 1080,1920);
        System.out.println(ass);
    }
}
