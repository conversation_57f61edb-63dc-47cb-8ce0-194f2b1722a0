package com.lazhu.baseai.tool.bytedance;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.volcengine.service.visual.IVisualService;
import com.volcengine.service.visual.impl.VisualServiceImpl;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.TimeUnit;

/**
 * 字节跳动对口型工具
 */
@Slf4j
public class ByteDanceVideoTalkClient {

    private static final String ACCESS_KEY = "AKLTNDUxYTYxNWZiZjEzNGZkNmI4ZjI0N2IxYzY1MjE2N2M";

    private static final String ACCESS_SECRET = "T0dVME5tVmpPR05tWldFMk5ESXdaV0ZsTkRRMU9XVmlOMkk1WVRSbVlUZw==";

    /**
     * 提交任务
     *
     * @param audioUrl 音频链接
     * @param videoUrl 视频链接
     * @return 任务id
     */
    public static String submitTask(String audioUrl, String videoUrl) throws Exception {
        IVisualService visualService = buildVisualService();
        // 请求body
        JSONObject req = new JSONObject();
        req.put("req_key", "realman_change_lips");
        req.put("url", videoUrl);
        req.put("pure_audio_url", audioUrl);
        req.put("align_audio", true);
        req.put("align_audio_reverse", true);
        log.info("字节对口型任务提交：{}", req);
        Object object = visualService.cvSubmitTask(req);
        log.info("字节对口型任务提交响应：{}", object);
        return ((JSONObject) object).getJSONObject("data").getString("task_id");
    }

    /**
     * 视频对口型任务查询
     *
     * @param taskId 任务id
     */
    public static TaskQueryResult taskQuery(String taskId) throws Exception {
        IVisualService visualService = buildVisualService();
        JSONObject req = new JSONObject();
        req.put("req_key", "realman_change_lips");
        req.put("task_id", taskId);
        log.info("字节对口型任务查询：{}", req);
        Object o = visualService.cvGetResult(req);
        log.info("字节对口型任务查询响应：{}", o);
        JSONObject resp = (JSONObject) o;
        return resp.getObject("data", TaskQueryResult.class);

    }


    private static IVisualService buildVisualService() {
        IVisualService visualService = VisualServiceImpl.getInstance();
        visualService.setAccessKey(ACCESS_KEY);
        visualService.setSecretKey(ACCESS_SECRET);
        return visualService;
    }


    /**
     * 任务查询结果
     */
    @Data
    public static class TaskQueryResult {

        /**
         * 合成结果
         * in_queue：任务已提交
         * generating：任务已被消费，处理中
         * done：处理完成，成功或者失败，可根据外层code&message进行判断
         * not_found：任务未找到，可能原因是无此任务或任务已过期(12小时)
         * expired：任务已过期，请尝试重新提交任务请求
         */
        private String status;

        @JSONField(name = "resp_data")
        private RespData respData;

        public boolean isDone() {
            return "done".equals(status);
        }

        public boolean isPending() {
            return "in_queue".equals(status);
        }

        public boolean isRunning() {
            return "generating".equals(status);
        }

        public boolean isFailed() {
            return "not_found".equals(status) || "expired".equals(status) || (isDone() && !"0".equals(getCode()));
        }

        public boolean isSuccess() {
            return isDone() && "0".equals(getCode());
        }

        public String getCode(){
            return respData != null ? respData.getCode() : null;
        }

        public String getMessage(){
            return respData != null ? respData.getMsg() : null;
        }
    }


    @Data
    public static class RespData {
        private String url;
        private String code;
        private String msg;
    }

    // 测试方法
    public static void main(String[] args) throws Exception {
        String audioUrl = "https://ins-file.lazhuyun.cn/test/video/1959942617257340930/audio_cut_precise_0.0s_to_89.271s_1756122327568.mp3";
        String videoUrl = "https://ins-file.lazhuyun.cn/test/video/1959942617257340930/video.mp4";
        String taskId = submitTask(audioUrl, videoUrl);

        long startTime = System.currentTimeMillis();
        TimeUnit.SECONDS.sleep(30);
        TaskQueryResult result;
        do {
            result = taskQuery(taskId);
            TimeUnit.SECONDS.sleep(30);
        } while (!result.isDone());

        long endTime = System.currentTimeMillis();

        log.info("视频合成查询结果:{},耗时：{}s", result, (endTime - startTime) / 1000);
    }

}
