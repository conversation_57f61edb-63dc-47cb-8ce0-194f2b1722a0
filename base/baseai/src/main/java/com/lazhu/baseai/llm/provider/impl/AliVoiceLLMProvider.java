package com.lazhu.baseai.llm.provider.impl;

import com.lazhu.baseai.tool.audio.CosyVoiceTool;
import com.lazhu.baseai.tool.dify.TTSOptimizationTool;
import com.lazhu.baseai.tool.audio.TxtToAudioService;
import cn.hutool.core.io.FileUtil;
import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.lazhu.baseai.llm.provider.VoiceLLMProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;

/**
 * 阿里云视频合成大模型提供者
 */
@Slf4j
@Service
public class AliVoiceLLMProvider implements VoiceLLMProvider {

    @Autowired
    private TTSOptimizationTool ttsOptimizationTool;

    @Override
    public String getProviderName() {
        return "aliyun";
    }

    @Override
    public String createVoice(String voiceName, String voiceUrl) {
        try {
            return CosyVoiceTool.createVoice(voiceName, voiceUrl);
        } catch (NoApiKeyException | InputRequiredException e) {
            log.info("阿里创建声音失败：{}，{}", voiceName, voiceUrl);
            throw new RuntimeException(e);
        }
    }

    @Override
    public String textToSpeech(String id, String text, String voiceId) {
        String folder = FileUtil.getTmpDirPath();
        if (!folder.endsWith(File.separator)) {
            folder = folder + File.separator;
        }
        folder = folder + "content_combine";
        TxtToAudioService.exec(id, folder, text, voiceId);
        //返回文件路径
        return folder + File.separator + id;
    }
}
