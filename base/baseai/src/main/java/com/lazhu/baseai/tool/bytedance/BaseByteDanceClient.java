package com.lazhu.baseai.tool.bytedance;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 字节跳动API基础客户端抽象类
 * 提供通用的HTTP请求方法和配置管理
 */
@Slf4j
public abstract class BaseByteDanceClient {

    // 通用配置常量
    protected static final String HOST = "https://openspeech.bytedance.com";
    protected static final String ACCESS_TOKEN = "iojXMDKeu1V6cwDwia8qVDL9xE6iQh3o";
    protected static final String APPID = "7879812815";

    // HTTP客户端和JSON处理器（单例模式）
    protected static final OkHttpClient HTTP_CLIENT = createHttpClient();
    protected static final ObjectMapper OBJECT_MAPPER = createObjectMapper();

    /**
     * 执行POST请求（通用方法）
     *
     * @param url         请求URL
     * @param requestBody 请求体对象
     * @param resourceId  资源ID（可选）
     * @return 响应字符串
     * @throws IOException 请求异常
     */
    protected static String executePostRequest(String url, Object requestBody, String resourceId) throws IOException {
        long startTime = System.currentTimeMillis();
        String jsonBody = OBJECT_MAPPER.writeValueAsString(requestBody);
        log.info("开始执行火山请求,url:{},requestBody:{},resourceId:{}", url, jsonBody, resourceId);

        RequestBody body = RequestBody.create(
                jsonBody,
                MediaType.parse("application/json")
        );

        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .header("Content-Type", "application/json")
                .header("Authorization", "Bearer;" + ACCESS_TOKEN)
                .post(body);

        // 添加可选的Resource-Id头
        if (resourceId != null && !resourceId.trim().isEmpty()) {
            requestBuilder.header("Resource-Id", resourceId);
        }

        Request request = requestBuilder.build();

        try (Response response = HTTP_CLIENT.newCall(request).execute()) {
            long endTime = System.currentTimeMillis();
            log.info("火山请求执行完成,url:{},request:{},response:{},耗时:{}ms", url, requestBody, response.body().toString(), endTime - startTime);
            if (response.isSuccessful()) {
                return response.body().string();
            }
            throw new IOException("HTTP请求失败: " + response.code() + " - " + response.message());
        }
    }

    /**
     * 执行POST请求（简化版，不带Resource-Id）
     */
    protected static String executePostRequest(String url, Object requestBody) throws IOException {
        return executePostRequest(url, requestBody, null);
    }

    protected static OkHttpClient createHttpClient() {
        return new OkHttpClient.Builder()
                .connectTimeout(60, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .build();
    }

    protected static ObjectMapper createObjectMapper() {
        return new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }
}