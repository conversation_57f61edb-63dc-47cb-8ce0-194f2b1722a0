package com.lazhu.baseai.tool.dify;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 热点搜索工具类
 * 用于调用热点搜索接口，根据关键词搜索热点话题
 */
@Slf4j
@Component
public class HotTopicSearchTool {

    /**
     * 选题创作接口URL
     */
    @Value("${ai.workflow_url:}")
    private String url;

    /**
     * 认证token
     */
    @Value("${ai.topic_search_auth:}")
    private String authToken;

    private static final String REQUEST_BODY = """
            {
                "inputs": {
                    "keys": ""
                },
                "response_mode": "blocking",
                "user": "abc-123"
            }
            """;

    /**
     * @param keyword 关键字
     */
    public List<String> search(List<String> keyword) {
        if (CollUtil.isEmpty(keyword)) {
            return CollUtil.newArrayList();
        }
        // 构建请求对象
        JSONObject object = JSONObject.parseObject(REQUEST_BODY);
        JSONObject inputs = object.getJSONObject("inputs");
        inputs.put("keys", JSONObject.toJSONString(keyword));

        // 转换为JSON
        String requestJson = object.toJSONString();
        log.info("热点搜索接口请求参数：{}", requestJson);

        // 创建HTTP请求
        HttpRequest httpRequest = HttpUtil.createPost(url)
                .header("Authorization", authToken)
                .header("Content-Type", "application/json")
                .body(requestJson);

        // 发送请求
        try (HttpResponse httpResponse = httpRequest.execute()) {
            String responseBody = httpResponse.body();
            log.info("热点搜索接口响应：{}", responseBody);

            if (!httpResponse.isOk()) {
                log.error("热点搜索接口请求失败，状态码：{}, 响应：{}", httpResponse.getStatus(), responseBody);
                return null;
            }

            // 解析响应
            JSONObject response = JSONObject.parseObject(responseBody);
            if (response == null) {
                log.error("热点搜索接口响应解析失败");
                return null;
            }

            JSONObject data = response.getJSONObject("data");
            if (data == null) {
                log.error("热点搜索接口响应解析失败");
                return null;
            }

            JSONObject jsonObject = data.getJSONObject("outputs");
            if (jsonObject == null) {
                log.error("热点搜索接口响应解析失败");
                return null;
            }
            JSONArray output = jsonObject.getJSONArray("output");
            if (output == null) {
                log.error("热点搜索接口响应解析失败");
                return null;
            }
            return output.toJavaList(String.class);

        } catch (Exception ex) {
            log.error("热点搜索接口请求失败", ex);
            return null;
        }
    }

    /**
     * 测试方法
     */
    public static void main(String[] args) {
        HotTopicSearchTool tool = new HotTopicSearchTool();
        tool.url = "http://192.168.33.174/v1/workflows/run";
        tool.authToken = "Bearer app-G3X7FPzYuZcZrjPkCl60t9KA";

        List<String> keys = CollUtil.newArrayList("宗馥莉香港保险信托", "香港法院判决宗馥莉");

        List<String> topicList = tool.search(keys);
        System.out.println(topicList);
    }
}