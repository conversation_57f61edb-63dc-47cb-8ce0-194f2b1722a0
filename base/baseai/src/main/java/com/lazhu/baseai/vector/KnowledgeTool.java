package com.lazhu.baseai.vector;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lazhu.common.enums.MediaAssetTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 知识库同步
 * <AUTHOR>
 * @time 2025/8/18 13:59
 */
@Slf4j
@Component
public class KnowledgeTool {

    @Value("${ai.knowledge_base_upload:}")
    private String knowledgeBaseUploadUrl;

    @Value("${ai.knowledge_base_update:}")
    private String knowledgeBaseUpdateUrl;

    @Value("${ai.knowledge_base_token:}")
    private String knowledgeBaseToken;

    /**
     * 图片
     */
    private static final String PIC_DOCUMENT_ID = "f02676ff-8110-42bd-85f2-f570b3805f2a";

    /**
     * 有声视频
     */
    private static final String VIDEO_HAS_AUDIO_ID = "66a315cb-a5f9-44b2-b0b1-8ceab5029f64";

    /**
     * 无声视频
     */
    private static final String VIDEO_NO_AUDIO_ID = "8c3590f7-d29e-4133-b4d2-36f747b921bc";


    /**
     * 获取视频或图片描述
     * <AUTHOR>
     * @param[1] profile
     * @param[2] mediaAssetType
     * @return String
     * @time 2025/8/13 9:15
     */
    public  String createKnowledge(String content,Integer height,Integer witdh,Long duration, String mediaAssetType,String hasAudio,String vectorId ) {
        try {
            // 构建请求参数
            JSONObject request = null;

            String documentId =null;
            if(MediaAssetTypeEnum.VIDEO.getType().equals(mediaAssetType)){
                if("1".equals(hasAudio)){
                    documentId=VIDEO_HAS_AUDIO_ID;
                }else {
                    documentId=VIDEO_NO_AUDIO_ID;
                }
            }else {
                documentId=PIC_DOCUMENT_ID;
            }
            String url = null;
            if(StringUtils.isBlank(vectorId)){
                url=String.format(knowledgeBaseUploadUrl,documentId);
                request=buildCreateRequest(content, height,witdh,duration);
            }else {
                url=String.format(knowledgeBaseUpdateUrl,documentId,vectorId);
                request=buildUpdateRequest(content, height,witdh,duration);
            }
            String requestJson = JSONObject.toJSONString(request);
            // 发送HTTP请求
            HttpRequest httpRequest = HttpUtil.createPost(url)
                    .header("Authorization", knowledgeBaseToken)
                    .header("Content-Type", "application/json")
                    .body(requestJson);

            String responseBody;
            try (HttpResponse response = httpRequest.execute()) {
                responseBody = response.body();
            }
            log.info("素材同步返回结果：{}", responseBody);
            // 解析响应
            if(StringUtils.isBlank(vectorId)){
                return parseCreateResponse(responseBody);
            }else {
                return parseUpdateResponse(responseBody);
            }


        } catch (Exception e) {
            log.error("素材同步返回结果异常", e);
            throw e;
        }
    }

    /**
     * 同步删除视频或图片描述
     * <AUTHOR>
     * @param[1] profile
     * @param[2] mediaAssetType
     * @return String
     * @time 2025/8/13 9:15
     */
    public  void deleteKnowledge( String mediaAssetType,String hasAudio,String vectorId ) {
        try {


            String documentId =null;
            if(MediaAssetTypeEnum.VIDEO.getType().equals(mediaAssetType)){
                if("1".equals(hasAudio)){
                    documentId=VIDEO_HAS_AUDIO_ID;
                }else {
                    documentId=VIDEO_NO_AUDIO_ID;
                }
            }else {
                documentId=PIC_DOCUMENT_ID;
            }
            String url = null;
            if(StringUtils.isBlank(vectorId)){
                url=String.format(knowledgeBaseUploadUrl,documentId);
            }else {
                url=String.format(knowledgeBaseUpdateUrl,documentId,vectorId);
            }
            // 发送HTTP请求
            HttpRequest httpRequest = HttpUtil.createRequest(Method.DELETE, url)
                    .header("Authorization", knowledgeBaseToken)
                    .header("Content-Type", "application/json");
            httpRequest.execute();

        } catch (Exception e) {
            log.error("素材同步返回删除异常", e);
            throw e;
        }
    }


    /**
     * 构建请求对象
     */
    private JSONObject buildCreateRequest(String content,Integer height,Integer width,Long duration) {

        JSONObject jsonObject =new JSONObject();
        jsonObject.put("content",content);
        jsonObject.put("height",height);
        jsonObject.put("width",width);
        if(ObjectUtil.isNotNull(duration)){
            jsonObject.put("duration",duration+"s");
        }
        JSONObject jsonObject2 =new JSONObject();
        jsonObject2.put("content",jsonObject.toJSONString());

        JSONArray jsonArray =new JSONArray();
        jsonArray.add(jsonObject2);

        JSONObject result =new JSONObject();
        result.put("segments",jsonArray);
        return result;
    }

    private JSONObject buildUpdateRequest(String content,Integer height,Integer width,Long duration) {

        JSONObject jsonObject =new JSONObject();
        jsonObject.put("content",content);
        jsonObject.put("height",height);
        jsonObject.put("width",width);
        if(ObjectUtil.isNotNull(duration)){
            jsonObject.put("duration",duration+"s");
        }
        JSONObject jsonObject2 =new JSONObject();
        jsonObject2.put("content",jsonObject.toJSONString());

        JSONObject result =new JSONObject();
        result.put("segment",jsonObject2);
        return result;
    }

    /**
     * 解析响应结果
     */
    private String parseCreateResponse(String responseBody) {
        if (StrUtil.isBlank(responseBody)) {
            throw new RuntimeException("素材同步返回结果为空");
        }
        JSONObject response = JSONObject.parseObject(responseBody);
        JSONArray data = response.getJSONArray("data");
        if(data != null && !data.isEmpty()){
            JSONObject jsonObject = data.getJSONObject(0);
            String id = jsonObject.getString("id");
            return id;
        }else {
            throw new RuntimeException("素材同步返回结果为空");
        }
    }

    private String parseUpdateResponse(String responseBody) {
        if (StrUtil.isBlank(responseBody)) {
            throw new RuntimeException("素材同步返回结果为空");
        }
        JSONObject response = JSONObject.parseObject(responseBody);
        JSONObject data = response.getJSONObject("data");
        if(data != null && !data.isEmpty()){
            String id = data.getString("id");
            return id;
        }else {
            throw new RuntimeException("素材同步返回结果为空");
        }
    }
}
