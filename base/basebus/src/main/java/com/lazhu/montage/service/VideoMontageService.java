package com.lazhu.montage.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lazhu.baseai.tool.dify.AssCreateTool;
import com.lazhu.baseai.tool.dify.VideoProcessCreateTool;
import com.lazhu.business.actor.entity.Actor;
import com.lazhu.business.actor.service.ActorService;
import com.lazhu.business.content.dto.ContentMetaData;
import com.lazhu.business.content.entity.Content;
import com.lazhu.business.content.service.ContentService;
import com.lazhu.business.mediaassets.entity.MediaAssetsImg;
import com.lazhu.business.mediaassets.entity.MediaAssetsVideo;
import com.lazhu.business.mediaassets.service.MediaAssetsImgService;
import com.lazhu.business.mediaassets.service.MediaAssetsVideoService;
import com.lazhu.business.videoeffectstpl.entity.VideoEffectsTpl;
import com.lazhu.business.videoeffectstpl.service.VideoEffectsTplService;
import com.lazhu.common.enums.CurStepEnum;
import com.lazhu.common.media.MediaPathUtils;
import com.lazhu.common.media.MediaResourceManager;
import com.lazhu.common.media.MediaUtil;
import com.lazhu.common.media.VideoInfo;
import com.lazhu.common.utils.OssUtil;
import com.lazhu.montage.model.ProcessAssets;
import com.lazhu.montage.model.ProcessElement;
import com.lazhu.montage.model.ProcessElementType;
import com.lazhu.montage.model.VideoMontageRequest;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 视频剪辑服务
 */
@Slf4j
@Service
public class VideoMontageService {


    @Autowired
    private ActorService actorService;

    @Autowired
    private VideoEffectsTplService videoEffectsTplService;

    @Autowired
    private AssCreateTool assCreateTool;

    @Autowired
    private VideoProcessCreateTool videoProcessCreateTool;

    @Autowired
    private MediaAssetsImgService mediaAssetsImgService;

    @Autowired
    private MediaAssetsVideoService mediaAssetsVideoService;

    @Autowired
    private VideoMontageTool videoMontageTool;

    @Autowired
    private ContentService contentService;

    @Autowired
    private OssUtil ossUtil;

    /**
     * 视频剪辑
     *
     * @param content 内容
     */
    public void processVideo(Content content) {
        long totalStartTime = System.currentTimeMillis();
        log.info("[VideoMontage-{}] 开始剪辑视频，内容ID: {}", content.getId(), content.getId());

        try {
            // 构建请求参数
            long buildRequestStartTime = System.currentTimeMillis();
            VideoMontageRequest videoMontageRequest = buildRequest(content);
            log.info("[VideoMontage-{}] 构建请求参数完成，耗时: {}ms", content.getId(), System.currentTimeMillis() - buildRequestStartTime);

            // 执行视频剪辑
            long processStartTime = System.currentTimeMillis();
            String exposeMediaUrl = videoMontageTool.processVideo(videoMontageRequest);
            log.info("[VideoMontage-{}] 视频剪辑处理完成，耗时: {}ms，输出文件: {}", content.getId(), System.currentTimeMillis() - processStartTime, exposeMediaUrl);

            // 上传剪辑视频  （判断剪辑操作过后的链接是否为网络链接，如果不是，则上传剪辑后的视频链接，如果是，则直接引用老链接）
            File file=null;
            if(!MediaPathUtils.isNetworkUrl(exposeMediaUrl)){
                file = FileUtil.file(exposeMediaUrl);
                String url = ossUtil.upload(file, "video/" + content.getId());
                content.setExposeMediaUrl(url);
            }else {
                content.setExposeMediaUrl(exposeMediaUrl);
            }
            // 生成封面
            File coverImageFile = MediaUtil.getVideoCover(exposeMediaUrl);
            String coverImage = ossUtil.upload(coverImageFile);
            List<String> imgs = Collections.singletonList(coverImage);
            content.setCoverImg(JSONObject.toJSONString(imgs));
            // 获取视频时长
            content.setDuration(MediaUtil.getDuration(exposeMediaUrl));
            // 清理临时文件
            MediaResourceManager.cleanupTempFile(file);
            log.info("[VideoMontage-{}] 视频剪辑任务成功完成，总耗时: {}ms", content.getId(), System.currentTimeMillis() - totalStartTime);
        } catch (Exception e) {
            log.error("[VideoMontage-{}] 视频剪辑失败，总耗时: {}ms，错误: {}", content.getId(), System.currentTimeMillis() - totalStartTime, e.getMessage(), e);
            throw e;
        } finally {
            // 剪辑结束，标记视频生成完成
            content.setCurStep(CurStepEnum.VIDEO_COMPLETE.getType());
            contentService.update(content);
        }
    }

    /**
     * 构建request
     */
    private VideoMontageRequest buildRequest(Content content) {
        VideoMontageRequest request = new VideoMontageRequest();
        request.setProcessId(content.getId().toString());
        request.setVideoPath(content.getMediaUrl());
        if(StringUtils.isNotBlank(content.getMetaData())){
            ContentMetaData metaData = JSONObject.parseObject(content.getMetaData(), ContentMetaData.class);
            request.setIsCueing(metaData.getIsCueing());
            request.setIsInsertMaterial(metaData.getIsInsertMaterial());
        }
        //获取ass
        String srtPath = content.getMediaUrl().replace("video.mp4", "audio.srt");
        String srt = HttpUtil.get(srtPath);
        log.info("获取到srt文件: {}，{}", content.getId(), srt);
        //格式化
        List<String> lines = new ArrayList<>();
        for (String line : srt.split("\n")) {
            lines.add(line.stripLeading());
        }
        srt = CollUtil.join(lines, "\n");
        request.setSrt(srt);
        Actor actor = actorService.queryById(content.getActorId());
        VideoEffectsTpl videoEffectsTpl = videoEffectsTplService.queryById(content.getEffectsTplId());
        String nickName = actor.getNickName();
        if (StrUtil.isNotBlank(actor.getTitle())) {
            nickName = nickName + "/" + actor.getTitle();
        }
        if (videoEffectsTpl != null ) {
            if (StrUtil.isNotBlank(videoEffectsTpl.getSrt())  && "1".equals(request.getIsCueing())) {
                // 获取视频宽高
                VideoInfo videoInfo = MediaUtil.getVideoInfo(content.getMediaUrl());
                int width = videoInfo.getWidth();
                int height = videoInfo.getHeight();
                String ass = assCreateTool.createAss(content.getTitle(), nickName, srt, videoEffectsTpl.getSrt(), width, height);
                log.info("获取到ass: {}，{}", content.getId(), ass);
                request.setAss(ass);
            }
            // 获取处理流程
            List<ProcessElement> processElementList = Collections.emptyList();
            if (StrUtil.isNotBlank(videoEffectsTpl.getProcessConfig())  && "1".equals(request.getIsInsertMaterial())) {
                JSONArray array = videoProcessCreateTool.createProcess(srt, videoEffectsTpl.getProcessConfig());
                processElementList = array.toJavaList(ProcessElement.class);
                //过滤没有id的素材
                processElementList = processElementList.stream().filter(element -> {
                    if(StrUtil.isNotBlank(element.getVid())){
                        return true;
                    }
                    log.warn("素材id为空，忽略处理:{}", JSONObject.toJSONString(element));
                    return false;
                }).collect(Collectors.toList());
                //查询所有素材
                request.setAssets(getAllAssets(processElementList));
                log.info("获取到处理流程: {}，{}", content.getId(), JSONObject.toJSONString(processElementList));
            }
            request.setProcessElements(processElementList);
        } else {
            // 未选择模板
            request.setProcessElements(Collections.emptyList());
        }
        return request;
    }

    /**
     * 获取所有素材
     *
     * @param processElements 处理元素列表
     * @return 素材查询结果
     */
    private ProcessAssets getAllAssets(List<ProcessElement> processElements) {
        // 根据素材类型分组，并获取所有的id
        Map<String, List<String>> groupedElements = processElements
                .stream()
                .collect(Collectors.groupingBy(ProcessElement::getType, Collectors.mapping(ProcessElement::getVid, Collectors.toList())));
        ProcessAssets assets = new ProcessAssets();
        for (String type : groupedElements.keySet()) {
            List<String> vids = groupedElements.get(type);
            if (type.equals(ProcessElementType.IMG)) {
                List<MediaAssetsImg> imgAssets = mediaAssetsImgService.queryByVids(vids);

                assets.setImageAssets(imgAssets.stream().map(img -> new ProcessAssets.AssetInfo(
                        img.getVectorId(),
                        img.getImgUrl(),
                        type, false)
                ).toList());

            } else if (type.equals(ProcessElementType.VIDEO_NO_AUDIO) || type.equals(ProcessElementType.VIDEO_WITH_AUDIO)) {
                List<MediaAssetsVideo> videoAssets = mediaAssetsVideoService.queryByVids(vids);

                assets.setVideoAssets(videoAssets.stream().map(video -> new ProcessAssets.AssetInfo(
                        video.getVectorId(),
                        video.getVideoUrl(),
                        type, "1".equals(video.getHasAudio()))
                ).toList());

            } else {
                // 其他类型不处理
                log.warn("素材类型暂不支持：{}", type);
            }
        }
        // 获取所有素材信息合并
        Collection<ProcessAssets.AssetInfo> union = CollUtil.union(assets.getImageAssets(), assets.getVideoAssets());
        // 分组 便于获取元素
        assets.setAssetPathMap(union.stream().collect(Collectors.toMap(ProcessAssets.AssetInfo::getVid, ProcessAssets.AssetInfo::getPath)));
        assets.setAssetInfoMap(union.stream().collect(Collectors.toMap(ProcessAssets.AssetInfo::getVid, asset -> asset)));
        return assets;
    }

}
