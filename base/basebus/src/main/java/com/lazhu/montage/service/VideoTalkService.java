package com.lazhu.montage.service;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lazhu.baseai.llm.dto.LLMBaseResponse;
import com.lazhu.baseai.llm.dto.VideoSynthesizeTask;
import com.lazhu.baseai.tool.audio.CosyVoiceTool;
import com.lazhu.baseai.tool.audio.TxtSplitter;
import com.lazhu.baseai.tool.audio.TxtToAudioService;
import com.lazhu.baseai.tool.dify.TTSOptimizationTool;
import com.lazhu.business.actor.entity.ActorVideo;
import com.lazhu.business.actor.service.ActorVideoService;
import com.lazhu.business.content.dto.ContentAudioInfo;
import com.lazhu.business.content.dto.ContentMetaData;
import com.lazhu.business.content.entity.Content;
import com.lazhu.business.content.service.ContentService;
import com.lazhu.business.llmconfig.service.LLMService;
import com.lazhu.business.tasks.entity.Tasks;
import com.lazhu.business.tasks.enums.TaskTypeEnum;
import com.lazhu.business.tasks.service.TasksService;
import com.lazhu.common.media.MediaResourceManager;
import com.lazhu.common.media.MediaUtil;
import com.lazhu.common.utils.OssUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 视频对口型服务
 */
@Slf4j
@Service
public class VideoTalkService {

    @Autowired
    private LLMService lLmService;

    @Autowired
    private OssUtil ossUtil;

    @Autowired
    private TasksService tasksService;

    @Autowired
    private ContentService contentService;

    @Autowired
    private TTSOptimizationTool ttsOptimizationTool;

    @Autowired
    private ActorVideoService actorVideoService;


    /**
     * 创建对口型任务
     *
     * @param contentId 内容id
     */
    public void createVideoTalkTask(Long contentId) {
        //删除task
        tasksService.deleteByContentId(contentId);
        //创建新的task
        Tasks tasks = new Tasks();
        tasks.setContentId(contentId);
        tasks.setType(TaskTypeEnum.VIDEO_TALK_SUBMIT.getCode());
        tasks.setPriority(0);
        tasks.setStatus(0);
        tasksService.save(tasks);
    }

    /**
     * 同步执行视频合成任务
     *
     * @param content 内容
     */
    public List<String> submitVideoTalk(Content content) throws Exception {
        // 1. 语音合成
        List<String> audioUrls = processAudioSynthesis(content);
        // 2. AI对口型视频生成
        List<String> taskIds = new ArrayList<>();
        for (String audioUrl : audioUrls) {
            List<String> taskIdList = processVideoSynthesis(content, audioUrl);
            taskIds.addAll(taskIdList);
        }
        return taskIds;
    }

    /**
     * 处理音频合成
     */
    private List<String> processAudioSynthesis(Content content) throws Exception {
        log.info("===> 开始音频合成，内容id：{}", content.getId());
        String voiceId = lLmService.queryVoiceId(content.getActorId());
        long startTime = System.currentTimeMillis();
        List<ContentAudioInfo> ssml;
        boolean updateSsml = false;
        if (StrUtil.isBlank(content.getSsml())) {
            ssml = createSsml(content.getContent());
        } else {
            ssml = JSONArray.parseArray(content.getSsml(), ContentAudioInfo.class);
        }
        String taskId = content.getId().toString();
        // 语音合成
        String outputFolder = generatorFolder();
        List<CosyVoiceTool.AudioInfo> audioInfoList = new ArrayList<>(ssml.size());
        for (int i = 0; i < ssml.size(); i++) {
            ContentAudioInfo info = ssml.get(i);
            String audioUrl = info.getAudioUrl();
            String outputPath = TxtToAudioService.getMp3PartPath(taskId, outputFolder, i);
            CosyVoiceTool.AudioInfo audioInfo;
            long duration = info.getDuration();
            long firstDelay = info.getFirstDelay();
            if (duration == 0 || firstDelay == 0) {
                //重新合成
                FileUtil.del(outputPath);
                File f = FileUtil.touch(outputPath);
                audioInfo = TxtToAudioService.tts(i, voiceId, info.getSsml(), outputPath);
                //上传oss
                audioUrl = ossUtil.uploadWithRandomFileName(f, "video/" + taskId);
                info.setAudioUrl(audioUrl);
                info.setDuration(audioInfo.timeLong());
                info.setFirstDelay(audioInfo.firstDelay());
                updateSsml = true;
            } else if (FileUtil.exist(outputPath)) {
                //本地文件存在
                String txt = TxtSplitter.extractTextFromSpeakContent(info.getSsml());
                audioInfo = new CosyVoiceTool.AudioInfo(i, txt, info.getDuration(), info.getFirstDelay());
            } else if (StrUtil.isNotBlank(audioUrl)) {
                // 本地文件不存在，下载网络文件到本地
                File tempFile = FileUtil.touch(outputPath);
                FileOutputStream fos = new FileOutputStream(tempFile);
                HttpUtil.download(audioUrl, fos, true);
                String txt = TxtSplitter.extractTextFromSpeakContent(info.getSsml());
                audioInfo = new CosyVoiceTool.AudioInfo(i, txt, info.getDuration(), info.getFirstDelay());
            } else {
                File f = FileUtil.touch(outputPath);
                // 合成语音
                audioInfo = TxtToAudioService.tts(i, voiceId, info.getSsml(), outputPath);
                //上传oss
                audioUrl = ossUtil.uploadWithRandomFileName(f, "video/" + taskId);
                info.setAudioUrl(audioUrl);
                info.setDuration(audioInfo.timeLong());
                info.setFirstDelay(audioInfo.firstDelay());
                updateSsml = true;
            }
            audioInfoList.add(audioInfo);
        }
        if (updateSsml) {
            // 更新ssml
            Content updateContent = new Content();
            updateContent.setId(content.getId());
            updateContent.setSsml(JSONArray.toJSONString(ssml));
            contentService.update(updateContent);
        }
        String audioPath = TxtToAudioService.audioMerge(taskId, outputFolder, audioInfoList);
        log.info("===> 音频合成完成，contentId：{}，音频路径：{}，耗时：{} s", content.getId(), audioPath, (System.currentTimeMillis() - startTime) / 1000);
        //上传音频文件
//        String audioUrl1 = ossUtil.uploadWithRandomFileName(FileUtil.file(audioPath), "video/" + taskId);
//        log.info("===> 音频上传oss完成，contentId：{}，音频路径：{}", content.getId(), audioUrl1);
        // 合成字幕
        String srtPath = TxtToAudioService.generateSubtitleFile(audioInfoList, outputFolder, taskId);
        //上传字幕文件
        ossUtil.upload(FileUtil.file(srtPath), "video/" + taskId);
        // 切分audioUrl （阿里对口型最多不超过120s）
        List<Long> splitPoints = SrtUtil.calculateSplitPoints(FileUtil.readUtf8String(srtPath), 119);
        long prev = 0;
        List<String> audioUrls = new ArrayList<>();
        for (int i = 0; i < splitPoints.size() - 1; i++) {
            long start;
            if (i != 0) {
                start = prev + 1;
            } else {
                start = splitPoints.get(i);
            }
            long end = splitPoints.get(i + 1);
            prev = end;
            String cutAudio = MediaUtil.cutAudio(audioPath, start, end);
            //上传oss
            String audioUrl = ossUtil.upload(FileUtil.file(cutAudio), "video/" + taskId);
            audioUrls.add(audioUrl);
        }
        // 删除临时文件
        FileUtil.del(outputFolder);
        return audioUrls;
    }

    public String generatorFolder() {
        String folder = FileUtil.getTmpDirPath();
        if (!folder.endsWith(File.separator)) {
            folder = folder + File.separator;
        }
        folder = folder + "content_combine";
        MediaResourceManager.ensureOutputDirectory(folder);
        return folder;
    }

    /**
     * 处理视频合成任务创建
     */
    private List<String> processVideoSynthesis(Content content, String audioUrl) throws Exception {
        ContentMetaData metaData = JSONObject.parseObject(content.getMetaData(), ContentMetaData.class);
        ActorVideo actorVideo = actorVideoService.queryById(metaData.getVideoId());
        String assertsVideoUrl = actorVideo.getMediaUrl();
        LLMBaseResponse<VideoSynthesizeTask> resp = lLmService.videoSynthesis(audioUrl, assertsVideoUrl);
        List<String> taskIds = resp.getBody().getTaskIds();

        log.info("===> 对口型视频任务创建完成，taskIds：{}", taskIds);
        return taskIds;
    }

    /**
     * 生成ssml标记语言
     */
    public List<ContentAudioInfo> createSsml(String content) {
        String optimizeText = ttsOptimizationTool.optimizeText(content);
        return Arrays.stream(TxtSplitter.smartSplit(optimizeText)).map(e -> new ContentAudioInfo(e, null, 0, 0)).toList();
    }


}
