package com.lazhu.montage.model;

import cn.hutool.core.util.StrUtil;
import lombok.Data;

import java.util.List;

/**
 * 视频处理请求实体类
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@Data
public class VideoMontageRequest {


    private String processId;

    /**
     * 视频源文件路径
     */
    private String videoPath;

    /**
     * 字幕文件
     */
    private String ass;

    /**
     * srt字幕
     */
    private String srt;

    /**
     * 处理节点
     */
    private List<ProcessElement> processElements;

    /**
     * 流程中用到的素材
     */
    private ProcessAssets assets;

    /**
     * 是否插入字幕（1、是 0、否）
     */
    private String isCueing;


    /**
     * 是否插入素材（1、是 0、否）
     */
    private String isInsertMaterial;

    public VideoMontageRequest() {
        // 生成一个唯一的处理ID
        this.processId = "video_montage_" + System.currentTimeMillis() + "_" + Thread.currentThread().threadId();
    }

    /**
     * 验证请求参数的有效性
     *
     * @throws IllegalArgumentException 当参数验证失败时抛出
     */
    public void validate() {
        // 验证视频路径
        if (videoPath == null || videoPath.trim().isEmpty()) {
            throw new IllegalArgumentException("视频路径不能为空");
        }

        // 验证字幕
        if (StrUtil.isBlank(ass) && StrUtil.isBlank(srt)) {
            throw new IllegalArgumentException("字幕不能为空字符串");
        }

        // 验证处理流程列表
        if (processElements == null) {
            throw new IllegalArgumentException("处理元素列表不能为空");
        }

        // 验证每个处理流程
        processElements.forEach(ProcessElement::validate);
    }
}
