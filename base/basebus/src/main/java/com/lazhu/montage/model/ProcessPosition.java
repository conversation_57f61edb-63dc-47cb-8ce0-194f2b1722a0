package com.lazhu.montage.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 位置信息实体类 (暂定)
 * 
 * <AUTHOR>
 * @date 2025-08-06
 */
@Data
public class ProcessPosition {
    
    /**
     * X坐标位置
     */
    @JSONField(name = "location_x")
    private Integer locationX;
    
    /**
     * Y坐标位置
     */
    @JSONField(name = "location_y")
    private Integer locationY;
    
    /**
     * 宽度
     */
    @JSONField(name = "width")
    private Integer width;
    
    /**
     * 高度
     */
    @JSONField(name = "height")
    private Integer height;
}