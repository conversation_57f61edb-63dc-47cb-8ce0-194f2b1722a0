package com.lazhu.montage.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.lazhu.common.media.*;
import com.lazhu.montage.model.ProcessAssets;
import com.lazhu.montage.model.ProcessElement;
import com.lazhu.montage.model.ProcessElementType;
import com.lazhu.montage.model.VideoMontageRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 视频处理工具类
 * 根据VideoProcessRequest处理视频，应用各种素材和特效
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@Slf4j
@Service
public class VideoMontageTool {


    /**
     * 临时文件管理Map，key为processId，value为该处理过程产生的临时文件列表
     */
    private final Map<String, List<File>> tempFilesMap = new ConcurrentHashMap<>();


    /**
     * 处理视频请求
     *
     * @param request 视频处理请求
     * @return 处理后的视频文件路径
     * @throws IllegalArgumentException 当参数验证失败时抛出
     */
    public String processVideo(VideoMontageRequest request) {
        // 参数验证
        request.validate();

        log.info("开始处理视频请求：{}", JSONObject.toJSONString(request));
        List<ProcessElement> processElements = request.getProcessElements();
        //1、获取所有的素材
        ProcessAssets assets = request.getAssets();
        //2 按照处理方式分类
        List<ProcessElement> replaceElements = new ArrayList<>();
        List<ProcessElement> insertElements = new ArrayList<>();
        for (ProcessElement element : processElements) {
            ProcessAssets.AssetInfo assetInfo = assets.getAssetInfoMap().get(element.getVid());
            if (assetInfo == null) {
                log.warn("素材不存在：{},忽略处理", element.getVid());
                continue;
            }
            // 如果是视频，对时间四舍五入处理（转成秒） TODO 暂时先这么处理，后续支持视频替换精确到毫秒级别
            if (element.getType().equals(ProcessElementType.VIDEO_WITH_AUDIO) || element.getType().equals(ProcessElementType.VIDEO_NO_AUDIO)) {
                element.setStartTime(NumberUtil.round(element.getStartTime(), 0).doubleValue());
                element.setEndTime(NumberUtil.round(element.getEndTime(), 0).doubleValue());
            }
            if (element.getType().equals(ProcessElementType.VIDEO_NO_AUDIO)
                    || element.getType().equals(ProcessElementType.IMG)) {
                //无声视频 或者 图片 替换处理
                replaceElements.add(element);
            } else if (element.getType().equals(ProcessElementType.VIDEO_WITH_AUDIO)) {
                //有声视频
                if (assetInfo.getHasAudio() != null && assetInfo.getHasAudio()) {
                    insertElements.add(element);
                } else {
                    log.warn("插入视频素材无音频：{},忽略处理", element.getVid());
                }
            } else {
                // 其他类型 （暂不处理）
                log.warn("素材类型不支持：{},忽略处理", element.getType());
            }
        }
        String finalOutputPath = null;
        try {
            //3、处理替换流程
            String processedVideo = processReplaceElements(request.getVideoPath(), replaceElements, request);
            // 4、合成字幕
            if("1".equals(request.getIsCueing())){
                String ass = AssUtil.formatAss(request.getAss(), replaceElements);
                request.setAss(ass);
                processedVideo = processSubtitle(processedVideo, request);
            }
            if("1".equals(request.getIsInsertMaterial())){
                // 5、处理插入流程
                finalOutputPath = processInsertElements(processedVideo, insertElements, request);
            }else {
                finalOutputPath=processedVideo;
            }
            return finalOutputPath;
        } catch (Exception e) {
            log.error("处理视频出错:id:{}", request.getProcessId(), e);
            throw e;
        } finally {
            // 根据处理ID清理所有临时文件，保留最终输出文件
            List<File> files = tempFilesMap.get(request.getProcessId());
            if(CollectionUtil.isNotEmpty(files)){
                String finalOutputPath1 = finalOutputPath;
                files.removeIf(file -> file.getAbsolutePath().equals(finalOutputPath1));
                files.forEach(MediaResourceManager::cleanupTempFile);
            }
        }
    }


    private String processReplaceElements(String videoPath, List<ProcessElement> replaceElements, VideoMontageRequest request) {
        long startTime = System.currentTimeMillis();
        log.info("开始处理替换素材,id:{},共{}个元素", request.getProcessId(), replaceElements.size());
        if (replaceElements.isEmpty()) {
            return videoPath;
        }
        // 1. 按时间顺序处理替换素材
        List<ProcessElement> sortedReplaceElements = replaceElements.stream().sorted(Comparator.comparingDouble(ProcessElement::getStartTime)).toList();
        String processedVideo = videoPath;
        int successCount = 0;
        for (int i = 0; i < sortedReplaceElements.size(); i++) {
            log.info("处理替换素材，第{}个元素", i);
            ProcessElement replaceElement = sortedReplaceElements.get(i);
            // 素材路径
            String replacePath = request.getAssets().getAssetPathMap().get(replaceElement.getVid());
            // 输出路径
            String outputPath = generateOutputPath(request.getProcessId(), "video_replaced", i, ".mp4");
            Position position = BeanUtil.copyProperties(replaceElement.getPosition(), Position.class);
            OverlayMode overlayMode = OverlayMode.getByName(replaceElement.getBg());
            try {
                if (replaceElement.getType().equals(ProcessElementType.VIDEO_NO_AUDIO)) {
                    //视频替换
                    MediaUtil.replaceVideoSegment(processedVideo, replacePath, replaceElement.getStartTime().longValue(), replaceElement.getEndTime().longValue(), position, overlayMode, outputPath);
                } else if (replaceElement.getType().equals(ProcessElementType.IMG)) {
                    //图片替换
                    long start = (long) NumberUtil.mul(replaceElement.getStartTime(), Double.valueOf(1000));
                    long end = (long) NumberUtil.mul(replaceElement.getEndTime(), Double.valueOf(1000));
                    MediaUtil.replaceWithImage(processedVideo, replacePath, start, end, position, overlayMode, outputPath);
                }
                processedVideo = outputPath;
                successCount++;
            } catch (Exception e) {
                log.error("处理替换素材出错，忽略该素材:{}", replaceElement.getVid(), e);
                // 继续处理下一个元素，不中断整个流程
            }
        }
        log.info(">>>>>>> 替换素材处理完成，id:{},共处理{}个元素,输出视频: {},耗时：{}ms <<<<<<< ", request.getProcessId(), successCount, processedVideo, System.currentTimeMillis() - startTime);
        return processedVideo;
    }

    private String processInsertElements(String videoPath, List<ProcessElement> insertElements, VideoMontageRequest request) {
        long startTime = System.currentTimeMillis();
        log.info("开始处理插入素材，id:{},共{}个元素", request.getProcessId(), insertElements.size());

        if (insertElements.isEmpty()) {
            return videoPath;
        }

        // 1. 按开始时间倒序排序，从后往前插入，避免时间偏移问题
        List<ProcessElement> sortedInsertElements = insertElements.stream()
                .sorted(Comparator.comparingDouble(ProcessElement::getStartTime).reversed())
                .toList();

        String processedVideo = videoPath;
        int successCount = 0;
        for (int i = 0; i < sortedInsertElements.size(); i++) {
            ProcessElement insertElement = sortedInsertElements.get(i);
            // 2. 获取要插入的视频路径
            String insertVideoPath = request.getAssets().getAssetPathMap().get(insertElement.getVid());
            // 3. 创建输出文件
            String outputPath = generateOutputPath(request.getProcessId(), "video_inserted", i, ".mp4");
            log.info("插入视频 {} 到位置 {}s", insertVideoPath, insertElement.getStartTime());
            try {
                // 4. 执行视频插入
                MediaUtil.insertVideo(processedVideo, insertVideoPath, insertElement.getStartTime().longValue(), outputPath);
                // 5. 更新处理后的视频路径
                processedVideo = outputPath;
                successCount++;
            } catch (Exception e) {
                log.error("插入视频失败，元素: {}", insertElement.getVid(), e);
                // 继续处理下一个元素，不中断整个流程
            }
        }
        log.info(">>>>>>> 插入素材处理完成，id:{},输出视频路径：{}，共处理{}个元素，耗时：{}ms <<<<<", request.getProcessId(), processedVideo, successCount, System.currentTimeMillis() - startTime);
        return processedVideo;
    }


    /**
     * 处理字幕
     *
     * @param videoPath 视频路径
     * @return 合成后的字幕文件路径
     */
    private String processSubtitle(String videoPath, VideoMontageRequest request) {
        long startTime = System.currentTimeMillis();
        log.info("开始合成字幕:id {},视频路径:{}", request.getProcessId(), videoPath);
        String outPath;
        if (StrUtil.isNotBlank(request.getAss())) {
            //合成ass字幕
            // 1. 转存字幕文件
            String ass = request.getAss();
            String subtitlePath = generateOutputPath(request.getProcessId(), "ass", ".ass");
            FileUtil.writeUtf8String(ass, subtitlePath);
            // 2. 合成字幕
            outPath = generateOutputPath(request.getProcessId(), "video_with_ass", ".mp4");
            MediaUtil.mergeAssSubtitle(videoPath, subtitlePath, outPath);
        } else {
            //合成srt字幕
            // 1. 转存字幕文件
            String srt = request.getSrt();
            List<String> lines = new ArrayList<>();
            for (String line : srt.split("\n")) {
                lines.add(line.stripLeading());
            }
            String subtitlePath = generateOutputPath(request.getProcessId(), "srt", ".srt");
            FileUtil.writeUtf8String(CollUtil.join(lines, "\n"), subtitlePath);
            // 2. 合成字幕
            outPath = generateOutputPath(request.getProcessId(), "video_with_srt", ".mp4");
            MediaUtil.addSubtitle(videoPath, subtitlePath, outPath);
        }
        log.info(">>>>>>> 字幕处理完成，id:{},输出文件路径：{},耗时：{}ms <<<<<<<", request.getProcessId(), outPath, System.currentTimeMillis() - startTime);
        return outPath;
    }

    /**
     * 生成输出文件路径
     *
     * @param processId 处理ID
     * @param stage     处理阶段标识
     * @param index     索引（可选）
     * @param extension 文件扩展名
     * @return 输出文件路径
     */
    private String generateOutputPath(String processId, String stage, Integer index, String extension) {
        StringBuilder fileName = new StringBuilder(processId);
        fileName.append("_").append(stage);

        if (index != null) {
            fileName.append("_").append(index);
        }

        File tempFile = MediaResourceManager.createTempFile(fileName.toString(), extension);
        tempFilesMap.computeIfAbsent(processId, k -> new ArrayList<>()).add(tempFile);
        log.debug("添加临时文件到管理列表: {} -> {}", processId, tempFile.getName());
        return tempFile.getAbsolutePath();
    }

    /**
     * 生成输出文件路径（无索引）
     */
    public String generateOutputPath(String processId, String stage, String extension) {
        return generateOutputPath(processId, stage, null, extension);
    }


}
