package com.lazhu.montage.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 素材
 * 包含所有查询到的素材信息
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@Data
public class ProcessAssets {


    /**
     * 图片素材列表
     */
    private List<AssetInfo> imageAssets;

    /**
     * 视频素材列表
     */
    private List<AssetInfo> videoAssets;


    /**
     * 素材ID到路径的映射
     */
    private Map<String, String> assetPathMap;

    /**
     * 素材ID到信息的映射
     */
    private Map<String, AssetInfo> assetInfoMap;

    /**
     * 素材信息类
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class AssetInfo {

        private String vid;

        /**
         * 素材路径
         */
        private String path;

        /**
         * 素材类型
         */
        private String type;

        /**
         * 是否有声（仅视频）
         */
        private Boolean hasAudio;
    }
}
