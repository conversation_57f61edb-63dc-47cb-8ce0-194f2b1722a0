package com.lazhu.montage.service;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class SrtUtil {

    /**
     * 解析SRT字幕文件
     *
     * @param srt srt文件内容
     * @return 字幕条目列表
     */
    private static List<SubtitleEntry> parseSrt(String srt) {
        List<SubtitleEntry> subtitles = new ArrayList<>();

        try {
            String[] lines = srt.split("\n");
            SubtitleEntry currentEntry = null;
            for (String line : lines) {
                line = line.trim();
                // 空行表示一个字幕条目结束
                if (StrUtil.isEmpty(line)) {
                    subtitles.add(currentEntry);
                    currentEntry = null;
                    continue;
                }
                // 跳过序号行（纯数字行）
                if (line.matches("\\d+")) {
                    continue;
                }
                // 时间戳行
                if (line.contains("-->")) {
                    String[] parts = line.split("-->");
                    long startTime = parseTime(parts[0].trim());
                    long endTime = parseTime(parts[1].trim());
                    currentEntry = new SubtitleEntry(startTime, endTime);
                } else if (currentEntry != null) {
                    // 字幕文本行
                    currentEntry.setText(line);
                }
            }
            // 添加最后一个条目（如果没有以空行结尾）
            if (currentEntry != null && !currentEntry.text.isEmpty()) {
                subtitles.add(currentEntry);
            }

        } catch (Exception e) {
            log.error("解析SRT失败: {}", srt, e);
        }

        return subtitles;
    }

    /**
     * 解析时间字符串为毫秒
     *
     * @param timeStr 时间字符串，格式为 HH:mm:ss,SSS
     * @return 毫秒数
     */
    private static long parseTime(String timeStr) {
        String[] parts = timeStr.split("[,:]");
        if (parts.length != 4) {
            return 0;
        }

        try {
            int hours = Integer.parseInt(parts[0]);
            int minutes = Integer.parseInt(parts[1]);
            int seconds = Integer.parseInt(parts[2]);
            int milliseconds = Integer.parseInt(parts[3]);

            return hours * 3600000L + minutes * 60000L + seconds * 1000L + milliseconds;
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    /**
     * 将豪秒数转换为时间字符串(00:00:00.00)
     *
     * @param millSeconds 毫秒
     * @return 时间字符串
     */
    private static String millSecondsToTime(long millSeconds) {
        long hours = millSeconds / 3600000L;
        long minutes = (millSeconds % 3600000L) / 60000L;
        long seconds = (millSeconds % 60000L) / 1000L;
        long milliseconds = millSeconds % 1000L;

        return String.format("%02d:%02d:%02d.%03d", hours, minutes, seconds, milliseconds);
    }


    /**
     * 根据字幕条目列表计算切分点
     */
    private static List<Long> calculateSplitPointsFromSubtitles(List<SubtitleEntry> subtitles, int maxDuration) {
        List<Long> splitPoints = new ArrayList<>();

        if (subtitles.isEmpty()) {
            return splitPoints;
        }

        long totalTime = subtitles.getLast().endTime;
        splitPoints.add(0L); // 起始点

        int i = 0;
        long segmentStart = 0;

        while (segmentStart < totalTime) {
            long idealEnd = segmentStart + maxDuration * 1000L; // 转换为毫秒

            // 如果理想结束时间超过了总时长，则直接以总时长为结束点
            if (idealEnd >= totalTime) {
                splitPoints.add(totalTime);
                break;
            }

            // 寻找距离120s最近但小于120s的字幕结束点
            long bestSplitPoint = segmentStart;
            for (; i < subtitles.size(); i++) {
                SubtitleEntry subtitle = subtitles.get(i);

                // 如果当前字幕结束时间已经超过理想结束时间，则停止寻找
                if (subtitle.endTime > idealEnd) {
                    break;
                }

                // 更新最佳切分点（选择距离理想结束时间最近但小于理想时间的字幕结束点）
                if (subtitle.endTime > segmentStart) {
                    bestSplitPoint = subtitle.endTime;
                }
            }

            // 如果没有找到合适的切分点，使用上一个有效的字幕结束时间或理想时间点
            if (bestSplitPoint == segmentStart && i > 0) {
                // 回退到前一个字幕的结束时间
                bestSplitPoint = subtitles.get(i - 1).endTime;
            } else if (bestSplitPoint == segmentStart) {
                // 如果还是没找到，使用理想时间点
                bestSplitPoint = idealEnd;
            }

            splitPoints.add(bestSplitPoint);
            segmentStart = bestSplitPoint;
        }

        return splitPoints;
    }


    /**
     * 根据字幕文件找出语音切分的时间点
     * <p>
     * 切分规则：
     * 1. 每段不大于120秒
     * 2. 找出小于120s最近的切分点
     *
     * @param srt         字幕
     * @param maxDuration 每段最大时长（秒）
     * @return 切分时间点列表（毫秒）
     */
    public static List<Long> calculateSplitPoints(String srt, int maxDuration) {
        List<SubtitleEntry> subtitleEntries = parseSrt(srt);
        return calculateSplitPointsFromSubtitles(subtitleEntries, maxDuration);
    }

    public static void main(String[] args) {
        String srt = FileUtil.readUtf8String("C:\\Users\\<USER>\\Desktop\\test.srt");
        List<SubtitleEntry> testSubtitles = parseSrt(srt);

        // 计算切分点
        List<Long> splitPoints = calculateSplitPointsFromSubtitles(testSubtitles, 120);

        log.info("总时长: {}毫秒", testSubtitles.getLast().endTime);
        log.info("切分点数量: {}", splitPoints.size());

        long prev = 0;
        for (int i = 0; i < splitPoints.size() - 1; i++) {
            long start;
            if (i != 0) {
                start = prev + 1;
            } else {
                start = splitPoints.get(i);
            }
            long end = splitPoints.get(i + 1);
            long duration = end - start;
            prev = end;
            log.info("第{}段: {} -> {} (时长: {}s)", i + 1, millSecondsToTime(start), millSecondsToTime(end), duration / 1000);
        }
    }

    /**
     * 字幕条目内部类
     */
    @Data
    private static class SubtitleEntry {
        long startTime;  // 毫秒
        long endTime;    // 毫秒
        String text;

        public SubtitleEntry(long startTime, long endTime) {
            this.startTime = startTime;
            this.endTime = endTime;
        }

    }

}
