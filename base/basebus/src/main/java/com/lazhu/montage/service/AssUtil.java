package com.lazhu.montage.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.lazhu.montage.model.ProcessElement;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 时间段处理工具类
 * 用于处理时间段的拆分、合并等操作
 */
@Slf4j
public class AssUtil {


    private static final Pattern TIME_PATTERN = Pattern.compile("\\d+:\\d+:\\d+\\.\\d+,\\d+:\\d+:\\d+\\.\\d+");

    /**
     * 格式化ass
     *
     * @param ass             ass
     * @param replaceElements 替换元素
     * @return 处理后的ass
     */
    public static String formatAss(String ass, List<ProcessElement> replaceElements) {
        if (StrUtil.isBlank(ass)) {
            return ass;
        }
        log.info("formatAss >> : {}", ass);
        String[] split = ass.split("\n");
        List<String> lines = new ArrayList<>();
        for (String line : split) {
            line = line.replace("{\\r}", "{\\\\r}");
            // 标题行
            if (line.contains("视频标题")) {
                List<String> titleLines = cutTitle(line, replaceElements);
                lines.addAll(titleLines);
            } else {
                lines.add(line);
            }
        }
        String result = CollUtil.join(lines, "\n");
        log.info("formatAss完成 >>  : {}", result);
        return result;
    }


    /**
     * 切分标题时间 ， 根据替换元素的时间切分标题时间
     *
     * @param title           标题行
     * @param replaceElements 替换元素
     */
    private static List<String> cutTitle(String title, List<ProcessElement> replaceElements) {
        if (CollUtil.isEmpty(replaceElements)) {
            return Collections.singletonList(title);
        }
        // 获取标题的展示时间
        Matcher matcher = TIME_PATTERN.matcher(title);
        if (matcher.find()) {
            String timeRange = matcher.group(0);
            String[] timeRangeArray = timeRange.split(",");
            Double titleStartTime = timeToSeconds(timeRangeArray[0]);
            Double titleEndTime = timeToSeconds(timeRangeArray[1]);
            // 创建时间段列表
            List<TimeSegment> segments = new ArrayList<>();
            segments.add(new TimeSegment(titleStartTime, titleEndTime, SegmentType.ORIGINAL));
            // 按时间排序
            List<ProcessElement> sortedReplaceElements = replaceElements.stream().sorted(Comparator.comparingDouble(ProcessElement::getStartTime)).toList();
            // 应用所有替换元素进行切分
            for (ProcessElement replaceElement : sortedReplaceElements) {
                List<TimeSegment> newSegments = new ArrayList<>();
                Double replaceStart = replaceElement.getStartTime();
                Double replaceEnd = replaceElement.getEndTime();
                for (TimeSegment segment : segments) {
                    newSegments.addAll(segment.subtract(replaceStart, replaceEnd));
                }
                segments = newSegments;
            }

            // 过滤并排序保留的片段
            List<TimeSegment> originalSegments = segments
                    .stream()
                    .filter(segment -> segment.getType() == SegmentType.ORIGINAL && segment.getStart() < segment.getEnd())
                    .sorted(Comparator.comparingDouble(TimeSegment::getStart))
                    .toList();

            return originalSegments
                    .stream()
                    .map(segment -> title.replace(timeRange, secondsToTime(segment.getStart()) + "," + secondsToTime(segment.getEnd())))
                    .toList();
        } else {
            return Collections.singletonList(title);
        }
    }

    /**
     * 时间段类型枚举
     */
    public enum SegmentType {
        /**
         * 原始时间段（来自原始标题）
         */
        ORIGINAL,
        /**
         * 被替换的时间段
         */
        REPLACED
    }

    /**
     * 时间段类，用于表示和操作时间段
     */
    public static class TimeSegment {
        private final Double start;
        private final Double end;
        private final SegmentType type;

        public TimeSegment(Double start, Double end, SegmentType type) {
            this.start = start;
            this.end = end;
            this.type = type;
        }

        public Double getStart() {
            return start;
        }

        public Double getEnd() {
            return end;
        }

        public SegmentType getType() {
            return type;
        }

        /**
         * 从当前时间段中减去指定的时间段
         *
         * @param subtractStart 要减去的时间段开始时间
         * @param subtractEnd   要减去的时间段结束时间
         * @return 拆分后的时间段列表
         */
        public List<TimeSegment> subtract(Double subtractStart, Double subtractEnd) {
            List<TimeSegment> result = new ArrayList<>();

            // 如果没有重叠，返回原时间段
            if (end <= subtractStart || start >= subtractEnd) {
                result.add(this);
                return result;
            }

            // 添加减去时间段前的部分（保持原类型）
            if (start < subtractStart) {
                result.add(new TimeSegment(start, subtractStart, this.type));
            }

            // 添加被替换的部分
            Double overlapStart = Math.max(start, subtractStart);
            Double overlapEnd = Math.min(end, subtractEnd);
            if (overlapStart < overlapEnd) {
                result.add(new TimeSegment(overlapStart, overlapEnd, SegmentType.REPLACED));
            }

            // 添加减去时间段后的部分（保持原类型）
            if (end > subtractEnd) {
                result.add(new TimeSegment(subtractEnd, end, this.type));
            }

            return result;
        }

        @Override
        public String toString() {
            return String.format("[%f, %f, %s]", start, end, type);
        }
    }


    /**
     * 将时间字符串(00:00:00.00)转换为秒数
     *
     * @param timeString 时间字符串
     * @return 秒数
     */
    private static Double timeToSeconds(String timeString) {
        String[] parts = timeString.split(":");
        int hours = Integer.parseInt(parts[0]);
        int minutes = Integer.parseInt(parts[1]);
        double seconds = Double.parseDouble(parts[2]);
        return hours * 3600.0 + minutes * 60.0 + seconds;
    }

    /**
     * 将秒数转换为时间字符串(00:00:00.00)
     *
     * @param seconds 秒数
     * @return 时间字符串
     */
    private static String secondsToTime(Double seconds) {
        int hours = (int) (seconds / 3600);
        int minutes = (int) ((seconds % 3600) / 60);
        double secs = seconds % 60;
        return String.format("%02d:%02d:%05.2f", hours, minutes, secs);
    }

    public static void main(String[] args) {
        // 创建测试用例
//        List<ProcessElement> replaceElements = new ArrayList<>();
//        ProcessElement element1 = new ProcessElement();
//        element1.setStartTime(10.11);
//        element1.setEndTime(20.1);
//        replaceElements.add(element1);
//
//        ProcessElement element2 = new ProcessElement();
//        element2.setStartTime(30.0);
//        element2.setEndTime(40.0);
//        replaceElements.add(element2);

        String process = "[{\"animation\":\"fade_in\",\"bg\":\"clear\",\"describe\":\"财政部等联合发布的《个人消费贷款财政贴息政策实施方案》文件内容图解\",\"endTime\":6.915,\"position\":{\"height\":1088,\"location_x\":160,\"location_y\":416,\"width\":759},\"startTime\":2.915,\"type\":\"img\",\"vid\":\"1ac23a2a-5556-4615-8ed6-dd9bae26201b\"},{\"animation\":\"fade_in\",\"bg\":\"clear\",\"describe\":\"年金保险产品介绍视频，展示年金险的领取计划、收益对比及增值服务\",\"endTime\":259.868,\"position\":{\"height\":1824,\"location_x\":27,\"location_y\":48,\"width\":1026},\"startTime\":38.868,\"type\":\"vidn\",\"vid\":\"23d240cb-f0bf-46a7-b886-220c898406fd\"}]";
        List<ProcessElement> replaceElements = JSONObject.parseArray(process, ProcessElement.class);
        // 测试标题行
        String ass = FileUtil.readUtf8String("C:\\Users\\<USER>\\Desktop\\example.ass");

        // 测试切分标题时间
        String result = formatAss(ass, replaceElements);
        System.out.println(result);
    }

}