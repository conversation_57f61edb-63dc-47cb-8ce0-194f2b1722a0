package com.lazhu.montage.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 处理元素实体类
 * 代表视频处理过程中的一个元素（图片、视频等）
 *
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
public class ProcessElement {

    /**
     * 动画效果配置
     */
    @JSONField(name = "animation")
    private String animation;

    /**
     * 元素描述信息
     */
    @JSONField(name = "describe")
    private String describe;

    /**
     * 结束时间（秒）
     */
    @JSONField(name = "endTime")
    private Double endTime;

    /**
     * 位置信息配置
     */
    @JSONField(name = "position")
    private ProcessPosition position;

    /**
     * 开始时间（秒）
     */
    @JSONField(name = "startTime")
    private Double startTime;

    /**
     * 元素类型（ img 图片 vidh 有声视频 vidn 无声视频 effe 简单滤镜(暂定) ）
     */
    @JSONField(name = "type")
    private String type;

    /**
     * 向量id
     */
    @JSONField(name = "vid")
    private String vid;

    /**
     * 尺寸规格（如：10x20）
     */
    @JSONField(name = "size")
    private String size;

    /**
     * 背景设置 clear 透明  black  黑色
     */
    @JSONField(name = "bg")
    private String bg;

    /**
     * 验证处理元素的有效性
     *
     * @throws IllegalArgumentException 当参数验证失败时抛出
     */
    public void validate() {
        // 验证元素类型
        if (type == null || type.trim().isEmpty()) {
            throw new IllegalArgumentException("元素的类型不能为空");
        }

        // 验证支持的类型
        if (!isValidElementType(type)) {
            throw new IllegalArgumentException("元素的类型不支持: " + type + "，支持的类型: img, vidn, vidh, effe");
        }

        // 验证素材ID
        if (vid == null || vid.trim().isEmpty()) {
            throw new IllegalArgumentException("元素的素材ID不能为空");
        }

        // 验证时间参数
        if (startTime == null) {
            throw new IllegalArgumentException("元素的开始时间不能为空");
        }

        if (endTime == null) {
            throw new IllegalArgumentException("元素的结束时间不能为空");
        }

        if (startTime < 0) {
            throw new IllegalArgumentException("元素的开始时间不能为负数: " + startTime);
        }

        if (endTime < 0) {
            throw new IllegalArgumentException("元素的结束时间不能为负数: " + endTime);
        }

        if (startTime >= endTime) {
            throw new IllegalArgumentException("元素的开始时间(" + startTime + ")必须小于结束时间(" + endTime + ")");
        }
    }

    /**
     * 检查元素类型是否有效
     *
     * @param type 元素类型
     * @return 是否为有效类型
     */
    private boolean isValidElementType(String type) {
        return ProcessElementType.IMG.equals(type) ||
                ProcessElementType.VIDEO_NO_AUDIO.equals(type) ||
                ProcessElementType.VIDEO_WITH_AUDIO.equals(type) ||
                ProcessElementType.EFFECT.equals(type);
    }
}