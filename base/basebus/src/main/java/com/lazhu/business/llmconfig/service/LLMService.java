package com.lazhu.business.llmconfig.service;

import com.lazhu.baseai.llm.dto.*;
import com.lazhu.baseai.llm.factory.LLMProviderFactory;
import com.lazhu.baseai.llm.provider.TextLLMProvider;
import com.lazhu.baseai.llm.provider.VideoLLMProvider;
import com.lazhu.baseai.llm.provider.VoiceLLMProvider;
import cn.hutool.core.bean.BeanUtil;
import com.lazhu.business.llmconfig.entity.LlmConfig;
import com.lazhu.business.llmvoicejoin.entity.LlmVoiceJoin;
import com.lazhu.business.llmvoicejoin.service.LlmVoiceJoinService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 大模型服务
 */
@Service
public class LLMService {

    @Autowired
    private LlmConfigService llmConfigService;


    @Autowired
    private LlmVoiceJoinService llmVoiceJoinService;


    @Autowired
    private LLMProviderFactory llmProviderFactory;


    /**
     * 大模型文本创作
     *
     * @param req 参数
     * @return 创作文本列表
     */

    public LLMBaseResponse<List<TextCreationResult>> createTxt(LLMBaseRequest<TextCreationReq> req) {
        LlmConfig llmConfig = llmConfigService.queryCurrentTextLLM();
        TextLLMProvider textProvider = llmProviderFactory.getTextProvider(llmConfig.getModelSupplier());
        req.setConfig(BeanUtil.copyProperties(llmConfig, ModelConfig.class));
        return textProvider.generateText(req);
    }

    /**
     * 大模型文本优化
     *
     * @param req 参数
     * @return 优化文本
     */

    public LLMBaseResponse<List<TextCreationResult>> finetuneTxt(LLMBaseRequest<TextCreationReq> req) {
        LlmConfig llmConfig = llmConfigService.queryCurrentTextLLM();
        TextLLMProvider textProvider = llmProviderFactory.getTextProvider(llmConfig.getModelSupplier());
        req.setConfig(BeanUtil.copyProperties(llmConfig, ModelConfig.class));
        return textProvider.finetuneTxt(req);
    }


    /**
     * 创建声音
     *
     * @param name     声音名称
     * @param voiceUrl 音源链接
     * @return 声音id
     */
    public VideoCreateResp createVoice(String name, String voiceUrl) {
        LlmConfig llmConfig = llmConfigService.queryCurrentVoiceLLM();
        VoiceLLMProvider voiceProvider = llmProviderFactory.getVoiceProvider(llmConfig.getModelSupplier());
        //调用大模型创建声音
        String voiceId = voiceProvider.createVoice(name, voiceUrl);

        VideoCreateResp resp = new VideoCreateResp();
        resp.setVoiceId(voiceId);
        resp.setLlmId(llmConfig.getId());
        return resp;
    }

    /**
     * 获取声音id
     */
    public String queryVoiceId(Long actorId) {
        //获取当前使用的语音大模型
        LlmConfig llmConfig = llmConfigService.queryCurrentVoiceLLM();
        LlmVoiceJoin join = llmVoiceJoinService.queryVoiceId(llmConfig.getId(), actorId);
        return join != null ? join.getVoiceId():null;
    }

    /**
     * 文本转语音
     *
     * @param id      内容id
     * @param text    文本
     * @param voiceId 声音id
     * @return 音频链接
     */
    public String audioSynthesis(String id, String text, String voiceId) {
        LlmConfig llmConfig = llmConfigService.queryCurrentVoiceLLM();
        VoiceLLMProvider voiceProvider = llmProviderFactory.getVoiceProvider(llmConfig.getModelSupplier());
        return voiceProvider.textToSpeech(id, text, voiceId);
    }

    /**
     * 视频合成
     *
     * @param audioUrl 音频链接
     * @param videoUrl 视频链接
     * @return taskId
     */
    public LLMBaseResponse<VideoSynthesizeTask> videoSynthesis(String audioUrl, String videoUrl) {
        LlmConfig llmConfig = llmConfigService.queryCurrentVideoLLM();
        VideoLLMProvider videoLLMProvider = llmProviderFactory.getVideoProvider(llmConfig.getModelSupplier());
        LLMBaseRequest<VideoSynthesizeReq> req = new LLMBaseRequest<>();
        req.setConfig(BeanUtil.copyProperties(llmConfig, ModelConfig.class));
        VideoSynthesizeReq videoSynthesizeReq = new VideoSynthesizeReq();
        videoSynthesizeReq.setAudioUrl(audioUrl);
        videoSynthesizeReq.setVideoUrl(videoUrl);
        req.setParams(videoSynthesizeReq);
        return videoLLMProvider.synthesizeVideo(req);
    }

    /**
     * 查询视频合成结果
     *
     * @param taskId 任务id
     * @return 合成结果
     */
    public LLMBaseResponse<VideoSynthesizeResp> queryVideoTask(String taskId) {
        LlmConfig llmConfig = llmConfigService.queryCurrentVideoLLM();
        VideoLLMProvider videoLLMProvider = llmProviderFactory.getVideoProvider(llmConfig.getModelSupplier());
        LLMBaseRequest<VideoSynthesizeTask> req = new LLMBaseRequest<>();
        req.setConfig(BeanUtil.copyProperties(llmConfig, ModelConfig.class));
        VideoSynthesizeTask videoSynthesizeReq = new VideoSynthesizeTask();
        videoSynthesizeReq.setTaskIds(List.of(taskId));
        req.setParams(videoSynthesizeReq);
        return videoLLMProvider.querySynthesizeResult(req);
    }
}
