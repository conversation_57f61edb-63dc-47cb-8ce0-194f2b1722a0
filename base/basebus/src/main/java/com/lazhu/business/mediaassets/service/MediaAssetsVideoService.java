package com.lazhu.business.mediaassets.service;

import com.lazhu.business.mediaassets.mapper.MediaAssetsVideoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lazhu.support.base.BaseService;
import com.lazhu.business.mediaassets.entity.MediaAssetsVideo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 系统视频素材表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Service
public class MediaAssetsVideoService extends BaseService<MediaAssetsVideo> {


    @Autowired
    private MediaAssetsVideoMapper mapper;

    public Long count(Map<String, Object> params) {
        return mapper.count(params);
    }

    public List<MediaAssetsVideo> queryByIds(List<Long> ids) {
        return mapper.queryByIds(ids);
    }

    /**
     * 根据向量id查询视频素材
     * @param vids 向量id
     * @return 视频素材
     */
    public List<MediaAssetsVideo> queryByVids(List<String> vids) {
        return mapper.queryByVids(vids);
    }
}
