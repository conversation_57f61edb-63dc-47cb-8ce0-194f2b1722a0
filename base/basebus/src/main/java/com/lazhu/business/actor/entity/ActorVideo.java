package com.lazhu.business.actor.entity;


import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lazhu.support.base.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ip人物视频
 * <AUTHOR>
 * @time 2025/8/15 15:17
 */
@TableName("c_actor_video")
@Data
@EqualsAndHashCode(callSuper = true)
public class ActorVideo extends BaseModel {
    private static final long serialVersionUID = 1L;
    //title 描述
    @TableField("title")
    private String title;
    //media_url 链接
    @TableField("media_url")
    private String mediaUrl;
    //duration_ms 时长 (秒)
    @TableField("duration")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long duration;
    // 视频封面
    @TableField("cover_img")
    private String coverImg;
    //actor_id 演员id
    @TableField("actor_id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long actorId;

    //user_id 用户id
    @TableField("user_id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long userId;

    //创建人
    @TableField("create_by")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long createBy;
}
