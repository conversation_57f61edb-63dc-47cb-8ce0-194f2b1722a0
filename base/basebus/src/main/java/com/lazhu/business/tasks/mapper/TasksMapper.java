package com.lazhu.business.tasks.mapper;

import com.lazhu.business.tasks.entity.Tasks;
import com.lazhu.support.base.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 任务记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface TasksMapper extends BaseMapper<Tasks> {

    void deleteByContentId(@Param("contentId") Long contentId);

    void deleteByContentIdAndType(@Param("contentId") Long contentId ,@Param("type") Integer type);
}
