package com.lazhu.business.noticestatistics.entity;

import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.lazhu.support.base.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * 关注者数据统计
 * <AUTHOR>
 * @since 2025-08-26
 */
@TableName("u_notice_statistics")
@Data
@EqualsAndHashCode(callSuper = true)
public class NoticeStatistics extends BaseModel {
    private static final long serialVersionUID = 1L;
    //user_id 用户id 
    @TableField("user_id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long userId;
    //actor_id 角色id 
    @TableField("actor_id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long actorId;
    //add_num 新增关注 
    @TableField("add_num")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer addNum;
    //cancel_num 取消关注 
    @TableField("cancel_num")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer cancelNum;
    //net_num 净增关注 
    @TableField("net_num")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer netNum;
    //total_num 关注者总数 
    @TableField("total_num")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer totalNum;

    //statistics_date 统计日期
    @TableField("statistics_date")
    private Date statisticsDate;
    
}
