package com.lazhu.business.mediaassets.strategy.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lazhu.baseai.vector.KnowledgeTool;
import com.lazhu.business.mediaassets.dto.MediaAssetsDTO;
import com.lazhu.business.mediaassets.dto.MediaAssetsQuery;
import com.lazhu.business.mediaassets.entity.MediaAssetsImg;
import com.lazhu.business.mediaassets.service.MediaAssetsImgService;
import com.lazhu.business.mediaassets.strategy.MediaAssetStrategy;
import com.lazhu.common.enums.MediaAssetSourceEnum;
import com.lazhu.common.enums.MediaAssetTypeEnum;
import io.netty.util.internal.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 图片素材策略实现
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Component
public class ImageAssetStrategy implements MediaAssetStrategy {

    @Autowired
    private MediaAssetsImgService mediaAssetsImgService;

    @Autowired
    private KnowledgeTool knowledgeTool;

    @Override
    public Page<MediaAssetsDTO> queryAssets(MediaAssetsQuery query) {
        // 执行查询
        Page<MediaAssetsImg> imgPage = mediaAssetsImgService.queryPage(BeanUtil.beanToMap(query));

        // 转换为统一素材对象
        List<MediaAssetsDTO> unifiedAssets = imgPage.getRecords().stream().map(img ->
                new MediaAssetsDTO(
                        img.getId(),
                        "1", // 图片类型
                        MediaAssetSourceEnum.SYSTEM.getSource(),
                        img.getTitle(),
                        img.getImgUrl(),
                        null,
                        null,
                        img.getCreateTime(),
                        img.getSize()
                )
        ).collect(Collectors.toList());

        // 创建返回的分页对象
        Page<MediaAssetsDTO> resultPage = new Page<>(imgPage.getCurrent(), imgPage.getSize());
        resultPage.setRecords(unifiedAssets);
        resultPage.setTotal(imgPage.getTotal());

        return resultPage;
    }

    @Override
    public Long countAssets(MediaAssetsQuery query) {
        return mediaAssetsImgService.count(BeanUtil.beanToMap(query));
    }


    @Override
    public MediaAssetsDTO uploadAsset(MediaAssetsDTO param) {
        MediaAssetsImg img = new MediaAssetsImg();
        img.setTitle(param.getTitle());
        img.setImgUrl(param.getUrl());
        img.setSize(param.getSize());
        String size =img.getSize();
        Integer width = Integer.valueOf(StringUtil.substringBefore(size,'*'));
        Integer height = Integer.valueOf(StringUtil.substringAfter(size,'*'));
        // 同步
        String vectorId = knowledgeTool.createKnowledge(img.getTitle(), height, width, null, MediaAssetTypeEnum.IMAGE.getType(), null, null);
        img.setVectorId(vectorId);
        mediaAssetsImgService.save(img);



        return new MediaAssetsDTO(
                img.getId(),
                "1", // 图片类型
                MediaAssetSourceEnum.SYSTEM.getSource(),
                img.getTitle(),
                img.getImgUrl(),
                null,
                null,
                img.getCreateTime(),
                img.getSize()
        );
    }

    @Override
    public Boolean updateAsset(Long id, String title) {
        MediaAssetsImg img = new MediaAssetsImg();
        img.setId(id);
        img.setTitle(title);
        mediaAssetsImgService.update(img);

        String content =img.getTitle();
        MediaAssetsImg mediaAssetsImg = mediaAssetsImgService.queryById(id);
        String vectorId = mediaAssetsImg.getVectorId();
        if(StringUtils.isNotBlank(vectorId)){
            String size =mediaAssetsImg.getSize();
            Integer width = Integer.valueOf(StringUtil.substringBefore(size,'*'));
            Integer height = Integer.valueOf(StringUtil.substringAfter(size,'*'));
            // 同步修改
            knowledgeTool.createKnowledge(content, height, width, null, MediaAssetTypeEnum.IMAGE.getType(), null, vectorId);
        }
        return true;
    }

    @Override
    public Boolean batchDeleteAssets(List<Long> ids) {
        // 同步删除
        List<MediaAssetsImg> mediaAssetsImgs = mediaAssetsImgService.queryByIds(ids);
        for (MediaAssetsImg mediaAssetsImg : mediaAssetsImgs) {
            if(StringUtils.isNotBlank(mediaAssetsImg.getVectorId())){
                knowledgeTool.deleteKnowledge(MediaAssetTypeEnum.IMAGE.getType(), null,mediaAssetsImg.getVectorId());
            }
        }

        return mediaAssetsImgService.batchDelById(ids) > 0;
    }
}