package com.lazhu.business.llmvoicejoin.service;

import com.lazhu.business.llmvoicejoin.mapper.LlmVoiceJoinMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lazhu.support.base.BaseService;
import com.lazhu.business.llmvoicejoin.entity.LlmVoiceJoin;

/**
 * <p>
 * 大模型音色素材关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Service
public class LlmVoiceJoinService extends BaseService<LlmVoiceJoin> {

    @Autowired
    private LlmVoiceJoinMapper llmVoiceJoinMapper;

    /**
     * 查询声音id
     * @param llmId 模型id
     * @param actorId 角色ipid
     */
    public LlmVoiceJoin queryVoiceId(Long llmId, Long actorId) {
        return llmVoiceJoinMapper.queryVoiceId(llmId, actorId);
    }


}
