<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lazhu.business.content.mapper.ContentMapper">

	<select id="selectIdPage" resultType="com.lazhu.business.content.entity.Content">
		select * from c_content
		<where>
			<if test="params.userId != null">
				and user_id = #{params.userId}
			</if>
			<if test="params.actorId != null">
				and actor_id = #{params.actorId}
			</if>
			<if test="params.topic != null and params.topic != ''">
			    and topic like concat(concat('%',#{params.topic}),'%')
			</if>
			<if test="params.title != null and params.title != ''">
			    and title like concat(concat('%',#{params.title}),'%')
			</if>
			<if test="params.status != null">
				and status = #{params.status}
			</if>
			<if test="params.contentType != null and params.contentType != ''">
			    and content_type like concat(concat('%',#{params.contentType}),'%')
			</if>
			<if test="params.content != null and params.content != ''">
			    and content like concat(concat('%',#{params.content}),'%')
			</if>
			<if test="params.coverImg != null and params.coverImg != ''">
			    and cover_img like concat(concat('%',#{params.coverImg}),'%')
			</if>
			<if test="params.mediaUrl != null and params.mediaUrl != ''">
			    and media_url like concat(concat('%',#{params.mediaUrl}),'%')
			</if>
			<if test="params.effectsTplId != null">
				and effects_tpl_id = #{params.effectsTplId}
			</if>
		</where>
	</select>
</mapper>
