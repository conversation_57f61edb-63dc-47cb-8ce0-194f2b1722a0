package com.lazhu.business.publishlog.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lazhu.business.publishlog.entity.PublishLog;
import com.lazhu.business.publishlog.entity.PublishLogQuery;
import com.lazhu.business.publishlog.vo.PublishLogVO;
import com.lazhu.business.userchannelaccount.entity.UserChannelAccountQuery;
import com.lazhu.business.userchannelaccount.vo.UserChannelAccountVO;
import com.lazhu.support.base.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 发布记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-26
 */
@Repository
public interface PublishLogMapper extends BaseMapper<PublishLog> {

    List<PublishLogVO> readPage(Page<PublishLogVO> page, @Param("params") PublishLogQuery query);
}
