package com.lazhu.business.mediaassets.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.lazhu.support.base.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * <p>
 * 系统视频素材表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@TableName("c_media_assets_video")
@Data
@EqualsAndHashCode(callSuper = true)
public class MediaAssetsVideo extends BaseModel {
    private static final long serialVersionUID = 1L;
    //title 描述 
    @TableField("title")
    private String title;
    //video_url 视频链接 
    @TableField("video_url")
    private String videoUrl;
    //duration_ms 时长 (秒)
    @TableField("duration")

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long duration;
    //cover_img 封面图片
    @TableField("cover_img")
    private String coverImg;
    // 是否保留声音（1、是 0、否）
    @TableField("has_audio")
    private String hasAudio;
    // 尺寸（宽 X 高）
    @TableField("size")
    private String size;
    // 向量ID
    @TableField("vector_id")
    private String vectorId;
    // 视频文件大小
    @TableField("storage")
    private String storage;

    
}
