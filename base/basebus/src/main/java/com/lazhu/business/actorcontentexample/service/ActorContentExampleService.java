package com.lazhu.business.actorcontentexample.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.lazhu.business.actorcontentexample.mapper.ActorContentExampleMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lazhu.support.base.BaseService;
import com.lazhu.business.actorcontentexample.entity.ActorContentExample;

import java.util.List;

/**
 * <p>
 * 角色文章案例 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Service
public class ActorContentExampleService extends BaseService<ActorContentExample> {

    @Autowired
    private ActorContentExampleMapper mapper;

    /**
     * 保存角色案例
     *
     * @param exampleContent
     */
    public void saveOrUpdate(ActorContentExample exampleContent) {
        ActorContentExample actorContentExample = queryByActorIdAndPlat(exampleContent.getActorId(), exampleContent.getPlat());
        if (actorContentExample == null) {
            mapper.insert(exampleContent);
        } else {
            exampleContent.setId(actorContentExample.getId());
            mapper.updateById(exampleContent);
        }
    }


    /**
     * 根据角色id和平台类型查询角色案例
     */
    public ActorContentExample queryByActorIdAndPlat(Long actorId, String plat) {
        return mapper.queryByActorIdAndPlat(actorId, plat);
    }

    /**
     * 根据角色id查询案例
     */
    public List<ActorContentExample> queryByActorId(Long actorId) {
        return mapper.selectList(new QueryWrapper<ActorContentExample>().eq("actor_id", actorId));
    }

}
