package com.lazhu.business.publishlog.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lazhu.support.base.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 发布记录表
 *
 * <AUTHOR>
 * @since 2025-08-26
 */
@TableName("c_publish_log")
@Data
@EqualsAndHashCode(callSuper = true)
public class PublishLog extends BaseModel {
    private static final long serialVersionUID = 1L;
    //content_id 内容id 
    @TableField("content_id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long contentId;
    // 用户id
    @TableField("user_id")
    private Long userId;
    // 角色id
    @TableField("actor_id")
    private Long actorId;
    // 发布渠道
    @TableField("channel")
    private Integer channel;
    //title 发布标题 
    @TableField("title")
    private String title;
    //description 视频描述 
    @TableField("description")
    private String description;
    //publish_type 发布方式;1 实时发布 2定时发布 
    @TableField("publish_type")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer publishType;
    //publish_time 发布时间 
    @TableField("publish_time")
    private Date publishTime;
    //publish_status 发布状态;0 待发布 1发布中 2发布成功  3发布失败
    @TableField("publish_status")
    private String publishStatus;
    //publish_id 发布id;第三方平台发布记录id 
    @TableField("publish_id")
    private String publishId;
    //account 第三方账号;
    @JSONField(serializeUsing = ToStringSerializer.class)
    @TableField("channel_account")
    private String channelAccount;
    //创建人
    @TableField("create_by")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long createBy;
    // 元数据 (json)
    @TableField("meta_data")
    private String metaData;


}
