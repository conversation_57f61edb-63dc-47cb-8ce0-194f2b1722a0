package com.lazhu.business.mediaassets.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.lazhu.support.base.BaseQuery;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * <p>
 * 系统视频素材表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MediaAssetsVideoQuery extends BaseQuery {
    //title 描述 
    private String title;
    //video_url 视频链接 
    private String videoUrl;
    //duration 时长 (秒)
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long duration;
    //cover_img 封面图片
    private String coverImg;
    // 是否保留声音（1、是 0、否）
    private String hasAudio;
    // 尺寸（宽 X 高）
    private String size;
    // 向量ID
    private String vectorId;
}
