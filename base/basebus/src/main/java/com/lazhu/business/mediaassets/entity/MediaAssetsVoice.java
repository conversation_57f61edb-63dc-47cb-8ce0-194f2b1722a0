package com.lazhu.business.mediaassets.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lazhu.support.base.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * <p>
 * 系统声音素材表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@TableName("c_media_assets_voice")
@Data
@EqualsAndHashCode(callSuper = true)
public class MediaAssetsVoice extends BaseModel {
    private static final long serialVersionUID = 1L;
    //title 描述 
    @TableField("title")
    private String title;
    //audio_url 音频链接 
    @TableField("audio_url")
    private String audioUrl;
    //duration_ms 时长 (秒)
    @TableField("duration")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long duration;
    
}
