<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lazhu.business.actor.mapper.ActorVideoMapper">

    <select id="selectIdPage" resultType="com.lazhu.business.actor.entity.ActorVideo">
        select * from c_actor_video
        <include refid="query"/>
        order by create_time desc
    </select>

    <sql id="query">
        <where>
            <if test="params.title != null and params.title != ''">
                and title like concat(concat('%',#{params.title}),'%')
            </if>
            <if test="params.actorId != null">
                and actor_id = #{params.actorId}
            </if>
            <if test="params.actorIds != null and params.actorIds.size > 0">
                and actor_id in
                <foreach item="item" collection="params.actorIds" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.userId != null">
                and user_id = #{params.userId}
            </if>
        </where>
    </sql>
</mapper>
