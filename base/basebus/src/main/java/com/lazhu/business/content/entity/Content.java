package com.lazhu.business.content.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.lazhu.support.base.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 内容管理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@TableName("c_content")
@Data
@EqualsAndHashCode(callSuper = true)
public class Content extends BaseModel {
    private static final long serialVersionUID = 1L;
    //user_id 所属用户 
    @TableField("user_id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long userId;
    //actor_id 演员id 
    @TableField("actor_id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long actorId;
    //topic 创作主题 
    @TableField("topic")
    private String topic;
    //title 标题 
    @TableField("title")
    private String title;
    //status 状态 (0:草稿, 1:完成, 2:已发布)
    @TableField("status")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer status;
    //content_type 类型 
    @TableField("content_type")
    private String contentType;
    //摘要
    @TableField("summary")
    private String summary;
    //content 文案内容 
    @TableField("content")
    private String content;
    //cover_img 封面图 
    @TableField("cover_img")
    private String coverImg;
    //media_url 视频链接 
    @TableField("media_url")
    private String mediaUrl;
    //视频时长 （毫秒）
    @TableField("duration")
    private Long duration;
    //元数据(生成内容参数)
    @TableField("meta_data")
    private String metaData;
    // 剪辑之后的url
    @TableField("expose_media_url")
    private String exposeMediaUrl;
    //特效模板id
    @TableField("effects_tpl_id")
    private Long effectsTplId;
    // 当前流程 1、文案生成 2、合成视频 3、视频合成中 4、视频合成完成
    @TableField("cur_step")
    private Integer curStep;

    // ssml 文本内容（可选，用于语音合成）
    @TableField("ssml")
    private String ssml;
}
