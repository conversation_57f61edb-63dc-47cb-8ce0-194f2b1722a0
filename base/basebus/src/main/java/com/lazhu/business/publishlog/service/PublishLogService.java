package com.lazhu.business.publishlog.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lazhu.business.publishlog.entity.PublishLogQuery;
import com.lazhu.business.publishlog.mapper.PublishLogMapper;
import com.lazhu.business.publishlog.vo.PublishLogVO;
import com.lazhu.business.userchannelaccount.entity.UserChannelAccountQuery;
import com.lazhu.business.userchannelaccount.vo.UserChannelAccountVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lazhu.support.base.BaseService;
import com.lazhu.business.publishlog.entity.PublishLog;

import java.util.Date;
import java.util.List;

/**
 * 发布记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-26
 */
@Service
public class PublishLogService extends BaseService<PublishLog> {

    @Autowired
    private PublishLogMapper publishLogMapper;

    /**
     * <AUTHOR>
     * @param[1] param
     * @time 2025/8/27 9:29
     */
    public Page<PublishLogVO> readPage(PublishLogQuery param) {
        if (param.getPageNo() == null) {
            param.setPageNo(1);
        }
        if (param.getPageSize() == null) {
            param.setPageSize(10);
        }
        Page<PublishLogVO> page = new Page<>(param.getPageNo(), param.getPageSize());
        List<PublishLogVO> list = publishLogMapper.readPage(page, param);
        page.setRecords(list);
        return page;
    }

    /**
     * 查询待发布任务
     */
    public List<PublishLog> queryWaitPublish(Date publishTime) {
        PublishLogQuery query = new PublishLogQuery();
        query.setPublishStatus("0");
        query.setPublishType(1);
        query.setPublishEndTime(publishTime);
        return publishLogMapper.selectIdPage(BeanUtil.beanToMap(query));
    }
}
