package com.lazhu.business.actorcontentexample.mapper;

import com.lazhu.business.actorcontentexample.entity.ActorContentExample;
import com.lazhu.support.base.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 角色文章案例 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
public interface ActorContentExampleMapper extends BaseMapper<ActorContentExample> {

    ActorContentExample queryByActorIdAndPlat(@Param("actorId") Long actorId, @Param("plat") String plat);
}
