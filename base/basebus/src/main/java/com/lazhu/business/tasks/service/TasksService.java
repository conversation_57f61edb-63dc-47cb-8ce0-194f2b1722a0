package com.lazhu.business.tasks.service;

import com.lazhu.business.tasks.mapper.TasksMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lazhu.support.base.BaseService;
import com.lazhu.business.tasks.entity.Tasks;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 任务记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Service
public class TasksService extends BaseService<Tasks> {


    @Autowired
    private TasksMapper tasksMapper;

    /**
     * 根据内容id删除任务记录
     *
     * @param contentId 内容id
     */
    public void deleteByContentId(Long contentId) {
        tasksMapper.deleteByContentId(contentId);
    }

    /**
     * 根据内容id删除任务记录
     *
     * @param contentId 内容id
     */
    public void deleteByContentIdAndType(Long contentId,Integer type) {
        tasksMapper.deleteByContentIdAndType(contentId,type);
    }

    /**
     * 批量保存
     */
    public void batchSave(List<Tasks> list) {
        tasksMapper.insert(list);
    }
}
