package com.lazhu.business.videostatistics.entity;

import java.util.Date;

import com.lazhu.support.base.BaseQuery;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * 视频数据统计
 * <AUTHOR>
 * @since 2025-08-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class VideoStatisticsQuery extends BaseQuery {
    //user_id 用户id 
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long userId;
    //actor_id 角色id 
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long actorId;
    //content_id 内容id 
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long contentId;
    //publish_id 发布id 
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long publishId;
    //play_num 播放量 
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer playNum;
    //zan_num 点赞量 
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer zanNum;
    //collect_num 收藏量 
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer collectNum;
    //comment_num 评论 
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer commentNum;
    //share_num 分享 
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer shareNum;
    //notice_num 关注 
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer noticeNum;
    //statistics_date 统计日期 
    private Date statisticsDate;

}
