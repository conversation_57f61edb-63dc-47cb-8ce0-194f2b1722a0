<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lazhu.business.mediaassets.mapper.UserMediaAssetsMapper">


    <sql id="query">
        <where>
            <if test="params.title != null and params.title != ''">
                and title like concat(concat('%',#{params.title}),'%')
            </if>
            <if test="params.assetsType != null and params.assetsType != ''">
                and assets_type = #{params.assetsType}
            </if>
            <if test="params.actorId != null">
                and actor_id = #{params.actorId}
            </if>
            <if test="params.actorIds != null and params.actorIds.size > 0">
                and actor_id in
                <foreach item="item" collection="params.actorIds" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.userId != null">
                and create_by = #{params.userId}
            </if>
            <if test="params.id != null">
                and id =#{params.id}
            </if>
        </where>
    </sql>

    <select id="selectIdPage" resultType="com.lazhu.business.mediaassets.entity.UserMediaAssets">
        select * from c_user_media_assets
        <include refid="query"/>
        order by create_time desc
    </select>
    <select id="count" resultType="java.lang.Long">
        select count(1) from c_user_media_assets
        <include refid="query"/>
    </select>
</mapper>
