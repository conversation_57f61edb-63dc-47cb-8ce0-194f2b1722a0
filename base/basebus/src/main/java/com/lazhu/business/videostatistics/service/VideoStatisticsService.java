package com.lazhu.business.videostatistics.service;



import com.lazhu.business.videostatistics.dto.VideoDailyStat;
import com.lazhu.business.videostatistics.dto.DateRange;
import com.lazhu.business.videostatistics.dto.ChartRequest;
import com.lazhu.business.videostatistics.mapper.VideoStatisticsMapper;
import com.lazhu.business.videostatistics.vo.ChartDataVO;
import com.lazhu.business.videostatistics.vo.VideoStatsVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lazhu.support.base.BaseService;
import com.lazhu.business.videostatistics.entity.VideoStatistics;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 视频数据统计 服务实现类
 * <AUTHOR>
 * @since 2025-08-26
 */
@Service
public class VideoStatisticsService extends BaseService<VideoStatistics> {

    @Autowired
    private VideoStatisticsMapper videoStatisticsMapper;

    // 指标名称映射
    private static final Map<String, String> METRIC_LABELS = new HashMap<>();
    static {
        METRIC_LABELS.put("play", "播放量");
        METRIC_LABELS.put("zan", "点赞量");
        METRIC_LABELS.put("collect", "收藏量");
        METRIC_LABELS.put("comment", "评论量");
        METRIC_LABELS.put("share", "分享量");
        METRIC_LABELS.put("notice", "关注量");
    }
    /**
     *
     * @param userId
     * @param periodType  1:近7天，2:近30天，3:昨天
     * @return
     */
    public VideoStatsVO getStatistics(Long userId, String periodType){

        // 获取当前周期数据
        DateRange  currentDateRange  = DateRange.calculateDateRange(periodType, false);
        VideoStatsVO currentStats = videoStatisticsMapper.selectStatsByUserIdAndDateRange(
                userId, currentDateRange.getStartDate(), currentDateRange.getEndDate());

        // 获取对比周期数据
        DateRange previousDateRange = DateRange.calculateDateRange(periodType, true);
        VideoStatsVO previousStats = videoStatisticsMapper.selectStatsByUserIdAndDateRange(
                userId, previousDateRange.getStartDate(), previousDateRange.getEndDate());

        // 设置对比数据并计算增长率
        currentStats.setPreviousPeriodStats(previousStats);
        calculateGrowthRate(currentStats, previousStats);
        return currentStats;
    }





    private void calculateGrowthRate(VideoStatsVO current, VideoStatsVO previous) {
        current.setPlayNumGrowthRate(calculateRate(current.getPlayNum(), previous.getPlayNum()));
        current.setZanNumGrowthRate(calculateRate(current.getZanNum(), previous.getZanNum()));
        current.setCollectNumGrowthRate(calculateRate(current.getCollectNum(), previous.getCollectNum()));
        current.setCommentNumGrowthRate(calculateRate(current.getCommentNum(), previous.getCommentNum()));
        current.setShareNumGrowthRate(calculateRate(current.getShareNum(), previous.getShareNum()));
        current.setNoticeNumGrowthRate(calculateRate(current.getNoticeNum(), previous.getNoticeNum()));
    }

    private String calculateRate(Integer current, Integer previous) {
        if (previous == null || previous == 0) {
            return "不可计算环比";
        }
        double rate = (current - previous) * 100.0 / previous;
        return String.format("%.2f%%", rate);
    }



    /**
     * 获取数据趋势
     * <AUTHOR>
     * @param[1] request
     * @return ChartDataVO
     * @time 2025/8/28 15:04
     */
    public ChartDataVO getVideoStatChart(ChartRequest request) {
        // 计算日期范围
        DateRange dateRange = DateRange.calculateDateRange(request);

        // 查询每日统计数据
        List<VideoDailyStat> dailyStats = videoStatisticsMapper
                .selectDailyStatsByUserIdAndDateRange(request.getUserId(),
                        dateRange.getStartDate(),
                        dateRange.getEndDate());

        // 转换为日期到统计数据的映射
        Map<LocalDate, VideoDailyStat> statMap = dailyStats.stream()
                .collect(Collectors.toMap(
                        stat -> stat.getStatDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
                        Function.identity()
                ));

        // 生成7个等间隔的日期点
        List<LocalDate> pointDates = DateRange.generatePointDates(
                dateRange.getStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
                dateRange.getEndDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate()
        );

        // 提取所有指标的数据
        Map<String, List<Integer>> metricsData = extractAllMetricsValues(pointDates, statMap);

        // 格式化日期为字符串
        List<String> dateLabels = pointDates.stream()
                .map(date -> formatDate(date))
                .collect(Collectors.toList());

        // 创建响应对象
        ChartDataVO result = new ChartDataVO();
        result.setDates(dateLabels);
        result.setMetricsData(metricsData);
        result.setMetricLabels(METRIC_LABELS);
        return result;
    }




    private Map<String, List<Integer>> extractAllMetricsValues(List<LocalDate> pointDates,
                                                               Map<LocalDate, VideoDailyStat> statMap) {
        Map<String, List<Integer>> result = new HashMap<>();

        // 初始化所有指标的数据列表
        for (String metric : METRIC_LABELS.keySet()) {
            result.put(metric, new ArrayList<>());
        }

        // 为每个日期点提取所有指标的数据
        for (LocalDate date : pointDates) {
            VideoDailyStat stat = statMap.get(date);
            result.get("play").add(stat != null ? stat.getPlayNum() : 0);
            result.get("zan").add(stat != null ? stat.getZanNum() : 0);
            result.get("collect").add(stat != null ? stat.getCollectNum() : 0);
            result.get("comment").add(stat != null ? stat.getCommentNum() : 0);
            result.get("share").add(stat != null ? stat.getShareNum() : 0);
            result.get("notice").add(stat != null ? stat.getNoticeNum() : 0);
        }

        return result;
    }

    private String formatDate(LocalDate date) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return date.format(formatter);
    }


    /**
     * 数据详情
     * <AUTHOR>
     * @param[1] request
     * @return List<DailyStat>
     * @time 2025/8/28 15:46
     */
    public List<VideoDailyStat> queryDetail(ChartRequest request){

        // 计算日期范围
        DateRange dateRange = DateRange.calculateDateRange(request);
        // 查询每日统计数据
        List<VideoDailyStat> dailyStats = videoStatisticsMapper
                .selectDailyStatsByUserIdAndDateRange(request.getUserId(),
                        dateRange.getStartDate(),
                        dateRange.getEndDate());

        // 转换为日期到统计数据的映射
        Map<LocalDate, VideoDailyStat> statMap = dailyStats.stream()
                .collect(Collectors.toMap(
                        stat -> stat.getStatDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
                        Function.identity()
                ));

        // 生成日期范围内的所有日期
        LocalDate startLocalDate = dateRange.getStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate endLocalDate = dateRange.getEndDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        List<LocalDate> allDates = DateRange.generateAllDates(startLocalDate, endLocalDate);

        // 为每个日期创建统计数据，如果没有数据则设为0
        List<VideoDailyStat> resultList = allDates.stream()
                .map(date -> createDailyStatsVO(date, statMap.get(date)))
                .collect(Collectors.toList());
        return resultList;
    }



    private VideoDailyStat createDailyStatsVO(LocalDate date, VideoDailyStat stat) {

        if (stat == null) {
            stat =new VideoDailyStat();
            stat.setPlayNum(0);
            stat.setZanNum(0);
            stat.setCollectNum(0);
            stat.setCommentNum(0);
            stat.setShareNum(0);
            stat.setNoticeNum(0);
        }
        stat.setDate(formatDate(date));
        return stat;
    }
}
