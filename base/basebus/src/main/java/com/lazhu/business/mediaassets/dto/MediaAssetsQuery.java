package com.lazhu.business.mediaassets.dto;

import com.lazhu.support.base.BaseQuery;
import lombok.Data;

import java.util.List;

/**
 * 素材查询参数
 */
@Data
public class MediaAssetsQuery extends BaseQuery {


    private Long id;

    /**
     * 素材来源 1 系统 2用户素材
     */
    private String source;


    private Long userId;

    /**
     * 演员id
     */
    private Long actorId;


    /**
     * 演员名称
     */
    private String actorName;

    /**
     * 演员id
     */
    private List<Long> actorIds;

    /**
     * 素材类型
     */
    private String assetsType;

    /**
     * 标题
     */
    private String title;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 过滤的素材ID列表（通过分类筛选得到）
     */
    private List<Long> assetIds;

}
