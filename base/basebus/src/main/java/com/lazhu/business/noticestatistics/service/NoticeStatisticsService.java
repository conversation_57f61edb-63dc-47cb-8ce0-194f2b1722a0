package com.lazhu.business.noticestatistics.service;

import cn.hutool.core.date.DateUtil;
import com.lazhu.business.noticestatistics.dto.NoticeDailyStat;
import com.lazhu.business.noticestatistics.mapper.NoticeStatisticsMapper;
import com.lazhu.business.noticestatistics.vo.NoticeStatsVo;
import com.lazhu.business.videostatistics.dto.VideoDailyStat;
import com.lazhu.business.videostatistics.dto.DateRange;
import com.lazhu.business.videostatistics.dto.ChartRequest;
import com.lazhu.business.videostatistics.vo.ChartDataVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lazhu.support.base.BaseService;
import com.lazhu.business.noticestatistics.entity.NoticeStatistics;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 关注者数据统计 服务实现类
 * <AUTHOR>
 * @since 2025-08-26
 */
@Service
public class NoticeStatisticsService extends BaseService<NoticeStatistics> {

    @Autowired
    private NoticeStatisticsMapper noticeStatisticsMapper;


    // 指标名称映射
    private static final Map<String, String> METRIC_LABELS = new HashMap<>();
    static {
        METRIC_LABELS.put("add", "新增关注");
        METRIC_LABELS.put("cancel", "取消关注");
        METRIC_LABELS.put("net", "净增关注");
        METRIC_LABELS.put("total", "关注者总数");
    }

    /**
     *
     * @param userId
     * @param periodType  1:近7天，2:近30天，3:昨天
     * @return
     */
    public NoticeStatsVo getStatistics(Long userId, String periodType){

        // 获取当前周期数据
        DateRange currentDateRange  = DateRange.calculateDateRange(periodType, false);
        NoticeStatsVo currentStats = noticeStatisticsMapper.selectStatsByUserIdAndDateRange(
                userId, currentDateRange.getStartDate(), currentDateRange.getEndDate());

        // 获取对比周期数据
        DateRange previousDateRange = DateRange.calculateDateRange(periodType, true);
        NoticeStatsVo previousStats = noticeStatisticsMapper.selectStatsByUserIdAndDateRange(
                userId, previousDateRange.getStartDate(), previousDateRange.getEndDate());

        // 设置对比数据并计算增长率
        currentStats.setPreviousPeriodStats(previousStats);
        calculateGrowthRate(currentStats, previousStats);

        // 查询当前总关注数
        Integer totalNum = noticeStatisticsMapper.selectTodayNoticeNumByUserId(userId, new Date());
        currentStats.setTotalNum(totalNum);
        return currentStats;
    }

    private void calculateGrowthRate(NoticeStatsVo current, NoticeStatsVo previous) {
        current.setAddNumGrowthRate(calculateRate(current.getAddNum(), previous.getAddNum()));
        current.setCancelNumGrowthRate(calculateRate(current.getCancelNum(), previous.getCancelNum()));
        current.setNetNumGrowthRate(calculateRate(current.getNetNum(), previous.getNetNum()));
    }

    private String calculateRate(Integer current, Integer previous) {
        if (previous == null || previous == 0) {
            return "不可计算环比";
        }
        double rate = (current - previous) * 100.0 / previous;
        return String.format("%.2f%%", rate);
    }


    /**
     * 获取数据趋势
     * <AUTHOR>
     * @param[1] request
     * @return ChartDataVO
     * @time 2025/8/28 15:04
     */
    public ChartDataVO getVideoStatChart(ChartRequest request) {
        // 计算日期范围
        DateRange dateRange = DateRange.calculateDateRange(request);

        // 查询每日统计数据
        List<NoticeDailyStat> dailyStats = noticeStatisticsMapper
                .selectDailyStatsByUserIdAndDateRange(request.getUserId(),
                        dateRange.getStartDate(),
                        dateRange.getEndDate());

        // 转换为日期到统计数据的映射
        Map<LocalDate, NoticeDailyStat> statMap = dailyStats.stream()
                .collect(Collectors.toMap(
                        stat -> stat.getStatDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
                        Function.identity()
                ));

        // 生成间隔的日期点
        List<LocalDate> pointDates = DateRange.generatePointDates(
                dateRange.getStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
                dateRange.getEndDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate()
        );

        // 提取所有指标的数据
        Map<String, List<Integer>> metricsData = extractAllMetricsValues(pointDates, statMap);

        // 格式化日期为字符串
        List<String> dateLabels = pointDates.stream()
                .map(date -> formatDate(date))
                .collect(Collectors.toList());

        // 创建响应对象
        ChartDataVO result = new ChartDataVO();
        result.setDates(dateLabels);
        result.setMetricsData(metricsData);
        result.setMetricLabels(METRIC_LABELS);
        return result;
    }

    private Map<String, List<Integer>> extractAllMetricsValues(List<LocalDate> pointDates,
                                                               Map<LocalDate, NoticeDailyStat> statMap) {
        Map<String, List<Integer>> result = new HashMap<>();

        // 初始化所有指标的数据列表
        for (String metric : METRIC_LABELS.keySet()) {
            result.put(metric, new ArrayList<>());
        }

        // 为每个日期点提取所有指标的数据
        for (LocalDate date : pointDates) {
            NoticeDailyStat stat = statMap.get(date);
            result.get("add").add(stat != null ? stat.getAddNum() : 0);
            result.get("cancel").add(stat != null ? stat.getCancelNum() : 0);
            result.get("net").add(stat != null ? stat.getNetNum() : 0);
            result.get("total").add(stat != null ? stat.getTotalNum() : 0);
        }

        return result;
    }

    private String formatDate(LocalDate date) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return date.format(formatter);
    }

    /**
     * 数据详情
     * <AUTHOR>
     * @param[1] request
     * @return List<DailyStat>
     * @time 2025/8/28 15:46
     */
    public List<NoticeDailyStat> queryDetail(ChartRequest request){

        // 计算日期范围
        DateRange dateRange = DateRange.calculateDateRange(request);
        // 查询每日统计数据
        List<NoticeDailyStat> dailyStats = noticeStatisticsMapper
                .selectDailyStatsByUserIdAndDateRange(request.getUserId(),
                        dateRange.getStartDate(),
                        dateRange.getEndDate());

        // 转换为日期到统计数据的映射
        Map<LocalDate, NoticeDailyStat> statMap = dailyStats.stream()
                .collect(Collectors.toMap(
                        stat -> stat.getStatDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
                        Function.identity()
                ));

        // 生成日期范围内的所有日期
        LocalDate startLocalDate = dateRange.getStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate endLocalDate = dateRange.getEndDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        List<LocalDate> allDates = DateRange.generateAllDates(startLocalDate, endLocalDate);

        // 为每个日期创建统计数据，如果没有数据则设为0
        List<NoticeDailyStat> resultList = allDates.stream()
                .map(date -> createDailyStatsVO(date, statMap.get(date)))
                .collect(Collectors.toList());
        return resultList;
    }

    private NoticeDailyStat createDailyStatsVO(LocalDate date, NoticeDailyStat stat) {

        if (stat == null) {
            stat =new NoticeDailyStat();
            stat.setAddNum(0);
            stat.setCancelNum(0);
            stat.setNetNum(0);
            stat.setTotalNum(0);
        }
        stat.setDate(formatDate(date));
        return stat;
    }
}
