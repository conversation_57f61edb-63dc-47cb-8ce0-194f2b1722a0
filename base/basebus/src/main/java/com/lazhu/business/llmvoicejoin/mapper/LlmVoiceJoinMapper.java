package com.lazhu.business.llmvoicejoin.mapper;

import com.lazhu.business.llmvoicejoin.entity.LlmVoiceJoin;
import com.lazhu.support.base.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 大模型音色素材关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Repository
public interface LlmVoiceJoinMapper extends BaseMapper<LlmVoiceJoin> {

    /**
     * 根据模型id和素材id查询音色id
     * @param llmId 模型id
     * @param actorId 角色id
     */
    LlmVoiceJoin queryVoiceId(@Param("llmId") Long llmId, @Param("actorId") Long actorId);

}
