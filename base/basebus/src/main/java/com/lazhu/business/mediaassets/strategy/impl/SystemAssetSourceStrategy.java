package com.lazhu.business.mediaassets.strategy.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lazhu.business.mediaassets.dto.MediaAssetsDTO;
import com.lazhu.business.mediaassets.dto.MediaAssetsQuery;
import com.lazhu.business.mediaassets.strategy.MediaAssetSourceStrategy;
import com.lazhu.business.mediaassets.strategy.MediaAssetStrategyFactory;
import com.lazhu.common.enums.MediaAssetSourceEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 系统素材策略实现
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Component
public class SystemAssetSourceStrategy implements MediaAssetSourceStrategy {

    @Autowired
    private MediaAssetStrategyFactory mediaAssetStrategyFactory;

    @Override
    public Page<MediaAssetsDTO> queryAssets(MediaAssetsQuery query) {
        // 根据素材类型获取对应的策略并查询
        return mediaAssetStrategyFactory.getStrategy(query.getAssetsType()).queryAssets(query);
    }

    @Override
    public MediaAssetsDTO queryAssetDetail(MediaAssetsQuery query) {
        Page<MediaAssetsDTO> mediaAssetsDTOPage = mediaAssetStrategyFactory.getStrategy(query.getAssetsType()).queryAssets(query);
        List<MediaAssetsDTO> records = mediaAssetsDTOPage.getRecords();
        if(CollectionUtil.isNotEmpty(records)){
            return CollectionUtil.getFirst(records);
        }
        return null ;
    }

    @Override
    public Long countAssets(MediaAssetsQuery query) {
        return mediaAssetStrategyFactory.getStrategy(query.getAssetsType()).countAssets(query);
    }

    @Override
    public MediaAssetsDTO uploadAsset(MediaAssetsDTO param) {
        return mediaAssetStrategyFactory.getStrategy(param.getAssetsType()).uploadAsset(param);
    }

    @Override
    public Boolean updateAsset(Long id, String title, String assetType) {
        return mediaAssetStrategyFactory.getStrategy(assetType).updateAsset(id, title);
    }

    @Override
    public Boolean batchDeleteAssets(List<Long> ids, String assetType) {
        return mediaAssetStrategyFactory.getStrategy(assetType).batchDeleteAssets(ids);
    }
}