package com.lazhu.business.mediaassets.strategy.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lazhu.baseai.vector.KnowledgeTool;
import com.lazhu.business.mediaassets.dto.MediaAssetsDTO;
import com.lazhu.business.mediaassets.dto.MediaAssetsQuery;
import com.lazhu.business.mediaassets.entity.MediaAssetsImg;
import com.lazhu.business.mediaassets.entity.MediaAssetsVideo;
import com.lazhu.business.mediaassets.service.MediaAssetsVideoService;
import com.lazhu.business.mediaassets.strategy.MediaAssetStrategy;
import com.lazhu.common.enums.MediaAssetSourceEnum;
import com.lazhu.common.enums.MediaAssetTypeEnum;
import com.lazhu.common.media.MediaUtil;
import com.lazhu.common.utils.OssUtil;
import com.lazhu.montage.service.VideoMontageService;
import com.lazhu.montage.service.VideoMontageTool;
import io.netty.util.internal.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.glassfish.jaxb.runtime.v2.runtime.reflect.opt.Const;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 视频素材策略实现
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Component
public class VideoAssetStrategy implements MediaAssetStrategy {

    @Autowired
    private MediaAssetsVideoService mediaAssetsVideoService;

    @Autowired
    private OssUtil ossUtil;

    @Autowired
    private VideoMontageTool videoMontageTool ;

    @Autowired
    private KnowledgeTool knowledgeTool;
    @Override
    public Page<MediaAssetsDTO> queryAssets(MediaAssetsQuery query) {
        // 执行查询
        Page<MediaAssetsVideo> videoPage = mediaAssetsVideoService.queryPage(BeanUtil.beanToMap(query));

        // 转换为统一素材对象
        List<MediaAssetsDTO> unifiedAssets = videoPage.getRecords().stream().map(video ->
                new MediaAssetsDTO(
                        video.getId(),
                        "2", // 视频类型
                        MediaAssetSourceEnum.SYSTEM.getSource(),
                        video.getTitle(),
                        video.getVideoUrl(),
                        video.getDuration(),
                        video.getCoverImg(),
                        video.getCreateTime(),
                        video.getHasAudio(),
                        video.getSize()
                )
        ).collect(Collectors.toList());

        // 创建返回的分页对象
        Page<MediaAssetsDTO> resultPage = new Page<>(videoPage.getCurrent(), videoPage.getSize());
        resultPage.setRecords(unifiedAssets);
        resultPage.setTotal(videoPage.getTotal());

        return resultPage;
    }

    @Override
    public Long countAssets(MediaAssetsQuery query) {
        return mediaAssetsVideoService.count(BeanUtil.beanToMap(query));
    }


    @Override
    public MediaAssetsDTO uploadAsset(MediaAssetsDTO param) {
        MediaAssetsVideo video = new MediaAssetsVideo();
        video.setTitle(param.getTitle());
        String fileUrl = param.getUrl();
        video.setHasAudio(param.getHasAudio());
        video.setVideoUrl(fileUrl);
        Long duration = MediaUtil.getDuration(fileUrl);
        video.setDuration(duration);
        File coverImgFile = MediaUtil.getVideoCover(fileUrl);
        String coverImgUrl = ossUtil.upload(coverImgFile);
        video.setCoverImg(coverImgUrl);
        FileUtil.del(coverImgFile);
        video.setSize(param.getSize());
        video.setStorage(param.getStorage());

        String content =video.getTitle();
        String size =video.getSize();
        List<Integer> integers = parseResolution(size);
        Integer width = integers.get(0);
        Integer height = integers.get(1);
        // 同步
        String vectorId = knowledgeTool.createKnowledge(content, height, width, duration, MediaAssetTypeEnum.VIDEO.getType(), video.getHasAudio(), null);
        video.setVectorId(vectorId);




        mediaAssetsVideoService.save(video);
        return new MediaAssetsDTO(
                video.getId(),
                "2", // 视频类型
                MediaAssetSourceEnum.SYSTEM.getSource(),
                video.getTitle(),
                video.getVideoUrl(),
                video.getDuration(),
                coverImgUrl,
                video.getCreateTime(),
                video.getHasAudio(),
                video.getSize()
        );
    }

    @Override
    public Boolean updateAsset(Long id, String title) {
        MediaAssetsVideo video = new MediaAssetsVideo();
        video.setId(id);
        video.setTitle(title);
        mediaAssetsVideoService.update(video);

        String content =video.getTitle();
        MediaAssetsVideo mediaAssetsVideo = mediaAssetsVideoService.queryById(id);
        String vectorId = mediaAssetsVideo.getVectorId();
        if(StringUtils.isNotBlank(vectorId)){
            List<Integer> integers = parseResolution(mediaAssetsVideo.getSize());
            Integer width = integers.get(0);
            Integer height = integers.get(1);
            // 同步
            knowledgeTool.createKnowledge(content, height, width, mediaAssetsVideo.getDuration(), MediaAssetTypeEnum.VIDEO.getType(), mediaAssetsVideo.getHasAudio(), vectorId);
        }
        return true;
    }

    @Override
    public Boolean batchDeleteAssets(List<Long> ids) {

        // 同步删除
        List<MediaAssetsVideo> mediaAssetsVideos = mediaAssetsVideoService.queryByIds(ids);
        for (MediaAssetsVideo mediaAssetsVideo : mediaAssetsVideos) {
            if(StringUtils.isNotBlank(mediaAssetsVideo.getVectorId())){
                knowledgeTool.deleteKnowledge(MediaAssetTypeEnum.VIDEO.getType(), mediaAssetsVideo.getHasAudio(),mediaAssetsVideo.getVectorId());
            }
        }
        return mediaAssetsVideoService.batchDelById(ids) > 0;
    }


    /**
     * 从字符串中解析分辨率数值
     * @param input 格式为 "宽度*高度|宽高比" 的字符串
     * @return 包含宽度和高度的整型数组，解析失败返回null
     */
    public static List<Integer> parseResolution(String input) {
        // 方法1：使用正则表达式（推荐）
        Pattern pattern = Pattern.compile("(\\d+)\\*(\\d+)");
        Matcher matcher = pattern.matcher(input);

        if (matcher.find()) {
            try {
                int width = Integer.parseInt(matcher.group(1));
                int height = Integer.parseInt(matcher.group(2));
                return CollectionUtil.newArrayList(width,height);
            } catch (NumberFormatException e) {
                // 数值转换失败
            }
        }
        return null; // 解析失败
    }

}