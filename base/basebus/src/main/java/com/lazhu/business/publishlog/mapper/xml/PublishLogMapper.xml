<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lazhu.business.publishlog.mapper.PublishLogMapper">

	<select id="selectIdPage" resultType="com.lazhu.business.publishlog.entity.PublishLog">
		select * from c_publish_log
		<where>
			<if test="params.contentId != null">
				and content_id = #{params.contentId}
			</if>
			<if test="params.userId != null">
				and user_id = #{params.userId}
			</if>
			<if test="params.actorId != null">
				and actor_id = #{params.actorId}
			</if>
			<if test="params.title != null and params.title != ''">
			    and title like concat(concat('%',#{params.title}),'%')
			</if>
			<if test="params.description != null and params.description != ''">
			    and description like concat(concat('%',#{params.description}),'%')
			</if>
			<if test="params.publishType != null">
				and publish_type = #{params.publishType}
			</if>
			<if test="params.publishTime != null">
				and publish_time = #{params.publishTime}
			</if>
			<if test="params.publishStatus != null and params.publishStatus != ''">
			    and publish_status like concat(concat('%',#{params.publishStatus}),'%')
			</if>
			<if test="params.publishId != null and params.publishId != ''">
			    and publish_id like concat(concat('%',#{params.publishId}),'%')
			</if>
			<if test="params.channel != null">
				and channel = #{params.channel}
			</if>
			<if test="params.channelAccount != null and params.channelAccount != ''">
				and channel_account like concat(concat('%',#{params.channelAccount}),'%')
			</if>
			<if test="params.publishEndTime != null">
				and publish_time <![CDATA[<=]]> #{params.publishEndTime}
			</if>
		</where>
	</select>

	<select id="readPage" resultType="com.lazhu.business.publishlog.vo.PublishLogVO">
		select
			pl.channel_account as channelAccount,
			pl.channel as channel,
			c.content_type as contentType,
			pl.title as title,
			pl.publish_status as publishStatus,
			pl.publish_time as publishTime,
			pl.content_id as contentId,
			pl.meta_data as metaData
		from c_publish_log pl
				 left join c_content c on pl.content_id =c.id
		<where>
			<if test="params.channel != null">
				and pl.channel = #{params.channel}
			</if>
			<if test="params.channelAccount != null and params.channelAccount != ''">
				and pl.channel_account like concat(concat('%',#{params.channelAccount}),'%')
			</if>
			<if test="params.contentType != null and params.contentType != ''">
				and c.content_type like concat(concat('%',#{params.contentType}),'%')
			</if>
			<if test="params.title != null and params.title != ''">
				and pl.title like concat(concat('%',#{params.title}),'%')
			</if>
			<if test="params.userId != null">
				and pl.user_id = #{params.userId}
			</if>
		</where>
		order by  pl.publish_time desc
	</select>
</mapper>
