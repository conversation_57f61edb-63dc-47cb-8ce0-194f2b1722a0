<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lazhu.business.userchannelaccount.mapper.UserChannelAccountMapper">

    <select id="selectIdPage" resultType="com.lazhu.business.userchannelaccount.entity.UserChannelAccount">
        select * from u_user_channel_account
        <where>
            <if test="params.userId != null">
                and user_id = #{params.userId}
            </if>
            <if test="params.channel != null">
                and channel = #{params.channel}
            </if>
            <if test="params.account != null and params.account != ''">
                and account like concat(concat('%',#{params.account}),'%')
            </if>
            <if test="params.cookie != null and params.cookie != ''">
                and cookie like concat(concat('%',#{params.cookie}),'%')
            </if>
            <if test="params.loginStatus != null">
                and login_status = #{params.loginStatus}
            </if>
        </where>
    </select>


    <select id="readPage" resultType="com.lazhu.business.userchannelaccount.vo.UserChannelAccountVO">
        SELECT
        uca.id as id,
        uca.account as account,
        uca.channel as channel,
        uca.create_time as createTime,
        a.nick_name AS actorName,
        a.id as actorId ,
        uca.update_time as updateTime,
        uca.nick_name as nickName,
        uca.login_status as loginStatus,
        uca.avatar as avatar,
        COUNT(pl.id) as publishedCount
        FROM
        u_user_channel_account uca
        LEFT JOIN c_actor a ON uca.actor_id = a.id
        left join c_publish_log pl on uca.actor_id =pl.actor_id and pl.publish_status ='1'
        <where>
            <if test="params.userId != null">
                and uca.user_id = #{params.userId}
            </if>
            <if test="params.actorId != null">
                and uca.actor_id = #{params.actorId}
            </if>
            <if test="params.channel != null">
                and uca.channel = #{params.channel}
            </if>
            <if test="params.account != null and params.account != ''">
                and uca.account like concat(concat('%',#{params.account}),'%')
            </if>
            <if test="params.loginStatus != null">
                and uca.login_status = #{params.loginStatus}
            </if>
        </where>
        GROUP BY uca.id
        order BY uca.create_time desc
    </select>
</mapper>
