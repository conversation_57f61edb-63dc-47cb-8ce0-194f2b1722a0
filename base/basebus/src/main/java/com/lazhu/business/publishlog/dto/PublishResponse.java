package com.lazhu.business.publishlog.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class PublishResponse implements Serializable {


    /**
     * 渠道类型
     */
    private Integer channelType;

    /**
     * 渠道账号
     */
    private String channelAccount;

    /**
     * 发布结果 0 待发布 1发布中 2发布成功  3发布失败
     */
    private String publishStatus;

    /**
     * 错误信息
     */
    private String errorMsg;

    public PublishResponse(Integer channelType, String channelAccount, String publishStatus, String errorMsg) {
        this.channelType = channelType;
        this.channelAccount = channelAccount;
        this.publishStatus = publishStatus;
        this.errorMsg = errorMsg;
    }

    /**
     * 成功
     */
    public static PublishResponse success(Integer channelType, String channelAccount) {
        return new PublishResponse(channelType, channelAccount, "2", null);
    }

    /**
     * 失败
     */
    public static PublishResponse fail(Integer channelType, String channelAccount, String errorMsg) {
        return new PublishResponse(channelType, channelAccount, "3", errorMsg);
    }

    /**
     * 待发布
     */
    public static PublishResponse wait(Integer channelType, String channelAccount) {
        return new PublishResponse(channelType, channelAccount, "0", null);
    }
}
