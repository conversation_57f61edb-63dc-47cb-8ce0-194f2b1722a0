package com.lazhu.business.mediaassets.mapper;

import com.lazhu.business.mediaassets.entity.MediaAssetsVideo;
import com.lazhu.support.base.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 系统视频素材表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
public interface MediaAssetsVideoMapper extends BaseMapper<MediaAssetsVideo> {

    Long count(@Param("params") Map<String, Object> params);

    List<MediaAssetsVideo> queryByIds(@Param("ids") List<Long> ids);

    List<MediaAssetsVideo> queryByVids(@Param("vids") List<String> vids);
}
