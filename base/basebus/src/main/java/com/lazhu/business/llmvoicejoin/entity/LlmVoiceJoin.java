package com.lazhu.business.llmvoicejoin.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.lazhu.support.base.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * <p>
 * 大模型音色素材关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@TableName("llm_voice_join")
@Data
@EqualsAndHashCode(callSuper = true)
public class LlmVoiceJoin extends BaseModel {
    private static final long serialVersionUID = 1L;
    //asserts_type 素材类型（1 系统素材 2用户素材） 
    @TableField("assets_type")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer assetsType;
    //actor_id ip角色id
    @TableField("actor_id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long actorId;
    //voice_id 音色id 
    @TableField("voice_id")
    private String voiceId;
    //llm_id 大模型id 
    @TableField("llm_id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long llmId;
    //创建人
    @TableField("create_by")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long createBy;
    
}
