package com.lazhu.business.llmvoicejoin.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.lazhu.support.base.BaseQuery;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * <p>
 * 大模型音色素材关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LlmVoiceJoinQuery extends BaseQuery {
    //asserts_type 素材类型（1 系统素材 2用户素材） 
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer assetsType;
    //actor_id ip角色id
    @TableField("actor_id")
    @J<PERSON>NField(serializeUsing = ToStringSerializer.class)
    private Long actorId;
    //voice_id 音色id 
    private String voiceId;
    //llm_id 大模型id 
    @J<PERSON>NField(serializeUsing = ToStringSerializer.class)
    private Long llmId;

}
