package com.lazhu.business.content.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class ContentAudioInfo implements java.io.Serializable{

    private String ssml;

    private String audioUrl;

    @JSONField(serializeUsing = ToStringSerializer.class)
    private long duration;

    @JSONField(serializeUsing = ToStringSerializer.class)
    private long firstDelay;
}
