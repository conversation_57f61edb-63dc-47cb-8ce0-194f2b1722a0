<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lazhu.business.mediaassets.mapper.MediaAssetsCategoryRelMapper">

    <!-- 根据分类ID查询关联素材 -->
    <select id="queryByCategory" resultType="com.lazhu.business.mediaassets.entity.MediaAssetsCategoryJoin">
        SELECT *
        FROM c_media_assets_category_join
        WHERE
        category_id = #{categoryId}
        AND asset_type = #{assetType}
        AND source = #{source}
    </select>

    <!-- 删除素材的所有分类关联 -->
    <update id="deleteByAsset">
        delete from c_media_assets_category_join
        WHERE asset_id in
        <foreach collection="assetIds" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        AND asset_type = #{assetType}
        AND source = #{source}
    </update>

</mapper>