package com.lazhu.business.content.dto;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lazhu.business.content.entity.Content;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;

import java.io.Serial;
import java.util.Date;
import java.util.List;

@Data
public class ContentDTO implements java.io.Serializable{

    @Serial
    private static final long serialVersionUID = 1L;

    //id
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;
    //user_id 所属用户
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long userId;
    //actor_id 演员id
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long actorId;
    //演员姓名
    private String actorName;
    //topic 创作主题
    private String topic;
    //title 标题
    private String title;
    //status 状态 (0:草稿, 1:完成, 2:已发布)
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer status;
    //content_type 类型
    private String contentType;
    //summary 摘要
    private String summary;
    //content 文案内容
    private String content;
    //cover_img 封面图
    private List<String> coverImg;
    //media_url 视频链接
    private String mediaUrl;
    //视频时长 （毫秒）
    private Long duration;
    // 创建时间
    private Date createTime;
    // 更新时间
    private Date updateTime;
    // 元数据
    private JSONObject metaData;
    private String exposeMediaUrl;

    // ssml 文本数组（前端以 JSON array 传入/返回）
    private List<ContentAudioInfo> ssml;

    // 特效模板id
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long effectsTplId;

    private Integer curStep;

    // ==== Converter methods ====
    public static ContentDTO fromEntity(Content content) {
        if (content == null) return null;
        ContentDTO dto = new ContentDTO();
        BeanUtil.copyProperties(content, dto, "coverImg", "metaData", "ssml");
        if (StrUtil.isNotBlank(content.getCoverImg())) {
            dto.setCoverImg(JSONArray.parseArray(content.getCoverImg(), String.class));
        }
        if (StrUtil.isNotBlank(content.getMetaData())) {
            dto.setMetaData(JSONObject.parseObject(content.getMetaData()));
        }
        if (StrUtil.isNotBlank(content.getSsml())) {
            dto.setSsml(JSONArray.parseArray(content.getSsml(), ContentAudioInfo.class));
        }
        return dto;
    }

    public Content toEntity() {
        Content content = new Content();
        BeanUtil.copyProperties(this, content, "coverImg", "metaData", "ssml");
        if (this.getCoverImg() != null) {
            content.setCoverImg(JSONArray.toJSONString(this.getCoverImg()));
        }
        if (this.getMetaData() != null) {
            content.setMetaData(JSONObject.toJSONString(this.getMetaData()));
        }
        if (this.getSsml() != null) {
            content.setSsml(JSONArray.toJSONString(this.getSsml()));
        }
        return content;
    }
}
