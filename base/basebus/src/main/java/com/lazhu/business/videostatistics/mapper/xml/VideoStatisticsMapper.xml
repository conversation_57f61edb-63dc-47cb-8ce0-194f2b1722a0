<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lazhu.business.videostatistics.mapper.VideoStatisticsMapper">

	<select id="selectIdPage" resultType="com.lazhu.business.videostatistics.entity.VideoStatistics">
		select * from c_video_statistics
		<where>
			<if test="params.userId != null">
				and user_id = #{params.userId}
			</if>
			<if test="params.actorId != null">
				and actor_id = #{params.actorId}
			</if>
			<if test="params.contentId != null">
				and content_id = #{params.contentId}
			</if>
			<if test="params.publishId != null">
				and publish_id = #{params.publishId}
			</if>
			<if test="params.playNum != null">
				and play_num = #{params.playNum}
			</if>
			<if test="params.zanNum != null">
				and zan_num = #{params.zanNum}
			</if>
			<if test="params.collectNum != null">
				and collect_num = #{params.collectNum}
			</if>
			<if test="params.commentNum != null">
				and comment_num = #{params.commentNum}
			</if>
			<if test="params.shareNum != null">
				and share_num = #{params.shareNum}
			</if>
			<if test="params.noticeNum != null">
				and notice_num = #{params.noticeNum}
			</if>
			<if test="params.statisticsDate != null">
				and statistics_date = #{params.statisticsDate}
			</if>
		</where>
	</select>
</mapper>
