package com.lazhu.business.mediaassets.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.lazhu.support.base.BaseQuery;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * <p>
 * 系统图片素材表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MediaAssetsImgQuery extends BaseQuery {
    //title 描述 
    private String title;
    //img_url 素材链接 
    private String imgUrl;

    // 尺寸（宽 X 高）
    private String size;

    // 向量id
    private String vectorId;
}
