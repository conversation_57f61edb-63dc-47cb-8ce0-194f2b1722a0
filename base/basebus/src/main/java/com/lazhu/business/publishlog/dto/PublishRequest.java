package com.lazhu.business.publishlog.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 创建发布任务请求参数
 */
@Data
public class PublishRequest {

    /**
     * 内容ID
     */
    private Long contentId;

    // 发布方式;1 实时发布 2定时发布
    private Integer publishType;

    // 发布时间
    private Date publishTime;

    // 发布账号 （多个账号时，会发布到多个平台）
    private List<Account> accountList;

    /**
     * 发布数据
     */
    private PublishMetadata metaData;


    @Data
    public static class Account {
        // 第三方平台账号
        private String account;

        // 发布平台
        private Integer channelType;
    }

}
