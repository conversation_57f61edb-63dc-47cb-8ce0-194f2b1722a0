<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lazhu.business.mediaassets.mapper.MediaAssetsCategoryMapper">

    <!-- 分页查询 -->
    <select id="selectIdPage" resultType="com.lazhu.business.mediaassets.entity.MediaAssetsCategory">
        SELECT *
        FROM c_media_assets_category
        <where>
            <if test="params.categoryName != null and params.categoryName != ''">
                AND category_name LIKE CONCAT('%', #{params.categoryName}, '%')
            </if>
            <if test="params.parentId != null">
                AND parent_id = #{params.parentId}
            </if>
            <if test="params.type != null and params.type != ''">
                AND (type = #{params.type} OR type = '0')
            </if>
            <if test="params.status != null">
                AND status = #{params.status}
            </if>
            ORDER BY sort_order ASC, create_time DESC
        </where>
    </select>

</mapper>