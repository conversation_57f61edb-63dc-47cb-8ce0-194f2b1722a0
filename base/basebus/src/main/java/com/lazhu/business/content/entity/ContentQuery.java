package com.lazhu.business.content.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.lazhu.support.base.BaseQuery;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * <p>
 * 内容管理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ContentQuery extends BaseQuery {
    //user_id 所属用户 
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long userId;
    //actor_id 演员id 
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long actorId;
    //topic 创作主题 
    private String topic;
    //title 标题 
    private String title;
    //status 状态 (0:草稿, 1:完成, 2:已发布) 
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer status;
    //content_type 类型 
    private String contentType;
    //content 文案内容 
    private String content;
    //cover_img 封面图 
    private String coverImg;
    //media_url 视频链接 
    private String mediaUrl;
    // 特效模板id
    private Long effectsTplId;
}
