package com.lazhu.business.mediaassets.service;

import com.lazhu.business.mediaassets.entity.MediaAssetsImg;
import com.lazhu.business.mediaassets.mapper.MediaAssetsImgMapper;
import com.lazhu.support.base.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 系统图片素材表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Service
public class MediaAssetsImgService extends BaseService<MediaAssetsImg> {

    @Autowired
    private MediaAssetsImgMapper mapper;

    public Long count(Map<String, Object> params) {
        return mapper.count(params);
    }


    public List<MediaAssetsImg> queryByIds(List<Long> ids) {
        return mapper.queryByIds(ids);
    }

    /**
     * 根据向量id查询图片素材
     * @param vids
     * @return
     */
    public List<MediaAssetsImg> queryByVids(List<String> vids) {
        return mapper.queryByVids(vids);
    }
}
