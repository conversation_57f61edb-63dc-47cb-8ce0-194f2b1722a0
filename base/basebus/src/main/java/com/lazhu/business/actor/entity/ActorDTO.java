package com.lazhu.business.actor.entity;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.lazhu.business.actorcontentexample.entity.ActorContentExample;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ActorDTO implements Serializable {

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;
    //nick_name 昵称
    private String nickName;
    //head_img 头像
    private String headImg;
    //profile 简介
    private String profile;
    //role_prompt 人设提示词
    private String rolePrompt;
    //视频素材数量
    private Integer videoMaterialCount;
    //音频素材数量
    private Integer audioMaterialCount;
    // 示例内容
    private List<ActorContentExample> exampleContent;
    // 头衔
    private String title;
    // 人物声音链接
    private String voiceUrl;

    //duration_ms 时长 (秒)
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long duration;

    private Long createBy;

    /**
     * 根据actor 构建 ActorDTO
     */
    public static ActorDTO buildFromActor(Actor actor) {
        ActorDTO actorDTO = new ActorDTO();
        BeanUtil.copyProperties(actor, actorDTO);
        return actorDTO;
    }

}
