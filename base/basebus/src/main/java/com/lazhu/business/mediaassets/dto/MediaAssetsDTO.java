package com.lazhu.business.mediaassets.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 素材列表dto
 */
@Data
public class MediaAssetsDTO implements Serializable {

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    private String assetsType;

    /**
     * 素材来源 1-系统素材 2-用户上传素材
     */
    private String source;

    private String title;

    private String url;

    /**
     * 封面
     */
    private String coverImg;

    /**
     * 时长
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long duration;

    /**
     * 角色id
     */
    @JSONField(serializeUsing =  ToStringSerializer.class)
    private Long actorId;

    /**
     * 角色名称
     */
    private String actorName;


    private Date createTime;

    private Long createBy;
    // 是否保留声音（1、是 0、否）
    private String hasAudio;

    private String size;

    // 视频文件大小
    private String storage;

    public MediaAssetsDTO() {

    }

    public MediaAssetsDTO(Long id, String type,String source, String title, String url, Long durationMs,
                          String coverImg, Date createTime) {
        this(id, type, source, title, url, durationMs, coverImg, createTime,null,null,null,null);
    }

    public MediaAssetsDTO(Long id, String type,String source, String title, String url, Long durationMs,
                          String coverImg, Date createTime,String hasAudio,String size) {
        this(id, type, source, title, url, durationMs, coverImg, createTime,null,null,hasAudio,size);
    }

    public MediaAssetsDTO(Long id, String type,String source, String title, String url, Long durationMs,
                          String coverImg, Date createTime,String size) {
        this(id, type, source, title, url, durationMs, coverImg, createTime,null,null,null,size);
    }

    public MediaAssetsDTO(Long id, String type, String source, String title,
                          String url, Long durationMs,
                          String coverImg, Date createTime, Long actorId,String actorName,String hasAudio,String size) {
        this.id = id;
        this.assetsType = type;
        this.source = source;
        this.title = title;
        this.url = url;
        this.duration = durationMs;
        this.coverImg = coverImg;
        this.createTime = createTime;
        this.actorId = actorId;
        this.actorName = actorName;
        this.hasAudio=hasAudio;
        this.size=size;
    }
}
