package com.lazhu.business.mediaassets.strategy;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lazhu.business.mediaassets.dto.MediaAssetsDTO;
import com.lazhu.business.mediaassets.dto.MediaAssetsQuery;

/**
 * 基于来源的素材操作策略接口
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
public interface MediaAssetSourceStrategy {

    /**
     * 查询素材列表
     *
     * @param query 查询条件
     * @return 素材分页列表
     */
    Page<MediaAssetsDTO> queryAssets(MediaAssetsQuery query);


    /**
     * 查询素材详情
     * <AUTHOR>
     * @param[1] query
     * @return MediaAssetsDTO
     * @time 2025/8/11 15:35
     */
    MediaAssetsDTO queryAssetDetail(MediaAssetsQuery query);

    /**
     * 统计素材数量
     *
     * @param query 查询条件
     * @return 素材数量
     */
    Long countAssets(MediaAssetsQuery query);

    /**
     * 上传素材
     *
     * @return 上传结果
     */
    MediaAssetsDTO uploadAsset(MediaAssetsDTO param);

    /**
     * 更新素材
     *
     * @param id 素材ID
     * @param title 标题
     * @param assetType 素材类型
     * @return 更新结果
     */
    Boolean updateAsset(Long id, String title, String assetType);

    /**
     * 批量删除素材
     *
     * @param ids 素材ID列表
     * @param assetType 素材类型
     * @return 删除结果
     */
    Boolean batchDeleteAssets(java.util.List<Long> ids, String assetType);
}