package com.lazhu.business.userchannelaccount.mapper;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lazhu.business.userchannelaccount.entity.UserChannelAccount;
import com.lazhu.business.userchannelaccount.entity.UserChannelAccountQuery;
import com.lazhu.business.userchannelaccount.vo.UserChannelAccountVO;
import com.lazhu.support.base.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UserChannelAccountMapper extends BaseMapper<UserChannelAccount> {


    List<UserChannelAccountVO> readPage(Page<UserChannelAccountVO> page, @Param("params") UserChannelAccountQuery query);

}
