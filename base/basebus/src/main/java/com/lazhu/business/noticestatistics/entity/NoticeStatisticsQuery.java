package com.lazhu.business.noticestatistics.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.lazhu.support.base.BaseQuery;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * 关注者数据统计
 * <AUTHOR>
 * @since 2025-08-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class NoticeStatisticsQuery extends BaseQuery {
    //user_id 用户id 
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long userId;
    //actor_id 角色id 
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long actorId;
    //add_num 新增关注 
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer addNum;
    //cancel_num 取消关注 
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer cancelNum;
    //net_num 净增关注 
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer netNum;
    //total_num 关注者总数 
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer totalNum;
    //statistics_date 统计日期
    @TableField("statistics_date")
    private Date statisticsDate;
}
