package com.lazhu.business.mediaassets.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lazhu.support.base.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * <p>
 * 系统图片素材表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@TableName("c_media_assets_img")
@Data
@EqualsAndHashCode(callSuper = true)
public class MediaAssetsImg extends BaseModel {
    private static final long serialVersionUID = 1L;
    //title 描述 
    @TableField("title")
    private String title;
    //img_url 素材链接 
    @TableField("img_url")
    private String imgUrl;
    // 尺寸（宽 X 高）
    @TableField("size")
    private String size;
    // 向量id
    @TableField("vector_id")
    private String vectorId;
}
