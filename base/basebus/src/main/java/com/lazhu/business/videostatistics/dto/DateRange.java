package com.lazhu.business.videostatistics.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;

@Data
@AllArgsConstructor
public class DateRange {

    private Date startDate;
    private Date endDate;

    public static DateRange calculateDateRange(String type, boolean isPreviousPeriod) {
        LocalDate endDate;
        LocalDate startDate;
        Map<String,Date> map =new HashMap<>();
        switch (type) {
            case "1":
                if (isPreviousPeriod) {
                    // 前天
                    endDate = LocalDate.now().minusDays(2);
                    startDate = endDate;
                } else {
                    // 昨天
                    endDate = LocalDate.now().minusDays(1);
                    startDate = endDate;
                }
                break;

            case "2":
                if (isPreviousPeriod) {
                    // 上上个7天
                    endDate = LocalDate.now().minusDays(8);
                    startDate = endDate.minusDays(6);
                } else {
                    // 近7天
                    endDate = LocalDate.now().minusDays(1);
                    startDate = endDate.minusDays(6);
                }
                break;

            case "3":
                if (isPreviousPeriod) {
                    // 上上个30天
                    endDate = LocalDate.now().minusDays(31);
                    startDate = endDate.minusDays(29);
                } else {
                    // 近30天
                    endDate = LocalDate.now().minusDays(1);
                    startDate = endDate.minusDays(29);
                }
                break;

            default:
                throw new IllegalArgumentException("不支持的统计类型: " + type);
        }

        return new DateRange(Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant()),
                Date.from(endDate.atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant()));
    }


    public static DateRange calculateDateRange(ChartRequest request) {
        LocalDate endDate = LocalDate.now();
        LocalDate startDate;

        switch (request.getRangeType()) {
            case "1":
                startDate = endDate.minusDays(7);
                endDate=endDate.minusDays(1);
                break;
            case "2":
                startDate = endDate.minusDays(30);
                endDate=endDate.minusDays(1);
                break;
            case "3":
                if (request.getStartDate() != null && request.getEndDate() != null) {
                    startDate = request.getStartDate().toInstant()
                            .atZone(ZoneId.systemDefault()).toLocalDate();
                    endDate = request.getEndDate().toInstant()
                            .atZone(ZoneId.systemDefault()).toLocalDate();
                } else {
                    throw new IllegalArgumentException("自定义范围需要提供开始和结束日期");
                }
                break;
            default:
                throw new IllegalArgumentException("不支持的日期范围类型: " + request.getRangeType());
        }

        return new DateRange(
                Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant()),
                Date.from(endDate.atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant())
        );
    }


    public static List<LocalDate> generatePointDates(LocalDate start, LocalDate end) {
        List<LocalDate> pointDates = new ArrayList<>();
        long totalDays = ChronoUnit.DAYS.between(start, end);

        // 如果总天数小于等于6，直接返回所有日期
        if (totalDays <= 6) {
            for (int i = 0; i <= totalDays; i++) {
                pointDates.add(start.plusDays(i));
            }
            return pointDates;
        }

        // 对于大于6天的情况，生成7个点，包括开始和结束日期
        pointDates.add(start); // 第一个点总是开始日期

        // 计算中间5个点的间隔（使用浮点数计算更精确）
        double interval = totalDays / 6.0;

        // 添加中间5个点
        for (int i = 1; i < 6; i++) {
            // 使用四舍五入确保日期是整数
            long daysToAdd = (long) Math.ceil(interval * i);
            LocalDate date = start.plusDays(daysToAdd);

            // 确保日期不重复且不晚于结束日期
            if (date.isAfter(pointDates.get(pointDates.size() - 1)) && !date.isAfter(end)) {
                pointDates.add(date);
            } else {
                // 如果日期与前一个点相同或超过了结束日期，则使用下一个可用日期
                LocalDate nextDate = pointDates.get(pointDates.size() - 1).plusDays(1);
                if (!nextDate.isAfter(end)) {
                    pointDates.add(nextDate);
                } else {
                    // 如果没有更多可用日期，跳出循环
                    break;
                }
            }
        }

        // 确保最后一个点是结束日期
        if (pointDates.isEmpty() || !pointDates.get(pointDates.size() - 1).equals(end)) {
            pointDates.add(end);
        }

        // 如果点数不足7个，用最后一个点补足
        while (pointDates.size() < 7 && pointDates.size() > 0) {
            pointDates.add(pointDates.get(pointDates.size() - 1));
        }

        return pointDates;
    }

    public static List<LocalDate> generateAllDates(LocalDate start, LocalDate end) {
        List<LocalDate> dates = new ArrayList<>();
        LocalDate current = start;

        while (!current.isAfter(end)) {
            dates.add(current);
            current = current.plusDays(1);
        }

        return dates;
    }
}
