package com.lazhu.business.tasks.enums;

import lombok.Getter;

/**
 * 任务类型枚举
 */
@Getter
public enum TaskTypeEnum {
    
    /**
     * 文本创作任务
     */
    TEXT_CREATION(1, "文本创作"),
    
    /**
     * 文本修改任务
     */
    TEXT_MODIFICATION(2, "文本修改"),
    
    /**
     * 视频对口型查询任务
     */
    VIDEO_TALK_QUERY(3, "视频对口型查询"),
    
    /**
     * 视频对口型提交任务
     */
    VIDEO_TALK_SUBMIT(301, "视频对口型提交"),
    
    /**
     * 视频剪辑任务
     */
    VIDEO_MONTAGE(4, "视频剪辑");
    
    private final int code;
    private final String description;
    
    TaskTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }
    
    /**
     * 根据code获取枚举
     * @param code 任务类型code
     * @return 对应的枚举值，未找到返回null
     */
    public static TaskTypeEnum fromCode(int code) {
        for (TaskTypeEnum type : TaskTypeEnum.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * 判断code是否匹配当前枚举
     * @param code 任务类型code
     * @return 是否匹配
     */
    public boolean is(int code) {
        return this.code == code;
    }
}