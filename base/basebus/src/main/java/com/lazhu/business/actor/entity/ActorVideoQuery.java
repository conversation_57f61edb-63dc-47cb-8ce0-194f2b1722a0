package com.lazhu.business.actor.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.lazhu.support.base.BaseQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * ip人物视频
 * <AUTHOR>
 * @time 2025/8/15 15:17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ActorVideoQuery extends BaseQuery {

    //title 描述
    private String title;
    //media_url 链接
    private String mediaUrl;
    //duration_ms 时长 (秒)
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long duration;
    //actor_id 演员id
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long actorId;
    // user_id 用户id
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long userId;


    private List<Long> actorIds;

}
