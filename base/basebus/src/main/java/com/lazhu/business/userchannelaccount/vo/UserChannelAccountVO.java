package com.lazhu.business.userchannelaccount.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 社媒账号vo
 *
 * <AUTHOR>
 * @time 2025/8/26 15:53
 */
@Data
public class UserChannelAccountVO implements Serializable {

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    /**
     * 角色id
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long actorId;

    /**
     * 角色名称
     */
    private String actorName;

    /**
     * 账号
     */
    private String account;

    /**
     * 所属平台
     */
    private String channel;

    /**
     * 已发布数量
     */
    private Integer publishedCount;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 登录状态
     */
    private Integer loginStatus;

    /**
     * 头像
     */
    private String avatar;

}
