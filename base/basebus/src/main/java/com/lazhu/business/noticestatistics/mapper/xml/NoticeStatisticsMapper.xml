<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lazhu.business.noticestatistics.mapper.NoticeStatisticsMapper">

	<select id="selectIdPage" resultType="com.lazhu.business.noticestatistics.entity.NoticeStatistics">
		select * from u_notice_statistics
		<where>
			<if test="params.userId != null">
				and user_id = #{params.userId}
			</if>
			<if test="params.actorId != null">
				and actor_id = #{params.actorId}
			</if>
			<if test="params.addNum != null">
				and add_num = #{params.addNum}
			</if>
			<if test="params.cancelNum != null">
				and cancel_num = #{params.cancelNum}
			</if>
			<if test="params.netNum != null">
				and net_num = #{params.netNum}
			</if>
			<if test="params.totalNum != null">
				and total_num = #{params.totalNum}
			</if>
		</where>
	</select>
</mapper>
