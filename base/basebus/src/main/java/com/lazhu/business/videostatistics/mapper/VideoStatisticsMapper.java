package com.lazhu.business.videostatistics.mapper;

import com.lazhu.business.videostatistics.dto.VideoDailyStat;
import com.lazhu.business.videostatistics.entity.VideoStatistics;
import com.lazhu.business.videostatistics.vo.VideoStatsVO;
import com.lazhu.support.base.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 视频数据统计 Mapper 接口
 * <AUTHOR>
 * @since 2025-08-26
 */
@Repository
public interface VideoStatisticsMapper extends BaseMapper<VideoStatistics> {


    @Select("SELECT COALESCE(SUM(play_num), 0) as play_num, " +
            "COALESCE(SUM(zan_num), 0) as zan_num, " +
            "COALESCE(SUM(collect_num), 0) as collect_num, " +
            "COALESCE(SUM(comment_num), 0) as comment_num, " +
            "COALESCE(SUM(share_num), 0) as share_num, " +
            "COALESCE(SUM(notice_num), 0) as notice_num " +
            "FROM c_video_statistics " +
            "WHERE user_id = #{userId} " +
            "AND statistics_date BETWEEN #{startDate} AND #{endDate}")
     VideoStatsVO selectStatsByUserIdAndDateRange(@Param("userId") Long userId,
                                                 @Param("startDate") Date startDate,
                                                 @Param("endDate") Date endDate);


    @Select("SELECT DATE(statistics_date) as stat_date, " +
            "SUM(play_num) as play_num, " +
            "SUM(zan_num) as zan_num, " +
            "SUM(collect_num) as collect_num, " +
            "SUM(comment_num) as comment_num, " +
            "SUM(share_num) as share_num, " +
            "SUM(notice_num) as notice_num " +
            "FROM c_video_statistics " +
            "WHERE user_id = #{userId} " +
            "AND statistics_date BETWEEN #{startDate} AND #{endDate} " +
            "GROUP BY DATE(statistics_date) " +
            "ORDER BY stat_date desc")
     List<VideoDailyStat> selectDailyStatsByUserIdAndDateRange(@Param("userId") Long userId,
                                                               @Param("startDate") Date startDate,
                                                               @Param("endDate") Date endDate);

}
