package com.lazhu.business.videoeffectstpl.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.lazhu.support.base.BaseQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * <p>
 * 剪辑模板表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class VideoEffectsTplQuery extends BaseQuery {
    //tpl_name 模板名称 
    private String tplName;
    //video_url 视频链接 
    private String videoUrl;
    //srt 字幕 
    private String srt;
    //process_config 处理流程配置(json) 
    private String processConfig;
    //cover_img 封面图片
    private String coverImg;
    //排序字段
    private Integer orderNum;

}
