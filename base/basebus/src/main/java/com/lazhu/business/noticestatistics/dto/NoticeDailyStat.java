package com.lazhu.business.noticestatistics.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class NoticeDailyStat implements Serializable {
        private Date statDate;
        //关注总数
        private Integer totalNum;
        // 新增关注
        private Integer addNum;
        // 取消关注
        private Integer cancelNum;
        // 净增关注
        private Integer netNum;
        private String date;
}