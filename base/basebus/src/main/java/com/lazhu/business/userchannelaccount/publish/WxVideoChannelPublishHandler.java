package com.lazhu.business.userchannelaccount.publish;


import com.lazhu.business.userchannelaccount.login.WxVideoChannelLoginHandler;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.options.LoadState;

import java.nio.file.Paths;

/**
 * 微信视频号内容发布器
 */
public class WxVideoChannelPublishHandler {
    
    private WxVideoChannelLoginHandler loginService;
    
    public WxVideoChannelPublishHandler() {
        this.loginService = new WxVideoChannelLoginHandler();
    }
    
//    @Override
//    public PublishSession login(String account) {
//        loginService.login(account);
//        return new WechatChannelPublishSession(loginService.getPage());
//    }
    
    /**
     * 微信视频号发布会话
     */
    public static class WechatChannelPublishSession{
        
        private Page page;
        
        public WechatChannelPublishSession(Page page) {
            this.page = page;
        }
        
        /**
         * 发布视频
         * @param videoPath 视频文件路径
         * @param title 视频标题
         * @param description 视频描述
         * @param location 地理位置（可选）
         * @return 是否发布成功
         */
        public boolean publishVideo(String videoPath, String title, String description, String location) {
            try {
                // 确保在发布页面
                if (!page.url().contains("platform/post/create")) {
                    page.navigate("https://channels.weixin.qq.com/platform/post/create");
                    page.waitForLoadState(LoadState.DOMCONTENTLOADED);
                }
                
                // 上传视频
                uploadVideo(videoPath);
                
                // 等待视频上传完成
                waitForVideoUpload();
                
                // 填写标题
                fillTitle(title);
                
                // 填写描述
                fillDescription(description);
                
                // 设置地理位置（如果提供）
                if (location != null && !location.isEmpty()) {
                    setLocation(location);
                }
                
                // 发布视频
                publishVideo();
                
                return true;
                
            } catch (Exception e) {
                System.err.println("发布视频失败: " + e.getMessage());
                e.printStackTrace();
                return false;
            }
        }
        
        /**
         * 上传视频文件
         */
        private void uploadVideo(String videoPath) {
            try {
                // 查找上传按钮或拖拽区域
                Locator uploadArea = page.locator("input[type='file']").first();
                
                if (!uploadArea.isVisible()) {
                    // 如果直接的文件输入不可见，尝试点击上传按钮
                    Locator uploadButton = page.locator("text=上传视频").or(page.locator("[class*='upload']")).first();
                    uploadButton.click();
                    
                    // 重新查找文件输入
                    uploadArea = page.locator("input[type='file']").first();
                }
                
                // 上传文件
                uploadArea.setInputFiles(Paths.get(videoPath));
                System.out.println("视频文件上传中...");
                
            } catch (Exception e) {
                throw new RuntimeException("上传视频失败", e);
            }
        }
        
        /**
         * 等待视频上传完成
         */
        private void waitForVideoUpload() {
            try {
                // 等待上传进度条消失或上传完成的标识出现
                page.waitForFunction("() => {" +
                        "const progressBar = document.querySelector('[class*=\"progress\"]');" +
                        "const uploadSuccess = document.querySelector('[class*=\"upload-success\"]');" +
                        "const videoPreview = document.querySelector('video');" +
                        "return !progressBar || uploadSuccess || videoPreview;" +
                        "}", new Page.WaitForFunctionOptions().setTimeout(300000)); // 5分钟超时
                
                System.out.println("视频上传完成");
                
            } catch (Exception e) {
                throw new RuntimeException("等待视频上传完成超时", e);
            }
        }
        
        /**
         * 填写标题
         */
        private void fillTitle(String title) {
            try {
                // 查找标题输入框
                Locator titleInput = page.locator("textarea[placeholder*='标题']")
                        .or(page.locator("input[placeholder*='标题']"))
                        .or(page.locator("[class*='title'] textarea"))
                        .or(page.locator("[class*='title'] input"));
                
                titleInput.waitFor(new Locator.WaitForOptions().setTimeout(10000));
                titleInput.fill(title);
                
                System.out.println("标题已填写: " + title);
                
            } catch (Exception e) {
                throw new RuntimeException("填写标题失败", e);
            }
        }
        
        /**
         * 填写描述
         */
        private void fillDescription(String description) {
            try {
                // 查找描述输入框
                Locator descriptionInput = page.locator("textarea[placeholder*='描述']")
                        .or(page.locator("textarea[placeholder*='简介']"))
                        .or(page.locator("[class*='description'] textarea"))
                        .or(page.locator("[class*='desc'] textarea"));
                
                descriptionInput.waitFor(new Locator.WaitForOptions().setTimeout(10000));
                descriptionInput.fill(description);
                
                System.out.println("描述已填写: " + description);
                
            } catch (Exception e) {
                System.err.println("填写描述失败: " + e.getMessage());
                // 描述可能是可选的，不抛出异常
            }
        }
        
        /**
         * 设置地理位置
         */
        private void setLocation(String location) {
            try {
                // 查找位置设置按钮
                Locator locationButton = page.locator("text=添加位置")
                        .or(page.locator("text=地理位置"))
                        .or(page.locator("[class*='location']"));
                
                if (locationButton.isVisible()) {
                    locationButton.click();
                    
                    // 在位置搜索框中输入位置
                    Locator locationInput = page.locator("input[placeholder*='搜索']")
                            .or(page.locator("input[placeholder*='位置']"));
                    
                    locationInput.fill(location);
                    
                    // 选择第一个搜索结果
                    page.waitForSelector("[class*='search-result']", new Page.WaitForSelectorOptions().setTimeout(5000));
                    page.locator("[class*='search-result']").first().click();
                    
                    System.out.println("地理位置已设置: " + location);
                }
                
            } catch (Exception e) {
                System.err.println("设置地理位置失败: " + e.getMessage());
                // 位置设置可能是可选的，不抛出异常
            }
        }
        
        /**
         * 发布视频
         */
        private void publishVideo() {
            try {
                // 查找发布按钮
                Locator publishButton = page.locator("text=发表")
                        .or(page.locator("text=发布"))
                        .or(page.locator("button[class*='publish']"))
                        .or(page.locator("button[class*='submit']"));
                
                publishButton.waitFor(new Locator.WaitForOptions().setTimeout(10000));
                publishButton.click();
                
                // 等待发布完成
                page.waitForSelector("text=发布成功", new Page.WaitForSelectorOptions().setTimeout(30000));
                
                System.out.println("视频发布成功！");
                
            } catch (Exception e) {
                throw new RuntimeException("发布视频失败", e);
            }
        }
        
        /**
         * 关闭会话
         */
        public void close() {
            if (page != null && page.isClosed() == false) {
                page.close();
            }
        }
    }

}