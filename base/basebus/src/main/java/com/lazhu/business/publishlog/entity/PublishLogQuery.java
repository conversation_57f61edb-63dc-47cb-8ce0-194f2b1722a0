package com.lazhu.business.publishlog.entity;

import java.util.Date;

import com.lazhu.support.base.BaseQuery;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * 发布记录表
 * <AUTHOR>
 * @since 2025-08-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PublishLogQuery extends BaseQuery {
    //content_id 内容id 
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long contentId;
    //title 发布标题 
    private String title;
    //description 视频描述 
    private String description;
    //publish_type 发布方式;1 实时发布 2定时发布 
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer publishType;
    //publish_time 发布时间 
    private Date publishTime;
    //publish_result 发布状态;0 待发布 1发布中 2发布成功  3发布失败 
    private String publishStatus;
    //publish_id 发布id;第三方平台发布记录id 
    private String publishId;

    // 所属平台
    private Integer channel;
    // 发布账号
    private String channelAccount;
    // 媒体类型 （1、图文 2、视频）
    private String contentType;
    // 用户id
    private Long userId;
    // 用户id
    private Long actorId;

    // 发布时间开始
    private Date publishEndTime;
}
