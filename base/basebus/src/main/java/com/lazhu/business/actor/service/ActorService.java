package com.lazhu.business.actor.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.lazhu.baseai.llm.dto.VideoCreateResp;
import com.lazhu.business.actor.mapper.ActorMapper;
import com.lazhu.business.llmconfig.service.LLMService;
import com.lazhu.business.llmvoicejoin.entity.LlmVoiceJoin;
import com.lazhu.business.llmvoicejoin.service.LlmVoiceJoinService;
import com.lazhu.common.media.MediaUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lazhu.support.base.BaseService;
import com.lazhu.business.actor.entity.Actor;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 演员表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Service
public class ActorService extends BaseService<Actor> {

    @Autowired
    private ActorMapper actorMapper;

    @Autowired
    private LLMService llmservice;

    @Autowired
    private LlmVoiceJoinService llmVoiceJoinService;

    public List<Actor> selectByIds(List<Long> ids){
        if(CollUtil.isEmpty(ids)){
            return Collections.emptyList();
        }
        return actorMapper.selectByIds(ids);
    }


    public void saveVoice(Actor param){
        Actor actor = this.queryById(param.getId());
        param.setDuration(MediaUtil.getDuration(param.getVoiceUrl()));
        //上传音色
        String firstLetter = PinyinUtil.getFirstLetter(actor.getNickName()+"的声音", "");
        firstLetter=firstLetter.replaceAll("[^a-zA-Z0-9]", "");
        if(firstLetter.length() > 10){
            firstLetter = firstLetter.substring(0,10);
        }
        VideoCreateResp voice = llmservice.createVoice(firstLetter, param.getVoiceUrl());
        //保存关联记录
        LlmVoiceJoin llmVoiceJoin = new LlmVoiceJoin();
        llmVoiceJoin.setAssetsType(2);
        llmVoiceJoin.setActorId(param.getId());
        llmVoiceJoin.setVoiceId(voice.getVoiceId());
        llmVoiceJoin.setLlmId(voice.getLlmId());
        llmVoiceJoinService.save(llmVoiceJoin);

        this.update(param);
    }
}
