package com.lazhu.business.noticestatistics.vo;

import com.lazhu.business.videostatistics.vo.VideoStatsVO;
import lombok.Data;

import java.io.Serializable;

@Data
public class NoticeStatsVo  implements Serializable {

    //关注总数
    private Integer totalNum;

    // 新增关注
    private Integer addNum;
    // 取消关注
    private Integer cancelNum;
    // 净增关注
    private Integer netNum;


    // 环比数据
    private NoticeStatsVo previousPeriodStats;

    private String addNumGrowthRate;
    private String cancelNumGrowthRate;
    private String netNumGrowthRate;


}
