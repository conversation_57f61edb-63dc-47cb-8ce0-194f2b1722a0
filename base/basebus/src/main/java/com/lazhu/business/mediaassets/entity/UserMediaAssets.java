package com.lazhu.business.mediaassets.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.lazhu.support.base.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * <p>
 * 用户上传素材表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@TableName("c_user_media_assets")
@Data
@EqualsAndHashCode(callSuper = true)
public class UserMediaAssets extends BaseModel {
    private static final long serialVersionUID = 1L;
    //assert_type 素材类型 
    @TableField("assets_type")
    private String assetsType;
    //title 描述 
    @TableField("title")
    private String title;
    //media_url 链接 
    @TableField("media_url")
    private String mediaUrl;
    //duration_ms 时长 (秒)
    @TableField("duration")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long duration;
    // 视频封面
    @TableField("cover_img")
    private String coverImg;
    //actor_id 演员id 
    @TableField("actor_id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long actorId;
    //创建人
    @TableField("create_by")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long createBy;
    // 是否保留声音（1、是 0、否）
    @TableField("has_audio")
    private String hasAudio;
    // 视频文件大小
    @TableField("storage")
    private String storage;
    // 尺寸（宽 X 高）
    @TableField("size")
    private String size;
    
}
