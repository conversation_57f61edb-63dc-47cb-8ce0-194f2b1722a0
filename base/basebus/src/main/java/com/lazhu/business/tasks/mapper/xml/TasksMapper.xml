<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lazhu.business.tasks.mapper.TasksMapper">

    <select id="selectIdPage" resultType="com.lazhu.business.tasks.entity.Tasks">
		select * from t_tasks
		<where>
			<if test="params.contentId != null">
				and content_id = #{params.contentId}
			</if>
			<if test="params.type != null">
				and type = #{params.type}
			</if>
			<if test="params.taskId != null and params.taskId != ''">
			    and task_id like concat(concat('%',#{params.taskId}),'%')
			</if>
			<if test="params.status != null">
				and status = #{params.status}
			</if>
			<if test="params.result != null and params.result != ''">
			    and result like concat(concat('%',#{params.result}),'%')
			</if>
			<if test="params.excludeId != null">
				and id != #{params.excludeId}
			</if>
			<if test="params.statusList != null">
				and status in
				<foreach collection="params.statusList" item="status" open="(" separator="," close=")">
					#{status}
				</foreach>
			</if>
		</where>
	</select>

	<delete id="deleteByContentId">
		delete from t_tasks where content_id = #{contentId}
	</delete>

	<delete id="deleteByContentIdAndType">
		delete from t_tasks where content_id = #{contentId} and type =#{type}
	</delete>
</mapper>
