package com.lazhu.business.actor.service;


import cn.hutool.core.io.FileUtil;
import com.lazhu.business.actor.entity.ActorVideo;
import com.lazhu.business.mediaassets.entity.UserMediaAssets;
import com.lazhu.common.media.MediaUtil;
import com.lazhu.common.utils.OssUtil;
import com.lazhu.support.base.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;


/**
 * <p>
 * ip角色视频 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Service
public class ActorVideoService extends BaseService<ActorVideo> {

    @Autowired
    private OssUtil ossUtil;

    public ActorVideo saveActorVideo(ActorVideo param) {
        param.setDuration(MediaUtil.getDuration(param.getMediaUrl()));
        File videoCover = MediaUtil.getVideoCover(param.getMediaUrl());
        String url = ossUtil.upload(videoCover);
        //删除临时文件
        FileUtil.del(videoCover);
        param.setCoverImg(url);
        this.save(param);
        return param;
    }
}
