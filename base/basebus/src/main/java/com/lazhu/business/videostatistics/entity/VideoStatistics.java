package com.lazhu.business.videostatistics.entity;

import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.lazhu.support.base.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * 视频数据统计
 * <AUTHOR>
 * @since 2025-08-26
 */
@TableName("c_video_statistics")
@Data
@EqualsAndHashCode(callSuper = true)
public class VideoStatistics extends BaseModel {
    private static final long serialVersionUID = 1L;
    //user_id 用户id 
    @TableField("user_id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long userId;
    //actor_id 角色id 
    @TableField("actor_id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long actorId;
    //content_id 内容id 
    @TableField("content_id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long contentId;
    //publish_id 发布id 
    @TableField("publish_id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long publishId;
    //play_num 播放量 
    @TableField("play_num")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer playNum;
    //zan_num 点赞量 
    @TableField("zan_num")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer zanNum;
    //collect_num 收藏量 
    @TableField("collect_num")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer collectNum;
    //comment_num 评论 
    @TableField("comment_num")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer commentNum;
    //share_num 分享 
    @TableField("share_num")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer shareNum;
    //notice_num 关注 
    @TableField("notice_num")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer noticeNum;
    //statistics_date 统计日期 
    @TableField("statistics_date")
    private Date statisticsDate;
    
}
