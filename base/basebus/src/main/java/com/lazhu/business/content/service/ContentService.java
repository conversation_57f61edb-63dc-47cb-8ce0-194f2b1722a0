package com.lazhu.business.content.service;

import cn.hutool.extra.template.Template;
import cn.hutool.extra.template.TemplateConfig;
import cn.hutool.extra.template.TemplateEngine;
import cn.hutool.extra.template.TemplateUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONArray;
import cn.hutool.core.bean.BeanUtil;
import com.lazhu.business.content.dto.ContentDTO;
import com.lazhu.baseai.llm.dto.TextCreationReq;
import cn.hutool.core.util.StrUtil;
import com.lazhu.baseai.tool.dify.HotTopicSearchTool;
import com.lazhu.baseai.tool.dify.LinkReaderTool;
import com.lazhu.business.actor.entity.Actor;
import com.lazhu.business.actor.service.ActorService;
import com.lazhu.business.actorconfig.entity.ActorConfig;
import com.lazhu.business.actorconfig.service.ActorConfigService;
import com.lazhu.business.actorcontentexample.entity.ActorContentExample;
import com.lazhu.business.actorcontentexample.service.ActorContentExampleService;
import com.lazhu.business.content.dto.ContentCreationReq;
import com.lazhu.common.enums.ContentTypeEnum;
import com.lazhu.system.dic.entity.Dic;
import com.lazhu.system.dic.service.DicService;
import com.lazhu.system.sysparams.service.SysParamsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lazhu.support.base.BaseService;
import com.lazhu.business.content.entity.Content;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 内容管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Service
public class ContentService extends BaseService<Content> {


    @Autowired
    private SysParamsService sysParamsService;

    @Autowired
    private ActorService actorService;

    @Autowired
    private ActorConfigService actorConfigService;

    @Autowired
    private DicService dicService;

    @Autowired
    private ActorContentExampleService actorContentExampleService;

    @Autowired
    private HotTopicSearchTool hotTopicSearchTool;

    @Autowired
    private LinkReaderTool linkReaderTool;


    /**
     * 将ContentCreationReq.TextRequest转换为TextCreationReq对象
     *
     * @param request ContentCreationReq.TextRequest对象
     * @return TextCreationReq对象
     */
    public TextCreationReq fromTextCreateRequest(ContentCreationReq.TextRequest request) {
        TextCreationReq textCreationReq = new TextCreationReq();

        String prompt = combinePrompt(request);
        textCreationReq.setPrompt(prompt);
        // 设置内容类型
        textCreationReq.setContentType(request.getContentType());
        textCreationReq.setPlat(request.getMetaData().getPlat());
        // 编辑索引默认为null（第一次生成为空）
        textCreationReq.setEditIndex(null);
        // 创建文本时默认为"开始生成"
        textCreationReq.setQuery("开始生成");
        textCreationReq.setIdea(request.getTopic());
        if("1".equals(request.getMetaData().getReferenceType())){
            textCreationReq.setHotTopics(processHashtagsAndMentions(request.getMetaData().getReferenceContent()));
        }
        //fill example content
        String plat = request.getMetaData().getPlat();
        List<Dic> platCode = dicService.queryByCodes(Collections.singletonList("PLAT_CODE"));
        Dic platDic = platCode.stream().filter(dic -> StrUtil.contains(plat, dic.getTxt())).findFirst().orElse(null);
        ActorContentExample example = actorContentExampleService.queryByActorIdAndPlat(request.getActorId(), platDic.getValue());
        if (example != null) {
            textCreationReq.setExample(example.getContent());
        }
        textCreationReq.setWordCount(request.getMetaData().getContentLength());
        return textCreationReq;
    }

    public TextCreationReq TextFineTuneRequest(ContentCreationReq.TextFineTuneRequest request) {
        TextCreationReq textCreationReq = fromTextCreateRequest(request.textRequest());
        textCreationReq.setQuery(request.query());
        textCreationReq.setEditIndex(request.editIndex());
        textCreationReq.setTitle(request.title());
        textCreationReq.setContent(request.content());
        return textCreationReq;
    }

    public String combinePrompt(ContentCreationReq.TextRequest request) {
        // 查询提示词模板
        String promptTpl;
        if (request.getContentType().equals(ContentTypeEnum.TXT.getType())) {
            // 图文
            promptTpl = sysParamsService.queryByKey("txt_create_prompt");
        } else {
            // 视频
            promptTpl = sysParamsService.queryByKey("video_txt_create_prompt");
        }
        Map<String, Object> params = new HashMap<>();
        params.put("plat", request.getMetaData().getPlat());
        Actor actor = actorService.queryById(request.getActorId());
        params.put("author_prompt", actor.getRolePrompt());
        params.put("theme", request.getTopic());
        params.put("user_role", actor.getNickName());
        params.put("word_count_max", request.getMetaData().getContentLength());
        params.put("other_requirements", request.getMetaData().getOtherDesc());
        if("1".equals(request.getMetaData().getReferenceType())){
            params.put("idea", processHashtagsAndMentions(request.getMetaData().getReferenceContent()));
        }
        //设置创作要求
        List<ActorConfig> list = actorConfigService.queryByIds(request.getStyleIds());
        //根据类型分组
        Map<String, List<ActorConfig>> styleGroup = list.stream().collect(Collectors.groupingBy(ActorConfig::getCharacterType));
        //查询字典：
        List<Dic> chacterType = dicService.queryByCodes(Collections.singletonList("CHACTER_TYPE"));
        //转map
        Map<String, Dic> chacterMap = chacterType.stream().collect(Collectors.toMap(Dic::getValue, dic -> dic));
        StringBuilder sb = new StringBuilder();
        int idx = 1;
        for (String key : styleGroup.keySet()) {
            String keyName = chacterMap.get(key).getTxt();
            StringBuilder sb1 = new StringBuilder();
            for (ActorConfig config : styleGroup.get(key)) {
                sb1.append(" - ").append(config.getCharacterName()).append(" : ").append(config.getCharacterPrompt()).append(" \n ");
            }
            sb.append(idx).append(". ").append("**").append(keyName).append("** : \n ").append(sb1).append(" \n ");
            idx++;
        }
        params.put("styles", sb.toString());
        //fill example content
        String plat = request.getMetaData().getPlat();
        List<Dic> platCode = dicService.queryByCodes(Collections.singletonList("PLAT_CODE"));
        Dic platDic = platCode.stream().filter(dic -> StrUtil.contains(plat, dic.getTxt())).findFirst().orElse(null);
        ActorContentExample example = actorContentExampleService.queryByActorIdAndPlat(request.getActorId(), platDic.getValue());
        if (example != null) {
            params.put("example_title", example.getTitle());
            params.put("example_content", example.getContent());
        }
        //设置热点
        String referenceType = request.getMetaData().getReferenceType();
        String articleContent = "";
        if ("1".equals(referenceType)) {
            //话题
            String referenceContent = request.getMetaData().getReferenceContent();
            List<String> kws = StrUtil.split(referenceContent, ",");
            List<String> search = hotTopicSearchTool.search(kws);
            articleContent = JSONObject.toJSONString(search);
        } else if ("2".equals(referenceType)) {
            //链接
            String referenceContent = request.getMetaData().getReferenceContent();
            articleContent = linkReaderTool.readContent(referenceContent);
        } else if ("3".equals(referenceType)) {
            //文案
            articleContent = request.getMetaData().getReferenceContent();
        }
        params.put("article_content", articleContent);
        TemplateEngine engine = TemplateUtil.createEngine(new TemplateConfig());
        Template template = engine.getTemplate(promptTpl);
        return template.render(params);
    }

    /**
     * 处理字符串中的 # 和 @ 符号
     *
     * @param text 原始文本
     * @return 处理后的文本
     */
    public String processHashtagsAndMentions(String text) {
        if (StrUtil.isBlank(text)) {
            return text;
        }

        String result = text;

        // 处理 # 号：移除 #号后面到空格的部分
        // 使用正则表达式匹配 # 开头到空格或字符串结尾的内容
        result = result.replaceAll("#[^\\s]*", "");

        // 处理 @ 符号：移除 @ 符号后面的所有内容
        int atIndex = result.indexOf('@');
        if (atIndex != -1) {
            result = result.substring(0, atIndex);
        }

        // 清理多余的空格
        result = result.replaceAll("\\s+", " ").trim();

        return result;
    }
}
