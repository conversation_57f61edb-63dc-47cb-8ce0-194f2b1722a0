package com.lazhu.business.publishlog.service;


import com.lazhu.business.publishlog.entity.PublishLog;
import com.lazhu.business.userchannelaccount.enums.ChannelType;
import com.lazhu.gxc.publish.VideoPublisher;
import com.lazhu.gxc.publish.model.UploadRequest;
import com.lazhu.gxc.publish.model.UploadResult;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 内容发布服务
 */
@RequiredArgsConstructor
@Service
public class PublishService {

    public final VideoPublisher videoPublisher;

    /**
     * 发布内容
     *
     * @param publishLog 发布记录
     */
    public UploadResult publish(PublishLog publishLog, String videoPath) {
        UploadRequest request = buildUploadRequest(publishLog, videoPath);
        return videoPublisher.uploadVideo(request);
    }

    private UploadRequest buildUploadRequest(PublishLog publishLog, String videoPath) {

        UploadRequest.VideoMetadata videoMetadata = UploadRequest.VideoMetadata.builder()
                .title(publishLog.getTitle())
                .description(publishLog.getDescription())
                .build();

        Integer channel = publishLog.getChannel();
        ChannelType channelType = ChannelType.fromType(channel);

        return UploadRequest.builder()
                .platformType(channelType.toPlatformType())
                .userId(publishLog.getActorId().toString())
                .videoPath(videoPath)
                .videoMetadata(videoMetadata)
                .build();
    }
}
