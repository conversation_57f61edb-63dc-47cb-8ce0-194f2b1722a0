package com.lazhu.business.userchannelaccount.enums;

import com.lazhu.gxc.publish.model.PlatformType;
import lombok.Getter;

/**
 * 平台类型枚举
 */
public enum ChannelType {

    wx_mp(1, "公众号"),

    wx_video(2, "视频号"),

    douyin(3, "抖音"),

    x<PERSON><PERSON><PERSON><PERSON>(4, "小红书");

    @Getter
    private final Integer type;
    @Getter
    private final String desc;

    ChannelType(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * 根据type获取枚举
     *
     * @param type 类型
     */
    public static ChannelType fromType(Integer type) {
        for (ChannelType value : values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        throw new RuntimeException("无效的渠道");
    }

    /**
     * 转成 platformType
     */
    public PlatformType toPlatformType() {
        return switch (this) {
            case wx_video -> PlatformType.WECHAT_VIDEO;
            case douyin -> PlatformType.DOUYIN;
            case xiaohongshu -> PlatformType.XIAOHONGSHU;
            default -> null;
        };
    }


}
