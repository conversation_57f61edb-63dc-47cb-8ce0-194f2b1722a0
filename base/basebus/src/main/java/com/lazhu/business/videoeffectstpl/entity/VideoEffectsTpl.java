package com.lazhu.business.videoeffectstpl.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lazhu.business.videoeffectstpl.dto.ProcessConfigDTO;
import com.lazhu.support.base.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 剪辑模板
 * <AUTHOR>
 * @since 2025-08-07
 */
@TableName("c_video_effects_tpl")
@Data
@EqualsAndHashCode(callSuper = true)
public class VideoEffectsTpl extends BaseModel {
    private static final long serialVersionUID = 1L;
    //tpl_name 模板名称 
    @TableField("tpl_name")
    private String tplName;
    //video_url 视频链接 
    @TableField("video_url")
    private String videoUrl;
    //srt 字幕 
    @TableField("srt")
    private String srt;
    //process_config 处理流程配置(json) 
    @TableField("process_config")
    private String processConfig;
    //cover_img 封面图片
    @TableField("cover_img")
    private String coverImg;
    //排序字段
    @TableField("order_num")
    private Integer orderNum;

    // 尺寸（宽 X 高）
    @TableField("size")
    private String size;
    // 视频文件大小
    @TableField("storage")
    private String storage;

    @TableField(exist = false)
    private List<ProcessConfigDTO> processConfigList;
    
}
