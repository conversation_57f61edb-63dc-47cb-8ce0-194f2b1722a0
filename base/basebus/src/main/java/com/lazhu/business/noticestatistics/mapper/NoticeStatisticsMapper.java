package com.lazhu.business.noticestatistics.mapper;

import com.lazhu.business.noticestatistics.dto.NoticeDailyStat;
import com.lazhu.business.noticestatistics.entity.NoticeStatistics;
import com.lazhu.business.noticestatistics.vo.NoticeStatsVo;
import com.lazhu.business.videostatistics.dto.VideoDailyStat;
import com.lazhu.business.videostatistics.vo.VideoStatsVO;
import com.lazhu.support.base.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 关注者数据统计 Mapper 接口
 * <AUTHOR>
 * @since 2025-08-26
 */
@Repository
public interface NoticeStatisticsMapper extends BaseMapper<NoticeStatistics> {





    @Select("SELECT COALESCE(SUM(add_num), 0) as addNum, " +
            "COALESCE(SUM(cancel_num), 0) as cancel_num, " +
            "COALESCE(SUM(net_num), 0) as net_num " +
            "FROM u_notice_statistics " +
            "WHERE user_id = #{userId} " +
            "AND statistics_date BETWEEN #{startDate} AND #{endDate}")
    NoticeStatsVo selectStatsByUserIdAndDateRange(@Param("userId") Long userId,
                                                  @Param("startDate") Date startDate,
                                                  @Param("endDate") Date endDate);


    @Select("SELECT DATE(statistics_date) as stat_date, " +
            "SUM(add_num) as add_num, " +
            "SUM(cancel_num) as cancel_num, " +
            "SUM(net_num) as net_num, " +
            "total_num as total_num " +
            "FROM u_notice_statistics " +
            "WHERE user_id = #{userId} " +
            "AND statistics_date BETWEEN #{startDate} AND #{endDate} " +
            "GROUP BY DATE(statistics_date) " +
            "ORDER BY stat_date desc")
    List<NoticeDailyStat> selectDailyStatsByUserIdAndDateRange(@Param("userId") Long userId,
                                                               @Param("startDate") Date startDate,
                                                               @Param("endDate") Date endDate);


    @Select("SELECT COALESCE(SUM(notice_num), 0) as today_notice_num " +
            "FROM c_video_statistics " +
            "WHERE user_id = #{userId} " +
            "AND DATE(statistics_date) = DATE(#{today})")
    Integer selectTodayNoticeNumByUserId(@Param("userId") Long userId,
                                         @Param("today") Date today);
}
