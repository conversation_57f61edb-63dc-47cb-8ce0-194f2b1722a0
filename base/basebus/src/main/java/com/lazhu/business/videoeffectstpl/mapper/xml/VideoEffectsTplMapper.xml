<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lazhu.business.videoeffectstpl.mapper.VideoEffectsTplMapper">

	<sql id="base_query">
		<where>
			<if test="params.tplName != null and params.tplName != ''">
				and tpl_name like concat(concat('%',#{params.tplName}),'%')
			</if>
			<if test="params.videoUrl != null and params.videoUrl != ''">
				and video_url like concat(concat('%',#{params.videoUrl}),'%')
			</if>
			<if test="params.srt != null and params.srt != ''">
				and srt like concat(concat('%',#{params.srt}),'%')
			</if>
			<if test="params.processConfig != null and params.processConfig != ''">
				and process_config like concat(concat('%',#{params.processConfig}),'%')
			</if>
		</where>
	</sql>

	<select id="selectIdPage" resultType="com.lazhu.business.videoeffectstpl.entity.VideoEffectsTpl">
		select * from c_video_effects_tpl
		<include refid="base_query"/>
		order by order_num desc,create_time desc
	</select>
</mapper>
