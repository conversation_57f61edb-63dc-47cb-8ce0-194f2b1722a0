package com.lazhu.business.userchannelaccount.entity;


import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lazhu.support.base.BaseModel;
import lombok.Data;

import java.util.Date;

@Data
@TableName("u_user_channel_account")
public class UserChannelAccount extends BaseModel {

    /**
     * 主键ID
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    /**
     * 用户ID
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long userId;

    /**
     * 角色ID
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long actorId;

    /**
     * 渠道类型  {@link com.lazhu.business.userchannelaccount.enums.ChannelType}
     */
    private Integer channel;

    /**
     * 账号名称
     */
    private String account;

    /**
     * 用户名
     */
    private String nickName;

    /**
     * 登录状态 0下线  1在线
     */
    private Integer loginStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 头像
     */
    private String avatar;
}
