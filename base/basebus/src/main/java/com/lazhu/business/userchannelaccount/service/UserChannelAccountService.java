package com.lazhu.business.userchannelaccount.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lazhu.business.actor.service.ActorService;
import com.lazhu.business.userchannelaccount.entity.UserChannelAccount;
import com.lazhu.business.userchannelaccount.entity.UserChannelAccountQuery;
import com.lazhu.business.userchannelaccount.enums.ChannelType;
import com.lazhu.business.userchannelaccount.mapper.UserChannelAccountMapper;
import com.lazhu.business.userchannelaccount.vo.UserChannelAccountVO;
import com.lazhu.gxc.publish.VideoPublisher;
import com.lazhu.gxc.publish.model.LoginResult;
import com.lazhu.gxc.publish.model.LoginSession;
import com.lazhu.support.base.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 用户渠道账号表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-26
 */
@Service
public class UserChannelAccountService extends BaseService<UserChannelAccount> {

    @Autowired
    private UserChannelAccountMapper userChannelAccountMapper;

    @Autowired
    private VideoPublisher videoPublisher;

    @Autowired
    private ActorService actorService;

    /**
     * 开始登录
     *
     * @param actorId 角色ID
     * @param channel 登录平台
     * @return 登录会话
     */
    public LoginSession startLogin(Long actorId, ChannelType channel) {
        return videoPublisher.startLogin(actorId.toString(), channel.toPlatformType());
    }

    /**
     * 检查登录状态 (轮询)
     */
    public LoginResult login(Long actorId, ChannelType channel, String sessionId) {
        LoginResult loginResult = videoPublisher.checkLoginStatus(channel.toPlatformType(), sessionId);
        if (loginResult.isSuccess()) {
            // 保存登录信息
            // 查询用户id
            Long userId = actorService.queryById(actorId).getCreateBy();
            LoginResult.UserInfo userInfo = loginResult.getUserInfo();
            UserChannelAccount userChannelAccount = queryUserChannelAccount(actorId, channel, userInfo.getAccount());
            if (userChannelAccount == null) {
                userChannelAccount = new UserChannelAccount();
                userChannelAccount.setChannel(channel.getType());
                userChannelAccount.setAccount(userInfo.getAccount());
                userChannelAccount.setNickName(userInfo.getNickName());
                userChannelAccount.setAvatar(userInfo.getAvatar());
                userChannelAccount.setUserId(userId);
                userChannelAccount.setActorId(actorId);
                userChannelAccount.setLoginStatus(1);
                userChannelAccount.setCreateTime(new Date());
                userChannelAccountMapper.insert(userChannelAccount);
            } else {
                userChannelAccount.setLoginStatus(1);
                userChannelAccount.setUpdateTime(new Date());
                userChannelAccountMapper.updateById(userChannelAccount);
            }
        }
        return loginResult;
    }


    public UserChannelAccount queryUserChannelAccount(Long actorId, ChannelType channel, String account) {
        List<UserChannelAccount> userChannelAccounts = this.mapper.selectList(new LambdaQueryWrapper<UserChannelAccount>()
                .eq(UserChannelAccount::getActorId, actorId)
                .eq(UserChannelAccount::getChannel, channel.getType())
                .eq(StrUtil.isNotBlank(account), UserChannelAccount::getAccount, account));
        return !userChannelAccounts.isEmpty() ? userChannelAccounts.getFirst() : null;
    }


    /**
     * @return Page<UserChannelAccountVO>
     * <AUTHOR>
     * @param[1] param
     * @time 2025/8/27 9:29
     */
    public Page<UserChannelAccountVO> readPage(UserChannelAccountQuery param) {
        if (param.getPageNo() == null) {
            param.setPageNo(1);
        }
        if (param.getPageSize() == null) {
            param.setPageSize(10);
        }
        Page<UserChannelAccountVO> page = new Page<>(param.getPageNo(), param.getPageSize());
        List<UserChannelAccountVO> list = userChannelAccountMapper.readPage(page, param);
        page.setRecords(list);
        return page;
    }

    /**
     * 查询所有在线的账号
     *
     * @return 账号列表
     */
    public List<UserChannelAccount> queryOnlineAccount() {
        return this.mapper.selectList(new LambdaQueryWrapper<UserChannelAccount>().eq(UserChannelAccount::getLoginStatus, 1));
    }

}
