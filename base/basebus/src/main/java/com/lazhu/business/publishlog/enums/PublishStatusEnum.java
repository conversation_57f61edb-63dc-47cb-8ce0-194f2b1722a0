package com.lazhu.business.publishlog.enums;

import lombok.Getter;

@Getter
public enum PublishStatusEnum {

    PENDING("0", "待发布"),
    PUBLISHING("1", "发布中"),
    SUCCESS("2", "发布成功"),
    FAILED("3", "发布失败");

    private final String code;
    private final String description;

    // 构造函数
    PublishStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    // 根据代码获取对应的枚举值
    public static PublishStatusEnum getByCode(String code) {
        for (PublishStatusEnum status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return null;
    }
}
