package com.lazhu.common.media;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.File;

/**
 * 媒体URL处理器
 * 负责处理网络URL的下载和内网链接替换
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
public class MediaUrlProcessor {

    /**
     * 外网链接前缀
     */
    private static final String EXTERNAL_URL_PREFIX = "https://ins-file.lazhuyun.cn/";

    /**
     * 内网链接替换
     */
    private static final String INTERNAL_URL_REPLACEMENT = "https://lazhuyun.oss-cn-hangzhou-internal.aliyuncs.com/";

    /**
     * 处理媒体URL
     * 如果是网络链接，优先下载到本地
     * 如果是指定的外网链接，替换为内网链接
     *
     * @param url 原始URL
     * @return 处理后的本地文件路径或替换后的URL
     */
    public static String processMediaUrl(String url) {
        String[] activeProfiles = SpringUtil.getActiveProfiles();
        if (activeProfiles == null || ArrayUtil.contains(activeProfiles, "dev")) {
            return url;
        }
        if (StrUtil.isBlank(url)) {
            return url;
        }

        // 如果不是网络URL，直接返回
        if (!MediaPathUtils.isNetworkUrl(url)) {
            return url;
        }

        log.info("处理媒体URL >> 原始URL: {}", url);

        // 替换外网链接为内网链接
        return url.startsWith(EXTERNAL_URL_PREFIX) ? url.replace(EXTERNAL_URL_PREFIX, INTERNAL_URL_REPLACEMENT) : url;
    }

    /**
     * 下载网络文件到本地
     *
     * @param url 网络URL
     * @return 本地文件路径，失败返回null
     */
    public static String downloadToLocal(String url) {
        try {
            // 生成本地文件名
            String fileName = generateLocalFileName(url);
            String extension = MediaPathUtils.getFileExtension(url);

            File localFile = MediaResourceManager.createTempFile("media_download_" + fileName, extension);

            log.info("开始下载网络文件 >> URL: {}, 本地路径: {}", url, localFile.getAbsolutePath());

            // 下载文件
            HttpUtil.downloadFile(url, localFile);

            if (localFile.exists() && localFile.length() > 0) {
                log.info("下载网络文件成功 >> URL: {}, 本地路径: {}, 文件大小: {}KB",
                        url, localFile.getAbsolutePath(), localFile.length() / 1024);
                return localFile.getAbsolutePath();
            } else {
                log.error("下载网络文件失败，文件不存在或大小为0 >> URL: {}", url);
                MediaResourceManager.cleanupTempFile(localFile);
                return null;
            }

        } catch (Exception e) {
            log.error("下载网络文件异常 >> URL: {}", url, e);
            return null;
        }
    }

    /**
     * 生成本地文件名
     *
     * @param url 网络URL
     * @return 安全的文件名
     */
    private static String generateLocalFileName(String url) {
        try {
            // 尝试从URL中提取文件名
            String fileName = MediaPathUtils.getFileNameWithoutExtension(url);
            if (StrUtil.isNotBlank(fileName)) {
                // 移除非法字符
                fileName = fileName.replaceAll("[^a-zA-Z0-9._-]", "_");
                if (fileName.length() > 50) {
                    fileName = fileName.substring(0, 50);
                }
                return fileName;
            }
        } catch (Exception e) {
            log.debug("从URL提取文件名失败: {}", url, e);
        }

        // 使用URL的hash值作为文件名
        return String.valueOf(Math.abs(url.hashCode()));
    }
}