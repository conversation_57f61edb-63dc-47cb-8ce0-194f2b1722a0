package com.lazhu.common.media;

import lombok.Getter;

import java.util.Arrays;

/**
 * 覆盖模式枚举
 * 定义视频或图片在合成时的覆盖方式
 */
public enum OverlayMode {
    
    /**
     * 透明背景（显示出原视频帧）
     */
    CLEAR("0x00000000@0", "clear"),
    
    /**
     * 黑色背景（完全遮盖原视频帧）
     */
    BLACK("black", "black");


    @Getter
    private final String color;

    @Getter
    private final String name;

    OverlayMode(String color, String name) {
        this.color = color;
        this.name = name;
    }

    /**
     * 根据name获取OverlayMode枚举
     */
    public static OverlayMode getByName(String name) {
        return Arrays.stream(OverlayMode.values())
                .filter(mode -> mode.name.equals(name))
                .findFirst()
                .orElse(null);
    }






}