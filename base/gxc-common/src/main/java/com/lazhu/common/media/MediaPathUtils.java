package com.lazhu.common.media;

import cn.hutool.core.util.StrUtil;

/**
 * 媒体路径处理工具类
 * 负责文件路径的处理和转换，特别是FFmpeg路径格式
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
public class MediaPathUtils {

    /**
     * 处理文件路径以适配FFmpeg的要求
     * 主要处理Windows路径的转义和特殊字符
     *
     * @param path 原始路径
     * @return 处理后的路径
     */
    public static String processPathForFFmpeg(String path) {
        if (StrUtil.isBlank(path)) {
            return path;
        }

        // 统一使用正斜杠
        String processedPath = path.replace("\\", "/");

        // Windows路径处理 - 对于ASS滤镜，需要特殊的转义格式
        if (isWindows() && processedPath.matches("^[A-Za-z]:.*")) {
            // 将 C:/path 转换为 C\\:/path 格式（双反斜杠转义）
            processedPath = processedPath.charAt(0) + "\\\\:" + processedPath.substring(2);
        }

        // 转义特殊字符
        processedPath = processedPath
                .replace(":", "\\:")  // 转义冒号
                .replace("'", "\\'")  // 转义单引号
                .replace("\"", "\\\"") // 转义双引号
                .replace(" ", "\\ ");  // 转义空格

        return processedPath;
    }

    /**
     * 处理输出文件路径
     * 确保输出路径格式正确
     */
    public static String processOutputPath(String path) {
        if (StrUtil.isBlank(path)) {
            return path;
        }

        // 标准化路径分隔符
        String processedPath = normalizePath(path);

        // 确保目录存在
        java.io.File file = new java.io.File(processedPath);
        java.io.File parentDir = file.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            parentDir.mkdirs();
        }

        return processedPath;
    }

    /**
     * 获取系统默认字体目录
     *
     * @return 字体目录路径
     */
    public static String getDefaultFontsDir() {
        String osName = System.getProperty("os.name").toLowerCase();
        
        if (osName.contains("win")) {
            // Windows系统
            return "C:/Windows/Fonts";
        } else if (osName.contains("mac")) {
            // macOS系统
            return "/System/Library/Fonts";
        } else {
            // Linux系统
            return "/usr/share/fonts";
        }
    }

    /**
     * 判断是否为Windows系统
     */
    public static boolean isWindows() {
        return System.getProperty("os.name").toLowerCase().contains("win");
    }

    /**
     * 判断是否为macOS系统
     */
    public static boolean isMacOS() {
        return System.getProperty("os.name").toLowerCase().contains("mac");
    }

    /**
     * 判断是否为Linux系统
     */
    public static boolean isLinux() {
        String osName = System.getProperty("os.name").toLowerCase();
        return osName.contains("linux") || osName.contains("unix");
    }

    /**
     * 获取操作系统类型
     */
    public static OSType getOSType() {
        if (isWindows()) {
            return OSType.WINDOWS;
        } else if (isMacOS()) {
            return OSType.MACOS;
        } else if (isLinux()) {
            return OSType.LINUX;
        } else {
            return OSType.UNKNOWN;
        }
    }

    /**
     * 操作系统类型枚举
     */
    public enum OSType {
        WINDOWS("Windows"),
        MACOS("macOS"),
        LINUX("Linux"),
        UNKNOWN("Unknown");

        private final String displayName;

        OSType(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    /**
     * 标准化路径分隔符
     * 将所有路径分隔符统一为正斜杠
     */
    public static String normalizePath(String path) {
        if (StrUtil.isBlank(path)) {
            return path;
        }
        return path.replace("\\", "/");
    }

    /**
     * 获取适合当前操作系统的路径分隔符
     */
    public static String getPathSeparator() {
        return System.getProperty("file.separator");
    }

    /**
     * 将路径转换为当前操作系统格式
     */
    public static String toSystemPath(String path) {
        if (StrUtil.isBlank(path)) {
            return path;
        }
        
        if (isWindows()) {
            return path.replace("/", "\\");
        } else {
            return path.replace("\\", "/");
        }
    }

    /**
     * 检查路径是否为绝对路径
     */
    public static boolean isAbsolutePath(String path) {
        if (StrUtil.isBlank(path)) {
            return false;
        }
        
        if (isWindows()) {
            // Windows: C:\ 或 \\server\share
            return path.matches("^[A-Za-z]:[/\\\\].*") || path.startsWith("\\\\");
        } else {
            // Unix-like: /path
            return path.startsWith("/");
        }
    }

    /**
     * 检查路径是否为网络URL
     */
    public static boolean isNetworkUrl(String path) {
        if (StrUtil.isBlank(path)) {
            return false;
        }
        
        String lowerPath = path.toLowerCase();
        return lowerPath.startsWith("http://") || 
               lowerPath.startsWith("https://") ||
               lowerPath.startsWith("ftp://") ||
               lowerPath.startsWith("rtmp://") ||
               lowerPath.startsWith("rtsp://");
    }

    /**
     * 获取文件扩展名（包含点号）
     */
    public static String getFileExtension(String path) {
        if (StrUtil.isBlank(path)) {
            return "";
        }
        
        int lastDotIndex = path.lastIndexOf('.');
        int lastSeparatorIndex = Math.max(path.lastIndexOf('/'), path.lastIndexOf('\\'));
        
        if (lastDotIndex > lastSeparatorIndex && lastDotIndex < path.length() - 1) {
            return path.substring(lastDotIndex);
        }
        
        return "";
    }

    /**
     * 获取不带扩展名的文件名
     */
    public static String getFileNameWithoutExtension(String path) {
        if (StrUtil.isBlank(path)) {
            return "";
        }
        
        // 获取文件名部分
        int lastSeparatorIndex = Math.max(path.lastIndexOf('/'), path.lastIndexOf('\\'));
        String fileName = path.substring(lastSeparatorIndex + 1);
        
        // 移除扩展名
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return fileName.substring(0, lastDotIndex);
        }
        
        return fileName;
    }

    /**
     * 构建安全的输出文件路径
     * 确保文件名不包含非法字符
     */
    public static String buildSafeOutputPath(String directory, String fileName) {
        if (StrUtil.isBlank(fileName)) {
            throw new IllegalArgumentException("文件名不能为空");
        }
        
        // 移除或替换非法字符
        String safeFileName = fileName
                .replaceAll("[<>:\"|?*]", "_")  // Windows非法字符
                .replaceAll("[\u0000-\u001f]", "_")  // 控制字符
                .trim();
        
        // 确保文件名不为空
        if (safeFileName.isEmpty()) {
            safeFileName = "output_" + System.currentTimeMillis();
        }
        
        if (StrUtil.isBlank(directory)) {
            return safeFileName;
        }
        
        return normalizePath(directory) + "/" + safeFileName;
    }

    /**
     * 验证路径是否安全（防止路径遍历攻击）
     */
    public static boolean isPathSafe(String path) {
        if (StrUtil.isBlank(path)) {
            return false;
        }
        
        String normalizedPath = normalizePath(path);
        
        // 检查是否包含路径遍历字符
        return !normalizedPath.contains("../") && 
               !normalizedPath.contains("..\\") &&
               !normalizedPath.equals("..") &&
               !normalizedPath.startsWith("../") &&
               !normalizedPath.startsWith("..\\");
    }
}
