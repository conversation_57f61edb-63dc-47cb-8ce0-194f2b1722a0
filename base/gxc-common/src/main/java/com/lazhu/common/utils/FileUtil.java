package com.lazhu.common.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.lazhu.common.media.MediaResourceManager;
import lombok.extern.slf4j.Slf4j;

import java.io.File;

@Slf4j
public class FileUtil {

    /**
     * 获取文件大小（以MB为单位）
     *
     * @param url URL或路径
     * @return 文件大小（MB），失败返回-1
     */
    public static double getFileSizeInMB(String url) {
        log.info("获取文件大小 >> url:{}", url);

        // 参数验证
        MediaResourceManager.validateParameters(url);

        try {
            // 判断是本地文件还是网络文件
            if (url.startsWith("http://") || url.startsWith("https://")) {
                // 网络文件，通过HTTP HEAD请求获取文件大小
                return getRemoteFileSizeInMB(url);
            } else {
                // 本地文件，直接获取文件大小
                File file = new File(url);
                if (file.exists() && file.isFile()) {
                    double sizeInMB = (double) file.length() / (1024 * 1024);
                    log.info("获取本地文件大小成功 >> url:{}, 大小:{} MB", url, String.format("%.2f", sizeInMB));
                    return sizeInMB;
                } else {
                    log.error("本地文件不存在或不是文件 >> url:{}", url);
                    return -1;
                }
            }
        } catch (Exception e) {
            log.error("获取文件大小异常 >> url:{}", url, e);
            return -1;
        }
    }

    /**
     * 获取网络文件大小（以MB为单位）
     *
     * @param url 网络文件URL
     * @return 文件大小（MB），失败返回-1
     */
    private static double getRemoteFileSizeInMB(String url) {
        try (HttpResponse response = HttpRequest.head(url).execute()) {
            if (response.getStatus() == 200) {
                long contentLength = response.contentLength();
                if (contentLength > 0) {
                    double sizeInMB = (double) contentLength / (1024 * 1024);
                    log.info("获取网络文件大小成功 >> url:{}, 大小:{} MB", url, String.format("%.2f", sizeInMB));
                    return sizeInMB;
                }
                return -1;
            } else {
                String msg = StrUtil.format("获取网络文件大小失败，HTTP响应码: {} >> url:{}", response.getStatus(), url);
                throw new RuntimeException(msg);
            }
        }
    }

    public static void main(String[] args) {
        System.out.println(getFileSizeInMB("https://ins-file.lazhuyun.cn/test/video/1953390558219079682/video.mp4"));
        System.out.println(getFileSizeInMB("C:\\Users\\<USER>\\Downloads\\video.mp4"));
    }

}
