package com.lazhu.common.media;

import cn.hutool.core.io.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StreamUtils;

import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * FFmpeg 执行器
 * 负责 FFmpeg 命令的构建、执行和结果处理
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
public class FFmpegExecutor {

    /**
     * ffmpeg.exe 路径
     */
    private static final String FFMPEG_PATH;

    static {
//        try {
//            File ffmpegFile = getFFmpegBinary();
//            FFMPEG_PATH = ffmpegFile.getAbsolutePath();
//        } catch (Exception e) {
//            log.error("获取ffmpeg路径失败", e);
//            throw new RuntimeException(e);
//        }
//        log.info("获取ffmpeg路径 >> {}", FFMPEG_PATH);
        FFMPEG_PATH =  isWindows()?"ffmpeg.exe":"ffmpeg";
    }

    /**
     * FFmpeg执行结果
     */
    public static class FFmpegResult {
        private final int exitCode;
        private final String output;
        private final boolean success;
        private final File outputFile;

        public FFmpegResult(int exitCode, String output, File outputFile) {
            this.exitCode = exitCode;
            this.output = output;
            this.success = exitCode == 0;
            this.outputFile = outputFile;
        }

        public int getExitCode() { return exitCode; }
        public String getOutput() { return output; }
        public boolean isSuccess() { return success; }
        public File getOutputFile() { return outputFile; }
    }

    /**
     * FFmpeg命令构建器
     */
    public static class FFmpegCommandBuilder {
        private final List<String> args = new ArrayList<>();

        public FFmpegCommandBuilder() {
            args.add(FFMPEG_PATH);
        }

        public FFmpegCommandBuilder input(String inputPath) {
            args.add("-i");
            args.add(inputPath);
            return this;
        }

        public FFmpegCommandBuilder output(String outputPath) {
            args.add(outputPath);
            return this;
        }

        public FFmpegCommandBuilder overwrite() {
            args.add("-y");
            return this;
        }

        public FFmpegCommandBuilder videoCodec(String codec) {
            args.add("-c:v");
            args.add(codec);
            return this;
        }

        public FFmpegCommandBuilder audioCodec(String codec) {
            args.add("-c:a");
            args.add(codec);
            return this;
        }

        public FFmpegCommandBuilder quality(int crf) {
            args.add("-crf");
            args.add(String.valueOf(crf));
            return this;
        }

        public FFmpegCommandBuilder videoFilter(String filter) {
            args.add("-vf");
            args.add(filter);
            return this;
        }

        public FFmpegCommandBuilder seekStart(String time) {
            args.add("-ss");
            args.add(time);
            return this;
        }

        public FFmpegCommandBuilder duration(String duration) {
            args.add("-t");
            args.add(duration);
            return this;
        }

        public FFmpegCommandBuilder frames(int count) {
            args.add("-vframes");
            args.add(String.valueOf(count));
            return this;
        }

        public FFmpegCommandBuilder addArg(String arg) {
            args.add(arg);
            return this;
        }

        public FFmpegCommandBuilder addArgs(String... arguments) {
            for (String arg : arguments) {
                args.add(arg);
            }
            return this;
        }

        public String[] build() {
            return args.toArray(new String[0]);
        }

        public List<String> getArgs() {
            return new ArrayList<>(args);
        }
    }

    /**
     * 执行FFmpeg命令的核心方法
     *
     * @param args FFmpeg命令参数
     * @param outputFile 输出文件（可选，用于验证）
     * @param progressCallback 进度回调（可选）
     * @return FFmpeg执行结果
     */
    public static FFmpegResult executeCommand(String[] args, File outputFile, Consumer<String> progressCallback) {
        log.info("执行FFmpeg命令: {}", String.join(" ", args));

        Process process = null;
        BufferedReader reader = null;
        
        try {
            // 确保输出目录存在
            if (outputFile != null) {
                File outputDir = outputFile.getParentFile();
                if (outputDir != null && !outputDir.exists()) {
                    outputDir.mkdirs();
                }
            }

            // 使用ProcessBuilder执行命令
            process = new ProcessBuilder(args)
                    .redirectErrorStream(true)
                    .start();

            // 读取输出信息
            StringBuilder output = new StringBuilder();
            reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
                
                // 进度回调
                if (progressCallback != null) {
                    progressCallback.accept(line);
                }
                
                // 默认进度日志
                if (line.contains("time=")) {
                    log.debug("FFmpeg进度: {}", line);
                }
            }

            // 等待命令执行完成
            int exitCode = process.waitFor();

            // 创建结果对象
            FFmpegResult result = new FFmpegResult(exitCode, output.toString(), outputFile);

            // 验证输出文件
            if (outputFile != null && exitCode == 0) {
                if (!outputFile.exists() || outputFile.length() == 0) {
                    log.error("输出文件未生成或为空: {}", outputFile.getAbsolutePath());
                    return new FFmpegResult(-1, output.toString() + "\n输出文件未生成或为空", outputFile);
                }
                log.info("FFmpeg执行成功，输出文件: {}, 大小: {}KB", 
                        outputFile.getAbsolutePath(), outputFile.length() / 1024);
            }

            return result;

        } catch (Exception e) {
            log.error("FFmpeg命令执行异常", e);
            return new FFmpegResult(-1, "执行异常: " + e.getMessage(), outputFile);
        } finally {
            // 资源清理
            cleanupResources(process, reader);
        }
    }

    /**
     * 执行FFmpeg命令（简化版本）
     */
    public static FFmpegResult executeCommand(String[] args, File outputFile) {
        return executeCommand(args, outputFile, null);
    }

    /**
     * 执行FFmpeg命令（无输出文件验证）
     */
    public static FFmpegResult executeCommand(String[] args) {
        return executeCommand(args, null, null);
    }

    /**
     * 清理进程和IO资源
     */
    private static void cleanupResources(Process process, BufferedReader reader) {
        // 关闭读取器
        if (reader != null) {
            try {
                reader.close();
            } catch (Exception e) {
                log.warn("关闭BufferedReader失败", e);
            }
        }

        // 清理进程资源
        if (process != null) {
            try {
                // 关闭进程的输入输出流
                if (process.getInputStream() != null) {
                    process.getInputStream().close();
                }
                if (process.getOutputStream() != null) {
                    process.getOutputStream().close();
                }
                if (process.getErrorStream() != null) {
                    process.getErrorStream().close();
                }
                
                // 如果进程还在运行，尝试正常终止
                if (process.isAlive()) {
                    log.warn("进程仍在运行，尝试终止...");
                    process.destroy();
                    
                    // 等待一段时间后强制终止
                    if (!process.waitFor(5, TimeUnit.SECONDS)) {
                        log.warn("进程未能正常终止，强制终止...");
                        process.destroyForcibly();
                    }
                }
            } catch (Exception e) {
                log.warn("清理进程资源失败", e);
            }
        }
    }

    /**
     * 获取ffmpeg二进制文件
     */
    private static File getFFmpegBinary() throws Exception {
        // 根据操作系统确定文件后缀
        String suffix = isWindows() ? ".exe" : "";

        // 创建临时文件
        File ffmpegFile = FileUtil.file(FileUtil.getTmpDirPath() + "/ffmpeg" + suffix);
        if (ffmpegFile.exists()) {
            return ffmpegFile;
        }
        ffmpegFile = FileUtil.touch(FileUtil.getTmpDirPath() + "/ffmpeg" + suffix);

        // 从classpath资源加载ffmpeg
        String fileName = isWindows() ? "ffmpeg.exe" : "ffmpeg";
        ClassPathResource resource = new ClassPathResource(fileName);
        try (InputStream in = resource.getInputStream();
             FileOutputStream out = new FileOutputStream(ffmpegFile)) {
            // 复制资源到临时文件
            StreamUtils.copy(in, out);
        }

        // 非Windows系统设置可执行权限
        if (!isWindows()) {
            if (ffmpegFile.setExecutable(true)) {
                log.info("设置ffmpeg可执行权限成功");
            } else {
                throw new RuntimeException("无法设置ffmpeg可执行权限");
            }
        }
        return ffmpegFile;
    }

    /**
     * 执行FFprobe命令获取媒体信息
     *
     * @param videoPath 视频文件路径
     * @return ffprobe执行结果
     */
    public static FFmpegResult executeFFprobe(String videoPath) {
        String ffprobePath = isWindows() ? "ffprobe.exe" : "ffprobe";
        String[] args = {
            ffprobePath,
            "-v", "quiet",              // 静默模式
            "-print_format", "json",     // JSON格式输出
            "-show_streams",             // 显示流信息
            "-select_streams", "v:0",    // 选择第一个视频流
            videoPath
        };
        
        log.info("执行FFprobe命令: {}", String.join(" ", args));
        return executeCommand(args, null, null);
    }

    public static boolean isWindows() {
        return System.getProperty("os.name").toLowerCase().contains("win");
    }
}
