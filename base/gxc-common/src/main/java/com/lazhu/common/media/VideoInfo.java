package com.lazhu.common.media;

import lombok.Data;

@Data
public class VideoInfo {

    /**
     * 宽
     */
    private int width;
    /**
     * 高
     */
    private int height;
    /**
     * 宽高比例 例如 9:16
     */
    private String aspectRatio;

    /**
     * 编码
     */
    private String codec;

    /**
     * 时长 （s）
     */
    private int duration;

    public VideoInfo(int width, int height, String codec, int duration) {
        this.width = width;
        this.height = height;
        this.aspectRatio = getAspectRatio(width, height);
        this.codec = codec;
        this.duration = duration;
    }

    /**
     * 计算两个数的最大公约数
     *
     * @param a 第一个数
     * @param b 第二个数
     * @return 最大公约数
     */
    private static int gcd(int a, int b) {
        if (b == 0) {
            return a;
        }
        return gcd(b, a % b);
    }

    /**
     * 获取视频宽高比
     *
     * @param width  视频宽度
     * @param height 视频高度
     * @return 宽高比字符串，例如"9:16"，失败返回null
     */
    private String getAspectRatio(int width, int height) {
        if (width > 0 && height > 0) {
            // 计算最大公约数
            int gcd = gcd(width, height);

            // 简化比例
            int simplifiedWidth = width / gcd;
            int simplifiedHeight = height / gcd;

            return simplifiedWidth + ":" + simplifiedHeight;
        }
        return null;
    }
}