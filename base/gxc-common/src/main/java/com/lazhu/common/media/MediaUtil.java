package com.lazhu.common.media;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.List;

/**
 * 媒体处理门面类
 * 基于门面模式，将所有媒体处理功能集成到一个统一的入口类中
 * 简化调用方式，提供一站式的媒体处理服务
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
public class MediaUtil {

    // ==================== 视频信息相关方法 ====================

    /**
     * 获取视频/音频时长（秒）
     *
     * @param url 视频URL或路径
     * @return 时长（秒），获取失败返回0
     */
    public static Long getDuration(String url) {
        String processedUrl = MediaUrlProcessor.processMediaUrl(url);
        return VideoInfoService.getDuration(processedUrl);
    }

    /**
     * 获取视频第一帧图片作为封面
     *
     * @param url 视频URL或路径
     * @return 封面图片文件，失败返回null
     */
    public static File getVideoCover(String url) {
        String processedUrl = MediaUrlProcessor.processMediaUrl(url);
        return VideoInfoService.getVideoCover(processedUrl);
    }

    /**
     * 获取视频分辨率
     *
     * @param url 视频URL或路径
     */
    public static VideoInfo getVideoInfo(String url) {
        String processedUrl = MediaUrlProcessor.processMediaUrl(url);
        return VideoInfoService.getVideoInfo(processedUrl);
    }


    // ==================== 视频处理相关方法 ====================

    /**
     * 媒体截取（快速模式，可能不够精确）
     *
     * @param url   视频地址
     * @param start 开始时间（秒）
     * @param end   截取结束时间（秒）
     * @return 截取后的文件路径，失败返回null
     */
    public static String cut(String url, long start, long end) {
        String processedUrl = MediaUrlProcessor.processMediaUrl(url);
        return VideoProcessService.cut(processedUrl, start, end);
    }

    /**
     * 音频文件截取（使用精确模式）
     *
     * @param url   音频地址
     * @param start 开始时间（秒）
     * @param end   截取结束时间（秒）
     * @return 截取后的文件路径，失败返回null
     */
    public static String cutAudio(String url, long start, long end) {
        String processedUrl = MediaUrlProcessor.processMediaUrl(url);
        return VideoProcessService.cutAudio(processedUrl, start, end, true);
    }

    /**
     * 视频合并
     *
     * @param urls    输入视频文件路径列表（支持本地文件路径和网络URL）
     * @param outPath 输出文件路径
     */
    public static void merge(List<String> urls, String outPath) {
        List<String> processedUrls = new java.util.ArrayList<>();
        for (String url : urls) {
            processedUrls.add(MediaUrlProcessor.processMediaUrl(url));
        }
        VideoProcessService.merge(processedUrls, outPath);
    }


    // ==================== 字幕相关方法 ====================

    /**
     * 添加srt字幕到视频（硬字幕烧录）
     *
     * @param videoUrl     视频路径
     * @param subtitlePath 字幕文件路径
     * @param outPath      输出文件路径
     */
    public static void addSubtitle(String videoUrl, String subtitlePath, String outPath) {
        String processedVideoUrl = MediaUrlProcessor.processMediaUrl(videoUrl);
        SubtitleService.addSubtitle(processedVideoUrl, subtitlePath, outPath);
    }


    /**
     * 合成ASS字幕到视频（使用默认参数）
     */
    public static void mergeAssSubtitle(String videoUrl, String assPath, String outPath) {
        String processedVideoUrl = MediaUrlProcessor.processMediaUrl(videoUrl);
        SubtitleService.mergeAssSubtitle(processedVideoUrl, assPath, outPath);
    }

    /**
     * 在视频指定位置插入另一段视频
     */
    public static void insertVideo(String originalUrl, String insertUrl, long insertTime, String outPath) {
        String processedOriginalUrl = MediaUrlProcessor.processMediaUrl(originalUrl);
        String processedInsertUrl = MediaUrlProcessor.processMediaUrl(insertUrl);
        VideoProcessService.insertVideo(processedOriginalUrl, processedInsertUrl, insertTime, outPath);
    }


    
    /**
     * 替换视频中指定时间段的内容（支持位置参数和覆盖模式）
     */
    public static void replaceVideoSegment(String originalUrl, String replaceUrl, long startTime, long endTime,
                                           Position position, OverlayMode overlayMode, String outPath) {
        String processedOriginalUrl = MediaUrlProcessor.processMediaUrl(originalUrl);
        String processedReplaceUrl = MediaUrlProcessor.processMediaUrl(replaceUrl);
        VideoProcessService.replaceVideoSegment(processedOriginalUrl, processedReplaceUrl, startTime, endTime, position, overlayMode, outPath);
    }



    /**
     * 用图片替换视频中指定时间段的帧（支持位置参数和覆盖模式）
     */
    public static void replaceWithImage(String videoUrl, String imageUrl, long startTime, long endTime,
                                        Position position, OverlayMode overlayMode, String outPath) {
        String processedVideoUrl = MediaUrlProcessor.processMediaUrl(videoUrl);
        String processedImageUrl = MediaUrlProcessor.processMediaUrl(imageUrl);
        VideoProcessService.replaceWithImage(processedVideoUrl, processedImageUrl, startTime, endTime, position, overlayMode, outPath);
    }

    /**
     * 压缩视频文件
     *
     * @param inputUrl  输入视频URL或路径
     * @param outPath   输出文件路径
     * @param quality   视频质量 (18-28, 数值越小质量越高)
     * @param maxWidth  最大宽度 (0表示不调整)
     * @param maxHeight 最大高度 (0表示不调整)
     */
    public static void compressVideo(String inputUrl, String outPath, int quality, int maxWidth, int maxHeight) {
        String processedInputUrl = MediaUrlProcessor.processMediaUrl(inputUrl);
        VideoProcessService.compressVideo(processedInputUrl, outPath, quality, maxWidth, maxHeight);
    }

    // ==================== 音频处理相关方法 ====================


    /**
     * 删除视频中的音频轨道（静音处理）
     *
     * @param videoUrl 视频路径（支持本地文件和网络URL）
     * @param outPath  输出文件路径
     */
    public static void removeAudio(String videoUrl, String outPath) {
        String processedVideoUrl = MediaUrlProcessor.processMediaUrl(videoUrl);
        VideoProcessService.removeAudio(processedVideoUrl, outPath);
    }

}
