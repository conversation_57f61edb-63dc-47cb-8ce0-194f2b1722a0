package com.lazhu.common.enums;

import lombok.Getter;

import java.io.Serializable;

@Getter
public enum CurStepEnum implements Serializable {


    TXT_GENERATION(-1, "文案生成"),

    TXT_IN_PRODUCTION(0, "文案生成中"),

    TXT_COMPLETE(1,"文案合成完成"),

    VIDEO_GENERATION(2, "视频合成"),

    VIDEO_IN_PRODUCTION(3,"视频合成中"),

    VIDEO_COMPLETE(4,"视频合成完成")
    ;

    private final Integer type;
    private final String desc;

    CurStepEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
