package com.lazhu.common.media;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 视频处理服务
 * 负责视频的截取、合并、转换等处理操作
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
public class VideoProcessService {

    /**
     * 媒体截取（使用精确模式）
     *
     * @param url   视频地址
     * @param start 开始时间（秒）
     * @param end   截取结束时间（秒）
     * @return 截取后的文件路径，失败返回null
     */
    public static String cut(String url, long start, long end) {
        return cut(url, start, end, true);
    }

    /**
     * 媒体截取（支持精确模式选择）
     *
     * @param url     视频地址
     * @param start   开始时间（秒）
     * @param end     截取结束时间（秒）
     * @param precise 是否使用精确模式（true=重新编码，精确但慢；false=流复制，快速但可能不精确）
     * @return 截取后的文件路径，失败返回null
     */
    public static String cut(String url, long start, long end, boolean precise) {
        log.info("截取媒体文件 >> url:{}, start:{}s, end:{}s, 时长:{}s, 精确模式:{}",
                url, start, end, (end - start), precise);

        // 参数验证
        MediaResourceManager.validateParameters(url);
        if (start < 0 || end <= start) {
            throw new IllegalArgumentException("时间参数无效：start=" + start + ", end=" + end);
        }

        try {
            // 创建输出文件
            String fileName = MediaPathUtils.getFileNameWithoutExtension(url);
            String extension = MediaPathUtils.getFileExtension(url);
            if (extension.isEmpty()) {
                extension = ".mp4";
            }

            String mode = precise ? "precise" : "fast";
            File outputFile = MediaResourceManager.createTempFile(
                    fileName + "_cut_" + mode + "_" + start + "s_to_" + end + "s", extension);

            String[] args;
            if (precise) {
                // 精确模式：重新编码，确保时间精确
                args = new FFmpegExecutor.FFmpegCommandBuilder()
                        .input(url)  // -i 在前，让FFmpeg准确解析
                        .seekStart(String.valueOf(start))  // -ss 在 -i 之后，精确定位
                        .duration(String.valueOf(end - start))  // -t 指定持续时间
                        .videoCodec("libx264")  // 重新编码视频
                        .audioCodec("aac")      // 重新编码音频
                        .addArgs("-crf", "23")  // 设置质量
                        .addArgs("-preset", "fast")  // 编码速度
                        .addArgs("-avoid_negative_ts", "make_zero")  // 避免负时间戳
                        .addArgs("-copyts")     // 保持时间戳
                        .overwrite()
                        .output(outputFile.getAbsolutePath())
                        .build();
            } else {
                // 快速模式：流复制，速度快但可能不够精确
                args = new FFmpegExecutor.FFmpegCommandBuilder()
                        .seekStart(String.valueOf(start))  // -ss 在 -i 之前，快速定位
                        .input(url)
                        .duration(String.valueOf(end - start))
                        .addArgs("-c", "copy")  // 流复制，不重新编码
                        .addArgs("-avoid_negative_ts", "make_zero")  // 避免负时间戳
                        .overwrite()
                        .output(outputFile.getAbsolutePath())
                        .build();
            }

            // 执行命令
            FFmpegExecutor.FFmpegResult result = FFmpegExecutor.executeCommand(args, outputFile);

            if (result.isSuccess()) {
                // 验证输出文件的实际时长
                Long actualDuration = VideoInfoService.getDuration(outputFile.getAbsolutePath());
                long expectedDuration = end - start;

                log.info("截取媒体文件成功 >> url:{}, 输出文件:{}, 实际大小:{}KB, 期望时长:{}s, 实际时长:{}s",
                        url, outputFile.getAbsolutePath(), outputFile.length() / 1024,
                        expectedDuration, actualDuration);

                // 如果时长差异超过1秒且当前是快速模式，建议使用精确模式
                if (!precise && actualDuration != null && Math.abs(actualDuration - expectedDuration) > 1) {
                    log.warn("时长差异较大(期望:{}s, 实际:{}s)，建议使用精确模式: cut(url, start, end, true)",
                            expectedDuration, actualDuration);
                }

                return outputFile.getAbsolutePath();
            } else {
                log.error("截取媒体文件失败 >> url:{}, 错误信息:{}", url, result.getOutput());
                MediaResourceManager.cleanupTempFile(outputFile);
                return null;
            }
        } catch (Exception e) {
            log.error("截取媒体文件异常 >> url:{}", url, e);
            throw new RuntimeException("截取媒体文件异常", e);
        }
    }

    /**
     * 音频文件截取（支持精确模式选择）
     *
     * @param url       音频地址
     * @param startTime 开始时间（毫秒）
     * @param endTime   截取结束时间（毫秒，包含此时间点）
     * @param precise   是否使用精确模式（true=重新编码，精确但慢；false=流复制，快速但可能不精确）
     * @return 截取后的文件路径，失败返回null
     */
    public static String cutAudio(String url, long startTime, long endTime, boolean precise) {
        double start = NumberUtil.div(startTime, 1000, 3);
        double end = NumberUtil.div(endTime, 1000, 3);
        // （包含endTime）结束时间添加1毫秒
        double inclusiveEnd = NumberUtil.div(endTime + 1, 1000, 3);
        double duration = inclusiveEnd - start;
        log.info("截取音频文件 >> url:{}, start:{}s, end:{}s, 时长:{}s, 精确模式:{}", url, start, end, duration, precise);

        // 参数验证
        MediaResourceManager.validateParameters(url);
        if (start < 0 || end <= start) {
            throw new IllegalArgumentException("时间参数无效：start=" + start + ", end=" + end);
        }

        try {
            // 创建输出文件
            String fileName = MediaPathUtils.getFileNameWithoutExtension(url);
            String extension = MediaPathUtils.getFileExtension(url).toLowerCase();
            if (extension.isEmpty()) {
                extension = ".mp3";
            }

            String mode = precise ? "precise" : "fast";
            File outputFile = MediaResourceManager.createTempFile(fileName + "_cut_" + mode + "_" + start + "s_to_" + end + "s", extension);

            String[] args;
            if (precise) {
                // 精确模式：重新编码，确保时间精确
                FFmpegExecutor.FFmpegCommandBuilder builder = new FFmpegExecutor.FFmpegCommandBuilder()
                        .input(url)  // -i 在前，让FFmpeg准确解析
                        .seekStart(String.valueOf(start))  // -ss 在 -i 之后，精确定位
                        .duration(String.valueOf(duration))  // -t 指定持续时间
                        .addArgs("-avoid_negative_ts", "make_zero")  // 避免负时间戳
                        .addArgs("-copyts")     // 保持时间戳
                        .overwrite()
                        .output(outputFile.getAbsolutePath());

                // 根据输入文件类型决定音频编解码器
                if (extension.equals(".mp3")) {
                    // 对于MP3文件，可以使用aac或者copy
                    builder.addArgs("-c:a", "copy"); // 使用流复制以避免重新编码问题
                } else {
                    builder.audioCodec("aac"); // 其他格式使用aac重新编码
                }

                args = builder.build();
            } else {
                // 快速模式：流复制，速度快但可能不够精确
                args = new FFmpegExecutor.FFmpegCommandBuilder()
                        .seekStart(String.valueOf(start))  // -ss 在 -i 之前，快速定位
                        .input(url)
                        .duration(String.valueOf(duration))  // -t 指定持续时间
                        .addArgs("-c", "copy")  // 流复制，不重新编码
                        .addArgs("-avoid_negative_ts", "make_zero")  // 避免负时间戳
                        .overwrite()
                        .output(outputFile.getAbsolutePath())
                        .build();
            }

            // 执行命令
            FFmpegExecutor.FFmpegResult result = FFmpegExecutor.executeCommand(args, outputFile);

            if (result.isSuccess()) {
                // 验证输出文件的实际时长
                Long actualDuration = VideoInfoService.getDuration(outputFile.getAbsolutePath());
                long expectedDurationS = Math.round(duration);

                log.info("截取音频文件成功 >> url:{}, 输出文件:{}, 实际大小:{}KB, 期望时长:{}s, 实际时长:{}s",
                        url, outputFile.getAbsolutePath(), outputFile.length() / 1024,
                        expectedDurationS, actualDuration);

                // 如果时长差异超过1秒且当前是快速模式，建议使用精确模式
                if (!precise && actualDuration != null && Math.abs(actualDuration - expectedDurationS) > 1) {
                    log.warn("时长差异较大(期望:{}s, 实际:{}s)，建议使用精确模式: cutAudio(url, start, end, true)",
                            expectedDurationS, actualDuration);
                }

                return outputFile.getAbsolutePath();
            } else {
                log.error("截取音频文件失败 >> url:{}, 错误信息:{}", url, result.getOutput());
                MediaResourceManager.cleanupTempFile(outputFile);
                return null;
            }
        } catch (Exception e) {
            log.error("截取音频文件异常 >> url:{}", url, e);
            throw new RuntimeException("截取音频文件异常", e);
        }
    }

    /**
     * 视频合并
     *
     * @param urls    输入视频文件路径列表（支持本地文件路径和网络URL）
     * @param outPath 输出文件路径
     */
    public static void merge(List<String> urls, String outPath) {
        log.info("视频合成 >> 输入文件:{}, 输出路径:{}", StrUtil.join(", ", urls), outPath);

        // 参数验证
        if (urls == null || urls.isEmpty()) {
            throw new IllegalArgumentException("输入视频列表不能为空");
        }
        MediaResourceManager.validateParameters(outPath);

        if (urls.size() == 1) {
            // 只有一个视频，直接复制或下载
            handleSingleVideo(urls.getFirst(), outPath);
            return;
        }

        // 多个视频需要合并
        mergeMultipleVideos(urls, outPath);
    }

    /**
     * 处理单个视频的情况
     */
    private static void handleSingleVideo(String sourceUrl, String outPath) {
        try {
            MediaResourceManager.ensureOutputDirectory(outPath);

            if (MediaPathUtils.isNetworkUrl(sourceUrl)) {
                // 网络URL，下载文件
                HttpUtil.downloadFile(sourceUrl, outPath);
            } else {
                // 本地文件，复制文件
                FileUtil.copy(sourceUrl, outPath, true);
            }
            log.info("只有一个视频，直接复制到输出路径 >> 输出文件:{}", outPath);
        } catch (Exception e) {
            log.error("复制单个视频文件失败 >> 错误信息:{}", e.getMessage());
            throw new RuntimeException("复制单个视频文件失败", e);
        }
    }

    /**
     * 合并多个视频
     */
    private static void mergeMultipleVideos(List<String> urls, String outPath) {
        // 默认使用无转场的方式
        mergeMultipleVideosInternal(urls, outPath, false, 0.5, "fade");
    }

    /**
     * 合并多个视频（支持可选转场）
     */
    private static void mergeMultipleVideosInternal(List<String> urls, String outPath, boolean withTransition, double durationSec, String transitionType) {
        if (withTransition) {
            mergeMultipleVideosWithTransition(urls, outPath, durationSec, transitionType);
        } else {
            // 保持现有逻辑
            List<String> localVideoPaths = new ArrayList<>();
            List<File> tempFiles = new ArrayList<>();
            try {
                for (String url : urls) {
                    if (MediaPathUtils.isNetworkUrl(url)) {
                        File tempFile = MediaResourceManager.createTempFileUnmanaged("video_merge", ".mp4");
                        HttpUtil.downloadFile(url, tempFile);
                        localVideoPaths.add(tempFile.getAbsolutePath());
                        tempFiles.add(tempFile);
                        log.info("下载网络视频到临时文件 >> URL:{}, 临时文件:{}", url, tempFile.getAbsolutePath());
                    } else {
                        localVideoPaths.add(url);
                    }
                }
                // 创建列表文件
                File listFile = MediaResourceManager.createTempFileUnmanaged("listfile", ".txt");
                tempFiles.add(listFile);
                try (BufferedWriter writer = new BufferedWriter(new FileWriter(listFile))) {
                    for (String path : localVideoPaths) {
                        writer.write("file '" + path.replace("'", "'\\''") + "'");
                        writer.newLine();
                    }
                }
                // 优先用无缝合并
                log.info("强制使用无缝合并方案");
                if (trySeamlessMerge(listFile.getAbsolutePath(), outPath)) {
                    log.info("无缝合并合成成功 >> 输出文件:{}", outPath);
                } else {
                    log.warn("无缝合并失败，尝试快速合并作为备选方案");
                    if (tryFastMerge(listFile.getAbsolutePath(), outPath)) {
                        log.info("快速合并成功 >> 输出文件:{}", outPath);
                    } else {
                        throw new RuntimeException("视频合并失败：无缝合并和快速合并都失败");
                    }
                }
            } catch (Exception e) {
                throw new RuntimeException("合并视频失败", e);
            } finally {
                for (File tempFile : tempFiles) {
                    MediaResourceManager.cleanupTempFile(tempFile);
                }
            }
        }
    }

    /**
     * 使用 xfade 加转场合并视频
     */
    private static void mergeMultipleVideosWithTransition(List<String> urls, String outPath, double durationSec, String transitionType) {
        List<String> tempFiles = new ArrayList<>();
        try {
            // 先预处理，统一分辨率/帧率/音频，保证 xfade 兼容
            List<String> normalized = normalizeVideos(urls);
            tempFiles.addAll(normalized);
            if (normalized.size() < 2) {
                // 一个视频直接复制
                handleSingleVideo(normalized.getFirst(), outPath);
                return;
            }
            // 构建 xfade filter_complex，计算每段的时长并设置数值型 offset
            StringBuilder filter = new StringBuilder();
            FFmpegExecutor.FFmpegCommandBuilder builder = new FFmpegExecutor.FFmpegCommandBuilder();
            for (String f : normalized) {
                builder.input(f);
            }
            int n = normalized.size();
            // 读取各段时长（秒）
            java.util.List<Double> durations = new java.util.ArrayList<>();
            for (String f : normalized) {
                Long d = VideoInfoService.getDuration(f);
                durations.add(d == null ? 0D : d.doubleValue());
            }
            String vPrev = "[0:v]";
            String aPrev = "[0:a]";
            double cum = durations.get(0);
            for (int i = 1; i < n; i++) {
                String vCur = "[" + i + ":v]";
                String aCur = "[" + i + ":a]";
                String vOut = "[v" + i + "]";
                String aOut = "[a" + i + "]";
                double offsetVal = Math.max(0.0, cum - durationSec);
                // 先视频 xfade，再音频 acrossfade（acrossfade 会在首尾对齐处进行交叉淡入淡出）
                filter.append(vPrev).append(vCur)
                        .append("xfade=transition=").append(transitionType)
                        .append(":duration=").append(durationSec)
                        .append(":offset=").append(offsetVal)
                        .append(",format=yuv420p").append(vOut).append(";");
                filter.append(aPrev).append(aCur)
                        .append("acrossfade=d=").append(durationSec)
                        .append(aOut).append(";");
                vPrev = vOut;
                aPrev = aOut;
                cum = cum + durations.get(i) - durationSec;
            }
            String[] args = builder
                    .addArgs("-filter_complex", filter.toString())
                    .addArgs("-map", vPrev)
                    .addArgs("-map", aPrev)
                    .videoCodec("libx264")
                    .audioCodec("aac")
                    .addArgs("-crf", "23")
                    .addArgs("-preset", "fast")
                    .addArgs("-pix_fmt", "yuv420p")
                    .overwrite()
                    .output(outPath)
                    .build();
            FFmpegExecutor.FFmpegResult result = FFmpegExecutor.executeCommand(args, new File(outPath));
            if (!result.isSuccess()) {
                throw new RuntimeException("转场合并失败: " + result.getOutput());
            }
        } catch (Exception e) {
            throw new RuntimeException("使用转场合并视频失败", e);
        } finally {
            for (String tempFile : tempFiles) {
                MediaResourceManager.cleanupTempFile(new File(tempFile));
            }
        }
    }

    /**
     * 对外暴露：合并并添加转场
     */
    public static void mergeWithTransition(List<String> urls, String outPath, double durationSec, String transitionType) {
        mergeMultipleVideosInternal(urls, outPath, true, durationSec, transitionType);
    }

    /**
     * 尝试快速合并（仅复制流，要求编码格式一致）
     * 优化版本：添加时间戳修复参数
     */
    private static boolean tryFastMerge(String listFilePath, String outPath) {
        try {
            MediaResourceManager.ensureOutputDirectory(outPath);
            File outputFile = new File(outPath);

            // 构建FFmpeg命令 - 添加时间戳修复参数
            String[] args = new FFmpegExecutor.FFmpegCommandBuilder()
                    .addArgs("-f", "concat")
                    .addArgs("-safe", "0")
                    .input(listFilePath)
                    .addArgs("-c", "copy")
                    .addArgs("-avoid_negative_ts", "make_zero")  // 避免负时间戳
                    .addArgs("-fflags", "+genpts")              // 重新生成时间戳
                    .addArgs("-reset_timestamps", "1")          // 重置时间戳
                    .overwrite()
                    .output(outPath)
                    .build();

            // 执行命令
            FFmpegExecutor.FFmpegResult result = FFmpegExecutor.executeCommand(args, outputFile);

            if (result.isSuccess()) {
                log.info("快速视频合并成功");
                return true;
            } else {
                log.warn("快速视频合并失败 >> 错误信息:{}", result.getOutput());
                return false;
            }
        } catch (Exception e) {
            log.warn("快速视频合并异常 >> 错误信息:{}", e.getMessage());
            return false;
        }
    }


    /**
     * 无缝合并
     * 使用filter_complex确保完全无缝衔接
     */
    private static boolean trySeamlessMerge(String listFilePath, String outPath) {
        try {
            MediaResourceManager.ensureOutputDirectory(outPath);
            File outputFile = new File(outPath);

            log.info("使用无缝重编码方式合并视频");

            // 读取文件列表
            List<String> videoFiles = readVideoListFile(listFilePath);
            if (videoFiles.isEmpty()) {
                log.error("视频文件列表为空");
                return false;
            }

            // 预处理：统一所有视频的分辨率和帧率
            List<String> normalizedFiles = normalizeVideos(videoFiles);
            if (normalizedFiles.isEmpty()) {
                log.error("视频预处理失败");
                return false;
            }

            // 根据视频数量构建不同的filter_complex
            if (normalizedFiles.size() == 2) {
                return mergeTwoVideos(normalizedFiles, outPath);
            } else if (normalizedFiles.size() == 3) {
                return mergeThreeVideos(normalizedFiles, outPath);
            } else {
                // 多于3个视频，使用通用方法
                return mergeMultipleVideosSeamless(normalizedFiles, outPath);
            }

        } catch (Exception e) {
            log.error("无缝视频合并异常 >> 错误信息:{}", e.getMessage());
            return false;
        }
    }

    /**
     * 预处理视频：统一分辨率、帧率和编码参数
     * 确保所有视频都有音频轨道（静音）以简化合并过程
     */
    private static List<String> normalizeVideos(List<String> videoFiles) {
        List<String> normalizedFiles = new ArrayList<>();
        List<File> tempFiles = new ArrayList<>();

        try {
            // 目标参数：统一为1080x1920, 25fps, H.264，确保都有音频轨道
            String targetResolution = "1080x1920";
            String targetFrameRate = "25";

            log.info("开始预处理视频，统一参数为: {}@{}fps，并确保所有视频都有音频轨道", targetResolution, targetFrameRate);

            for (int i = 0; i < videoFiles.size(); i++) {
                String inputFile = videoFiles.get(i);
                String normalizedFile = MediaResourceManager.createTempFile("normalized_" + i, ".mp4").getAbsolutePath();
                tempFiles.add(new File(normalizedFile));

                // 检测是否有音频
                boolean hasAudio = hasAudioTrack(inputFile);
                log.info("视频 {} 音频检测: {}", inputFile, hasAudio ? "有音频" : "无音频");

                // 统一视频参数，无音频的添加静音轨道
                FFmpegExecutor.FFmpegCommandBuilder builder = new FFmpegExecutor.FFmpegCommandBuilder()
                        .input(inputFile)
                        .addArgs("-vf", "scale=1080:1920:force_original_aspect_ratio=decrease:force_divisible_by=2,pad=1080:1920:(ow-iw)/2:(oh-ih)/2")
                        .addArgs("-r", targetFrameRate)                // 统一帧率
                        .videoCodec("libx264")                         // 统一视频编码
                        .addArgs("-crf", "23")                         // 质量控制
                        .addArgs("-preset", "fast")                    // 编码速度
                        .addArgs("-pix_fmt", "yuv420p")               // 统一像素格式
                        .addArgs("-ar", "44100")                      // 统一音频采样率
                        .addArgs("-ac", "2")                          // 统一为立体声
                        .addArgs("-avoid_negative_ts", "make_zero");   // 避免负时间戳

                if (hasAudio) {
                    // 有音频，使用原音频
                    builder.audioCodec("aac");
                } else {
                    // 无音频，生成静音轨道 - 使用正确的FFmpeg语法
                    // 需要创建一个新的命令构建器来正确处理多输入
                    builder = new FFmpegExecutor.FFmpegCommandBuilder()
                            .input(inputFile)
                            .addArgs("-f", "lavfi")
                            .addArgs("-i", "anullsrc=channel_layout=stereo:sample_rate=44100")
                            .addArgs("-vf", "scale=1080:1920:force_original_aspect_ratio=decrease:force_divisible_by=2,pad=1080:1920:(ow-iw)/2:(oh-ih)/2")
                            .addArgs("-r", targetFrameRate)
                            .videoCodec("libx264")
                            .addArgs("-crf", "23")
                            .addArgs("-preset", "fast")
                            .addArgs("-pix_fmt", "yuv420p")
                            .addArgs("-ar", "44100")
                            .addArgs("-ac", "2")
                            .addArgs("-avoid_negative_ts", "make_zero")
                            .addArgs("-map", "0:v")     // 映射视频流
                            .addArgs("-map", "1:a")     // 映射生成的静音流
                            .audioCodec("aac")
                            .addArgs("-shortest");      // 以最短流为准
                }

                String[] args = builder.overwrite()
                        .output(normalizedFile)
                        .build();

                log.info("预处理视频 {} -> {} ({})", inputFile, normalizedFile, hasAudio ? "保留音频" : "添加静音");
                FFmpegExecutor.FFmpegResult result = FFmpegExecutor.executeCommand(args, new File(normalizedFile));

                if (result.isSuccess()) {
                    normalizedFiles.add(normalizedFile);
                    log.info("视频预处理成功: {}", normalizedFile);
                } else {
                    log.error("视频预处理失败: {} -> 错误信息: {}", inputFile, result.getOutput());
                    // 清理已创建的临时文件
                    for (File tempFile : tempFiles) {
                        MediaResourceManager.cleanupTempFile(tempFile);
                    }
                    return new ArrayList<>();
                }
            }

            log.info("所有视频预处理完成，统一参数: {}@{}fps，所有视频都有音频轨道", targetResolution, targetFrameRate);
            return normalizedFiles;

        } catch (Exception e) {
            log.error("视频预处理异常", e);
            // 清理临时文件
            for (File tempFile : tempFiles) {
                MediaResourceManager.cleanupTempFile(tempFile);
            }
            return new ArrayList<>();
        }
    }

    /**
     * 读取视频列表文件
     */
    private static List<String> readVideoListFile(String listFilePath) {
        List<String> videoFiles = new ArrayList<>();
        try {
            List<String> lines = java.nio.file.Files.readAllLines(java.nio.file.Paths.get(listFilePath));
            for (String line : lines) {
                if (line.trim().startsWith("file '") && line.trim().endsWith("'")) {
                    String filePath = line.trim().substring(6, line.trim().length() - 1);
                    videoFiles.add(filePath);
                }
            }
        } catch (Exception e) {
            log.error("读取视频列表文件失败: {}", e.getMessage());
        }
        return videoFiles;
    }

    /**
     * 合并两个视频（无缝衔接）
     * 由于预处理已确保所有视频都有音频轨道，使用简化的concat滤镜
     */
    private static boolean mergeTwoVideos(List<String> videoFiles, String outPath) {
        try {
            File outputFile = new File(outPath);

            log.info("合并两个预处理后的视频（都有音频轨道）");

            // 使用简化的concat滤镜，因为所有视频都有音频
            String filterComplex = "[0:v][0:a][1:v][1:a]concat=n=2:v=1:a=1[outv][outa]";

            log.info("两个视频合并filter_complex: {}", filterComplex);

            String[] args = new FFmpegExecutor.FFmpegCommandBuilder()
                    .input(videoFiles.get(0))
                    .input(videoFiles.get(1))
                    .addArgs("-filter_complex", filterComplex)
                    .addArgs("-map", "[outv]")
                    .addArgs("-map", "[outa]")
                    .videoCodec("libx264")                      // 统一视频编码
                    .audioCodec("aac")                          // 统一音频编码
                    .addArgs("-crf", "23")                      // 质量控制
                    .addArgs("-preset", "fast")                 // 编码速度
                    .addArgs("-pix_fmt", "yuv420p")            // 统一像素格式
                    .addArgs("-r", "25")                       // 统一帧率
                    .addArgs("-avoid_negative_ts", "make_zero") // 避免负时间戳
                    .addArgs("-vsync", "cfr")                  // 视频同步 - 使用字符串参数
                    .addArgs("-async", "1")                    // 音频同步
                    .overwrite()
                    .output(outPath)
                    .build();

            FFmpegExecutor.FFmpegResult result = FFmpegExecutor.executeCommand(args, outputFile);

            if (result.isSuccess()) {
                log.info("两个视频无缝合并成功");
                return true;
            } else {
                log.error("两个视频无缝合并失败 >> 错误信息:{}", result.getOutput());
                return false;
            }
        } catch (Exception e) {
            log.error("两个视频无缝合并异常", e);
            return false;
        }
    }

    /**
     * 合并三个视频（无缝衔接）
     * 由于预处理已确保所有视频都有音频轨道，使用简化的concat滤镜
     */
    private static boolean mergeThreeVideos(List<String> videoFiles, String outPath) {
        try {
            File outputFile = new File(outPath);

            log.info("合并三个预处理后的视频（都有音频轨道）");

            // 使用简化的concat滤镜，因为所有视频都有音频
            String filterComplex = "[0:v][0:a][1:v][1:a][2:v][2:a]concat=n=3:v=1:a=1[outv][outa]";

            log.info("三个视频合并filter_complex: {}", filterComplex);

            String[] args = new FFmpegExecutor.FFmpegCommandBuilder()
                    .input(videoFiles.get(0))
                    .input(videoFiles.get(1))
                    .input(videoFiles.get(2))
                    .addArgs("-filter_complex", filterComplex)
                    .addArgs("-map", "[outv]")
                    .addArgs("-map", "[outa]")
                    .videoCodec("libx264")                      // 统一视频编码
                    .audioCodec("aac")                          // 统一音频编码
                    .addArgs("-crf", "23")                      // 质量控制
                    .addArgs("-preset", "fast")                 // 编码速度
                    .addArgs("-pix_fmt", "yuv420p")            // 统一像素格式
                    .addArgs("-r", "25")                       // 统一帧率
                    .addArgs("-avoid_negative_ts", "make_zero") // 避免负时间戳
                    .addArgs("-vsync", "cfr")                  // 视频同步 - 使用字符串参数
                    .addArgs("-async", "1")                    // 音频同步
                    .overwrite()
                    .output(outPath)
                    .build();

            FFmpegExecutor.FFmpegResult result = FFmpegExecutor.executeCommand(args, outputFile);

            if (result.isSuccess()) {
                log.info("三个视频无缝合并成功");
                return true;
            } else {
                log.error("三个视频无缝合并失败 >> 错误信息:{}", result.getOutput());
                return false;
            }
        } catch (Exception e) {
            log.error("三个视频无缝合并异常", e);
            return false;
        }
    }

    /**
     * 合并多个视频（通用方法）
     * 由于预处理已确保所有视频都有音频轨道，使用简化的concat滤镜
     */
    private static boolean mergeMultipleVideosSeamless(List<String> videoFiles, String outPath) {
        try {
            File outputFile = new File(outPath);

            log.info("合并{}个预处理后的视频（都有音频轨道）", videoFiles.size());

            // 构建简化的filter_complex，因为所有视频都有音频
            StringBuilder filterBuilder = new StringBuilder();
            FFmpegExecutor.FFmpegCommandBuilder cmdBuilder = new FFmpegExecutor.FFmpegCommandBuilder();

            // 添加所有输入文件
            for (String videoFile : videoFiles) {
                cmdBuilder.input(videoFile);
            }

            // 构建concat滤镜 - 所有视频都有音频和视频流
            for (int i = 0; i < videoFiles.size(); i++) {
                filterBuilder.append("[").append(i).append(":v]");
                filterBuilder.append("[").append(i).append(":a]");
            }
            filterBuilder.append("concat=n=").append(videoFiles.size()).append(":v=1:a=1[outv][outa]");

            log.info("动态构建的filter_complex: {}", filterBuilder);

            String[] args = cmdBuilder
                    .addArgs("-filter_complex", filterBuilder.toString())
                    .addArgs("-map", "[outv]")
                    .addArgs("-map", "[outa]")
                    .videoCodec("libx264")                      // 统一视频编码
                    .audioCodec("aac")                          // 统一音频编码
                    .addArgs("-crf", "23")                      // 质量控制
                    .addArgs("-preset", "fast")                 // 编码速度
                    .addArgs("-pix_fmt", "yuv420p")            // 统一像素格式
                    .addArgs("-r", "25")                       // 统一帧率
                    .addArgs("-avoid_negative_ts", "make_zero") // 避免负时间戳
                    .addArgs("-vsync", "cfr")                  // 视频同步 - 使用字符串参数
                    .addArgs("-async", "1")                    // 音频同步
                    .overwrite()
                    .output(outPath)
                    .build();

            FFmpegExecutor.FFmpegResult result = FFmpegExecutor.executeCommand(args, outputFile);

            if (result.isSuccess()) {
                log.info("多个视频无缝合并成功");
                return true;
            } else {
                log.error("多个视频无缝合并失败 >> 错误信息:{}", result.getOutput());
                return false;
            }
        } catch (Exception e) {
            log.error("多个视频无缝合并异常", e);
            return false;
        }
    }

    /**
     * 检测视频文件是否有音频轨道
     */
    public static boolean hasAudioTrack(String videoFile) {
        try {
            // 使用FFmpegCommandBuilder构建命令，它会自动包含正确的FFmpeg路径
            String[] args = new FFmpegExecutor.FFmpegCommandBuilder()
                    .input(videoFile)
                    .addArgs("-f", "null", "-")
                    .build();

            FFmpegExecutor.FFmpegResult result = FFmpegExecutor.executeCommand(args, null);
            String output = result.getOutput();

            // 检查输出中是否包含音频流信息
            // 查找类似 "Stream #0:1[0x2](und): Audio:" 的行
            boolean hasAudio = output.contains("Audio:") &&
                    output.matches("(?s).*Stream #\\d+:\\d+.*Audio:.*");

            log.debug("音频轨道检测 - 文件: {}, 结果: {}", videoFile, hasAudio);
            return hasAudio;

        } catch (Exception e) {
            log.warn("检测音频轨道失败，默认假设有音频: {}", videoFile, e);
            return true; // 出错时默认假设有音频
        }
    }

    /**
     * 在视频指定位置插入另一段视频
     *
     * @param originalUrl 原始视频URL
     * @param insertUrl   要插入的视频URL
     * @param insertTime  插入位置（秒）
     * @param outPath     输出文件路径
     */
    public static void insertVideo(String originalUrl, String insertUrl, long insertTime, String outPath) {
        log.info("在视频中插入视频 >> 原视频:{}, 插入视频:{}, 插入位置:{}s",
                originalUrl, insertUrl, insertTime);
        List<File> tempFiles = new ArrayList<>();
        try {
            // 1. 截取原视频前半段
            String part1 = MediaResourceManager.createTempFile("part1", ".mp4").getAbsolutePath();
            String cutPart1Result = cut(originalUrl, 0, insertTime, true);
            if (cutPart1Result != null) {
                // 将结果文件复制到part1位置
                FileUtil.copy(cutPart1Result, part1, true);
                tempFiles.add(new File(part1));
                tempFiles.add(new File(cutPart1Result));
            }

            // 2. 截取原视频后半段
            Long totalDuration = VideoInfoService.getDuration(originalUrl);
            String part2 = MediaResourceManager.createTempFile("part2", ".mp4").getAbsolutePath();
            String cutPart2Result = cut(originalUrl, insertTime, totalDuration, true);
            if (cutPart2Result != null) {
                // 将结果文件复制到part2位置
                FileUtil.copy(cutPart2Result, part2, true);
                tempFiles.add(new File(part2));
                tempFiles.add(new File(cutPart2Result));
            }

            // 3. 合并三段视频
            List<String> videos = Arrays.asList(part1, insertUrl, part2);
            merge(videos, outPath);

        } catch (Exception e) {
            log.error("插入视频失败", e);
            throw new RuntimeException("插入视频失败", e);
        } finally {
            // 清理临时文件
            tempFiles.forEach(MediaResourceManager::cleanupTempFile);
        }
    }


    /**
     * 替换视频中指定时间段的内容（保持原音频）
     *
     * @param originalUrl 原始视频URL
     * @param replaceUrl  替换视频URL
     * @param startTime   替换开始时间（毫秒）
     * @param endTime     替换结束时间（毫秒）
     * @param position    替换视频的位置信息（可选）
     * @param overlayMode 覆盖模式（透明背景或黑色背景）
     * @param outPath     输出文件路径
     */
    public static void replaceVideoSegment(String originalUrl, String replaceUrl,
                                           long startTime, long endTime,
                                           Position position,
                                           OverlayMode overlayMode,
                                           String outPath) {
        log.info("替换视频片段（只替换画面，保持原音频） >> 原视频:{}, 替换视频:{}, 时间段:{}s-{}s, 覆盖模式:{}",
                originalUrl, replaceUrl, startTime, endTime, overlayMode);

        List<File> tempFiles = new ArrayList<>();
        try {
            MediaResourceManager.ensureOutputDirectory(outPath);
            File outputFile = new File(outPath);

            Long replaceDuration = endTime - startTime;
            Long replaceVideoDuration = VideoInfoService.getDuration(replaceUrl);

            // 处理替换视频，确保时长匹配
            String processedReplaceVideo = replaceUrl;
            if (replaceVideoDuration > replaceDuration) {
                String trimmedReplace = MediaResourceManager.createTempFile("replace_trimmed", ".mp4").getAbsolutePath();
                String cutResult = cut(replaceUrl, 0, replaceDuration, true);
                if (cutResult != null) {
                    FileUtil.copy(cutResult, trimmedReplace, true);
                    processedReplaceVideo = trimmedReplace;
                    tempFiles.add(new File(trimmedReplace));
                    tempFiles.add(new File(cutResult));
                } else {
                    log.warn("替换视频裁剪失败，使用原始视频");
                }
            } else if (replaceVideoDuration < replaceDuration) {
                endTime = startTime + replaceVideoDuration;
                log.warn("替换视频长度不足，修改替换时间段为: {}s-{}s", startTime, endTime);
            }


            // 构建滤镜复杂参数
            StringBuilder filterBuilder = new StringBuilder();


            // 先创建一个全屏的背景（透明或黑色），覆盖指定的时间段
            filterBuilder.append(String.format(
                    "color=%s:size=1080x1920:d=%d:r=25[background];",
                    overlayMode.getColor(), endTime - startTime));

            // 对替换视频进行缩放处理
            if (position != null && position.getWidth() != null && position.getHeight() != null) {
                filterBuilder.append(String.format(
                        "[1:v]scale=%d:%d:force_original_aspect_ratio=decrease:force_divisible_by=2[scaled_replace];",
                        position.getWidth(), position.getHeight()));
            } else {
                filterBuilder.append("[1:v]scale=1080:1920:force_original_aspect_ratio=decrease:force_divisible_by=2[scaled_replace];");
            }

            // 在背景上叠加替换视频
            if (position != null && position.getLocationX() != null && position.getLocationY() != null) {
                // 使用指定的位置坐标
                filterBuilder.append(String.format(
                        "[background][scaled_replace]overlay=%d:%d[replace_with_background];",
                        position.getLocationX(), position.getLocationY()));
            } else {
                // 使用默认的覆盖位置（全屏覆盖），坐标为(0,0)
                filterBuilder.append("[background][scaled_replace]overlay=0:0[replace_with_background];");
            }
            // 在原视频的指定时间段覆盖处理后的内容
            filterBuilder.append(String.format("[0:v][replace_with_background]overlay=enable='between(t,%d,%d)'[outv]", startTime, endTime));


            String filterComplex = filterBuilder.toString();

            log.info("使用overlay滤镜替换视频帧 >> 时间段: {}s-{}s, 位置信息: {}, 覆盖模式: {}", startTime, endTime, position, overlayMode);

            String[] args = new FFmpegExecutor.FFmpegCommandBuilder()
                    .input(originalUrl)           // 输入0: 原视频（视频+音频）
                    .input(processedReplaceVideo) // 输入1: 处理后的替换视频
                    .addArgs("-filter_complex", filterComplex)
                    .addArgs("-map", "[outv]")    // 映射处理后的视频流
                    .addArgs("-map", "0:a")       // 映射原视频的完整音频流
                    .videoCodec("libx264")
                    .audioCodec("aac")
                    .addArgs("-crf", "23")
                    .addArgs("-preset", "fast")
                    .addArgs("-pix_fmt", "yuv420p")
                    .addArgs("-avoid_negative_ts", "make_zero")
                    .addArgs("-async", "1")       // 音频同步
                    .addArgs("-vsync", "cfr")     // 视频同步
                    .overwrite()
                    .output(outPath)
                    .build();

            FFmpegExecutor.FFmpegResult result = FFmpegExecutor.executeCommand(args, outputFile);

            if (!result.isSuccess()) {
                throw new RuntimeException("替换视频片段失败: " + result.getOutput());
            }

            log.info("视频片段替换成功，原音频完整保留 >> 输出文件: {}", outPath);

        } catch (Exception e) {
            log.error("替换视频片段（保持音频）失败", e);
            throw new RuntimeException("替换视频片段（保持音频）失败", e);
        } finally {
            // 清理临时文件
            tempFiles.forEach(MediaResourceManager::cleanupTempFile);
        }
    }


    /**
     * 用图片替换视频中指定时间段的帧
     *
     * @param videoUrl    原始视频URL
     * @param imageUrl    替换图片路径
     * @param startTime   替换开始时间（毫秒）
     * @param endTime     替换结束时间（毫秒）
     * @param position    替换图片的位置信息（可选）
     * @param overlayMode 覆盖模式（透明背景或黑色背景）
     * @param outPath     输出文件路径
     */
    public static void replaceWithImage(String videoUrl, String imageUrl,
                                        long startTime, long endTime,
                                        Position position, OverlayMode overlayMode,
                                        String outPath) {

        double start = NumberUtil.div(startTime, 1000, 3);
        double end = NumberUtil.div(endTime, 1000, 3);
        log.info("用图片替换视频帧 >> 视频:{}, 图片:{}, 时间段:{}s-{}s, 位置信息:{}, 覆盖模式:{}",
                videoUrl, imageUrl, start, end, position, overlayMode);

        try {
            MediaResourceManager.ensureOutputDirectory(outPath);
            File outputFile = new File(outPath);

            // 构建复杂的视频滤镜
            StringBuilder filterBuilder = new StringBuilder();

            // 先创建一个全屏的背景（透明或黑色），覆盖指定的时间段，不依赖position参数
            filterBuilder.append(String.format(
                    "color=%s:size=1080x1920:d=%.3f:r=25[background];",
                    overlayMode.getColor(), end - start));

            // 对图片进行缩放处理
            if (position != null && position.getWidth() != null && position.getHeight() != null) {
                filterBuilder.append(String.format(
                        "[1:v]scale=%d:%d:force_original_aspect_ratio=decrease:force_divisible_by=2[scaled_image];",
                        position.getWidth(), position.getHeight()));
            } else {
                filterBuilder.append("[1:v]scale=1080:1920:force_original_aspect_ratio=decrease:force_divisible_by=2[scaled_image];");
            }
            // 在背景上叠加图片
            if (position != null && position.getLocationX() != null && position.getLocationY() != null) {
                // 使用指定的位置坐标
                filterBuilder.append(String.format(
                        "[background][scaled_image]overlay=%d:%d[replace_with_background];",
                        position.getLocationX(), position.getLocationY()));
            } else {
                // 使用默认的覆盖位置（全屏覆盖），坐标为(0,0)
                filterBuilder.append("[background][scaled_image]overlay=0:0[replace_with_background];");
            }
            // 在原视频的指定时间段覆盖处理后的内容
            filterBuilder.append(String.format(
                    "[0:v][replace_with_background]overlay=enable='between(t,%.3f,%.3f)'[v]",
                    start, end));

            String filter = filterBuilder.toString();

            String[] args = new FFmpegExecutor.FFmpegCommandBuilder()
                    .input(videoUrl)
                    .input(imageUrl)
                    .addArgs("-filter_complex", filter)
                    .addArgs("-map", "[v]")
                    .addArgs("-map", "0:a")  // 保持原音频
                    .videoCodec("libx264")
                    .audioCodec("copy")
                    .overwrite()
                    .output(outPath)
                    .build();

            FFmpegExecutor.FFmpegResult result = FFmpegExecutor.executeCommand(args, outputFile);

            if (!result.isSuccess()) {
                throw new RuntimeException("图片替换失败: " + result.getOutput());
            }

            log.info("图片替换成功 >> 输出文件:{}", outPath);

        } catch (Exception e) {
            log.error("图片替换视频帧失败", e);
            throw new RuntimeException("图片替换视频帧失败", e);
        }
    }


    /**
     * 删除视频中的音频轨道（静音处理）
     *
     * @param videoUrl 视频路径（支持本地文件和网络URL）
     * @param outPath  输出文件路径
     */
    public static void removeAudio(String videoUrl, String outPath) {
        log.info("删除视频音频 >> 输入文件:{}, 输出文件:{}", videoUrl, outPath);

        // 参数验证
        MediaResourceManager.validateParameters(videoUrl, outPath);

        try {
            MediaResourceManager.ensureOutputDirectory(outPath);
            File outputFile = new File(outPath);

            String[] args = new FFmpegExecutor.FFmpegCommandBuilder()
                    .input(videoUrl)
                    .addArgs("-c:v", "copy")  // 复制视频流，不重新编码
                    .addArgs("-an")           // 删除音频流
                    .overwrite()
                    .output(outPath)
                    .build();

            FFmpegExecutor.FFmpegResult result = FFmpegExecutor.executeCommand(args, outputFile);

            if (!result.isSuccess()) {
                throw new RuntimeException("删除音频失败: " + result.getOutput());
            }

            log.info("删除视频音频成功 >> 输出文件:{}", outPath);

        } catch (Exception e) {
            log.error("删除视频音频失败", e);
            throw new RuntimeException("删除视频音频失败", e);
        }
    }

    /**
     * 压缩视频文件
     *
     * @param inputUrl  输入视频URL或路径
     * @param outPath   输出文件路径
     * @param quality   视频质量 (18-28, 数值越小质量越高)
     * @param maxWidth  最大宽度 (0表示不调整)
     * @param maxHeight 最大高度 (0表示不调整)
     */
    public static void compressVideo(String inputUrl, String outPath, int quality, int maxWidth, int maxHeight) {
        log.info("压缩视频 >> 输入文件:{}, 输出文件:{}, 质量:{}, 最大尺寸:{}x{}",
                inputUrl, outPath, quality, maxWidth, maxHeight);

        // 参数验证
        MediaResourceManager.validateParameters(inputUrl, outPath);

        if (quality < 18 || quality > 28) {
            throw new IllegalArgumentException("质量参数应在18-28之间，数值越小质量越高");
        }

        try {
            MediaResourceManager.ensureOutputDirectory(outPath);
            File outputFile = new File(outPath);

            FFmpegExecutor.FFmpegCommandBuilder builder = new FFmpegExecutor.FFmpegCommandBuilder()
                    .input(inputUrl)
                    .videoCodec("libx264")           // 使用H.264编码
                    .addArgs("-crf", String.valueOf(quality))  // 设置质量
                    .addArgs("-preset", "medium")    // 编码速度/压缩率平衡
                    .audioCodec("aac")               // 音频编码
                    .addArgs("-ar", "44100")         // 音频采样率
                    .addArgs("-ac", "2")             // 立体声
                    .addArgs("-b:a", "128k")         // 音频比特率
                    .addArgs("-pix_fmt", "yuv420p")  // 像素格式
                    .overwrite();

            // 如果指定了最大尺寸，则添加缩放参数
            if (maxWidth > 0 || maxHeight > 0) {
                String scaleFilter;
                if (maxWidth > 0 && maxHeight > 0) {
                    // 同时限制宽高
                    scaleFilter = String.format("scale='min(%d,iw)':'min(%d,ih)':force_original_aspect_ratio=decrease", maxWidth, maxHeight);
                } else if (maxWidth > 0) {
                    // 只限制宽度
                    scaleFilter = String.format("scale=%d:-1:force_original_aspect_ratio=decrease", maxWidth);
                } else {
                    // 只限制高度
                    scaleFilter = String.format("scale=-1:%d:force_original_aspect_ratio=decrease", maxHeight);
                }
                builder.videoFilter(scaleFilter);
            }

            String[] args = builder.output(outPath).build();

            FFmpegExecutor.FFmpegResult result = FFmpegExecutor.executeCommand(args, outputFile);

            if (!result.isSuccess()) {
                throw new RuntimeException("视频压缩失败: " + result.getOutput());
            }

            log.info("视频压缩成功 >> 输入文件:{}, 输出文件:{}, 输出大小:{}KB",
                    inputUrl, outPath, outputFile.length() / 1024);

        } catch (Exception e) {
            log.error("视频压缩失败", e);
            throw new RuntimeException("视频压缩失败", e);
        }
    }


}