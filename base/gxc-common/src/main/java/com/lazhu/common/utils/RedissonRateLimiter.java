package com.lazhu.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RateType;
import org.redisson.api.RedissonClient;

import java.time.Duration;

/**
 * Redisson限流器服务类
 * 用于控制API调用频率，防止超过限制
 */
@Slf4j
public class RedissonRateLimiter {

    // 限流器相关常量
    private final RRateLimiter rRateLimiter;
    private final int rate;
    private final Duration rateInterval;

    /**
     * 使用自定义配置创建限流器
     *
     * @param redissonClient Redisson客户端实例
     * @param rateLimiterKey 限流器在Redis中的键名
     * @param rate           限流速率（单位时间内允许的请求数）
     * @param rateInterval   限流时间间隔
     */
    public RedissonRateLimiter(RedissonClient redissonClient, String rateLimiterKey,
                               int rate, Duration rateInterval) {
        this.rate = rate;
        this.rateInterval = rateInterval;

        // 初始化Redisson限流器
        this.rRateLimiter = redissonClient.getRateLimiter(rateLimiterKey);
        this.rRateLimiter.trySetRate(RateType.OVERALL, rate, rateInterval);
    }

    /**
     * 获取许可，阻塞直到获取成功
     *
     * @throws InterruptedException 如果线程在等待过程中被中断
     */
    public void acquire() throws InterruptedException {
        try {
            rRateLimiter.acquire();
        } catch (Exception e) {
            log.warn("Redisson限流器异常，使用备用限流策略", e);
            // 使用简单的延迟作为备用策略
            long delayMillis = calculateDelayMillis();
            Thread.sleep(delayMillis);
        }
    }

    /**
     * 尝试获取许可，不阻塞
     *
     * @param timeout 超时时间
     * @return true-获取成功，false-获取失败或超时
     */
    public boolean tryAcquire(Duration timeout) throws InterruptedException {
        try {
            return rRateLimiter.tryAcquire(timeout);
        } catch (Exception e) {
            log.warn("Redisson限流器异常，使用备用限流策略", e);
            // 使用简单的延迟作为备用策略
            long delayMillis = calculateDelayMillis();
            Thread.sleep(delayMillis);
            return true;
        }
    }

    /**
     * 计算基于配置的延迟时间（毫秒）
     *
     * @return 延迟时间（毫秒）
     */
    private long calculateDelayMillis() {
        return rateInterval.toMillis() / rate;
    }

    /**
     * 获取限流器配置信息
     *
     * @return 配置信息字符串
     */
    public String toString() {
        return String.format("Rate: %d per %s", rate, rateInterval.toString());
    }
}