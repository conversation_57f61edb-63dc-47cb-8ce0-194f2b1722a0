package com.lazhu.common.media;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 视频信息服务
 * 负责获取视频的各种信息，如时长、封面等
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
public class VideoInfoService {

    /**
     * 获取视频/音频时长（秒）
     *
     * @param url 视频URL或路径
     * @return 时长（秒），获取失败返回0
     */
    public static Long getDuration(String url) {
        log.info("获取视频/音频时长 >> url:{}", url);

        // 参数验证
        MediaResourceManager.validateParameters(url);

        // 方法1：尝试使用FFmpeg的信息获取方式
        Long duration = getDurationWithFFmpeg(url);
        if (duration > 0) {
            return duration;
        }

        // 方法2：如果方法1失败，尝试使用另一种方式
        duration = getDurationWithAlternativeMethod(url);
        if (duration > 0) {
            return duration;
        }

        log.error("获取视频/音频时长失败 >> url:{}", url);
        return 0L;
    }

    /**
     * 使用FFmpeg获取时长的第一种方法
     */
    private static Long getDurationWithFFmpeg(String url) {
        try {
            // 使用FFmpeg的-i参数获取信息，重定向到null避免输出文件错误
            String[] args = new FFmpegExecutor.FFmpegCommandBuilder()
                    .addArg("-i")
                    .addArg(url)
                    .addArgs("-f", "null")
                    .addArg("-")
                    .build();

            FFmpegExecutor.FFmpegResult result = FFmpegExecutor.executeCommand(args);

            // FFmpeg在获取信息时通常会返回非0退出码，但输出包含有用信息
            String output = result.getOutput();
            if (output != null && !output.trim().isEmpty()) {
                return parseDurationFromOutput(output);
            }
        } catch (Exception e) {
            log.debug("FFmpeg方法1获取时长失败", e);
        }
        return 0L;
    }

    /**
     * 使用FFmpeg获取时长的备用方法
     */
    private static Long getDurationWithAlternativeMethod(String url) {
        try {
            // 使用FFmpeg的另一种方式：尝试转码到null但获取信息
            String[] args = new FFmpegExecutor.FFmpegCommandBuilder()
                    .input(url)
                    .addArgs("-t", "0.1")  // 只处理0.1秒来快速获取信息
                    .addArgs("-f", "null")
                    .addArg("-")
                    .build();

            FFmpegExecutor.FFmpegResult result = FFmpegExecutor.executeCommand(args);

            String output = result.getOutput();
            if (output != null && !output.trim().isEmpty()) {
                return parseDurationFromOutput(output);
            }
        } catch (Exception e) {
            log.debug("FFmpeg方法2获取时长失败", e);
        }
        return 0L;
    }

    /**
     * 获取视频第一帧图片作为封面
     *
     * @param url 视频URL或路径
     * @return 封面图片文件，失败返回null
     */
    public static File getVideoCover(String url) {
        log.info("获取视频第一帧图片 >> url:{}", url);

        // 参数验证
        MediaResourceManager.validateParameters(url);

        try {
            // 创建输出文件
            String fileName = MediaPathUtils.getFileNameWithoutExtension(url);
            File outputFile = MediaResourceManager.createTempFile("video_cover_" + fileName, ".jpg");

            // 构建FFmpeg命令
            String[] args = new FFmpegExecutor.FFmpegCommandBuilder()
                    .input(url)
                    .seekStart("00:00:00")
                    .frames(1)
                    .addArgs("-q:v", "2")
                    .overwrite()
                    .output(outputFile.getAbsolutePath())
                    .build();

            // 执行命令
            FFmpegExecutor.FFmpegResult result = FFmpegExecutor.executeCommand(args, outputFile);

            if (result.isSuccess()) {
                log.info("获取视频封面成功 >> url:{}, 封面文件:{}", url, outputFile.getAbsolutePath());
                return outputFile;
            } else {
                log.error("获取视频第一帧图片失败 >> url:{}, 错误信息:{}", url, result.getOutput());
                MediaResourceManager.cleanupTempFile(outputFile);
                return null;
            }
        } catch (Exception e) {
            log.error("获取视频第一帧图片失败 >> url:{}", url, e);
            return null;
        }
    }

    /**
     * 获取视频指定时间点的截图
     *
     * @param url         视频URL或路径
     * @param timeSeconds 截图时间点（秒）
     * @return 截图文件，失败返回null
     */
    public static File getVideoScreenshot(String url, long timeSeconds) {
        log.info("获取视频截图 >> url:{}, time:{}s", url, timeSeconds);

        // 参数验证
        MediaResourceManager.validateParameters(url);
        if (timeSeconds < 0) {
            throw new IllegalArgumentException("时间点不能为负数");
        }

        try {
            // 创建输出文件
            String fileName = MediaPathUtils.getFileNameWithoutExtension(url);
            File outputFile = MediaResourceManager.createTempFile(
                    "screenshot_" + fileName + "_" + timeSeconds + "s", ".jpg");

            // 构建FFmpeg命令
            String[] args = new FFmpegExecutor.FFmpegCommandBuilder()
                    .input(url)
                    .seekStart(String.valueOf(timeSeconds))
                    .frames(1)
                    .addArgs("-q:v", "2")
                    .overwrite()
                    .output(outputFile.getAbsolutePath())
                    .build();

            // 执行命令
            FFmpegExecutor.FFmpegResult result = FFmpegExecutor.executeCommand(args, outputFile);

            if (result.isSuccess()) {
                log.info("获取视频截图成功 >> url:{}, 截图文件:{}", url, outputFile.getAbsolutePath());
                return outputFile;
            } else {
                log.error("获取视频截图失败 >> url:{}, 错误信息:{}", url, result.getOutput());
                MediaResourceManager.cleanupTempFile(outputFile);
                return null;
            }
        } catch (Exception e) {
            log.error("获取视频截图失败 >> url:{}", url, e);
            return null;
        }
    }


    /**
     * 从FFmpeg输出中解析时长
     */
    private static Long parseDurationFromOutput(String output) {
        try {
            String[] lines = output.split("\n");
            for (String line : lines) {
                if (line.contains("Duration")) {
                    String[] parts = line.split(",");
                    String durationPart = parts[0].replace("Duration:", "").trim();
                    String[] timeParts = durationPart.split(":");
                    long hours = Long.parseLong(timeParts[0]);
                    long minutes = Long.parseLong(timeParts[1]);
                    double secondsWithMillis = Double.parseDouble(timeParts[2]);
                    return (hours * 3600) + (minutes * 60) + (long) secondsWithMillis;
                }
            }
        } catch (Exception e) {
            log.error("解析时长失败", e);
        }
        return 0L;
    }


    /**
     * 使用ffmpeg命令获取视频分辨率
     *
     * @param videoPath 视频文件路径
     * @return 包含宽度和高度的数组，格式为 [width, height]
     */
    private static int[] getResolutionWithFFmpeg(String videoPath) {
        try {
            String[] args = new FFmpegExecutor.FFmpegCommandBuilder()
                    .input(videoPath)
                    .build();

            FFmpegExecutor.FFmpegResult fFmpegResult = FFmpegExecutor.executeCommand(args);
            String output = fFmpegResult.getOutput();
            if (output != null && !output.trim().isEmpty()) {
                // 查找分辨率信息的正则表达式
                Pattern pattern = Pattern.compile("(\\d{3,}x\\d{3,})");
                Matcher matcher = pattern.matcher(output);

                if (matcher.find()) {
                    String resolution = matcher.group(1);
                    String[] parts = resolution.split("x");
                    int width = Integer.parseInt(parts[0]);
                    int height = Integer.parseInt(parts[1]);
                    return new int[]{width, height};
                }
            }
        } catch (Exception e) {
            log.error("通过ffmpeg获取视频分辨率失败: {}", videoPath, e);
        }
        return null;
    }


    /**
     * 获取视频信息（包括分辨率、编码、时长）
     *
     * @param url 视频URL或路径
     * @return 视频信息对象
     */
    public static VideoInfo getVideoInfo(String url) {
        log.info("获取视频完整信息 >> url:{}", url);

        // 参数验证
        MediaResourceManager.validateParameters(url);

        // 判断是否为网络URL，只有网络文件才需要下载和清理
        boolean isRemoteUrl = url.startsWith("http://") || url.startsWith("https://");
        String videoPath = url;

        if (isRemoteUrl) {
            videoPath = MediaUrlProcessor.downloadToLocal(url);
            url = videoPath;
        }
        try {
            // 使用FFmpegExecutor执行ffprobe命令
            FFmpegExecutor.FFmpegResult result = FFmpegExecutor.executeFFprobe(videoPath);

            if (!result.isSuccess()) {
                log.error("ffprobe命令执行失败，退出码: {}", result.getExitCode());
                return null;
            }
            String jsonOutput = result.getOutput();
            log.info("ffprobe输出 >> {}", jsonOutput);

            // 简单的JSON解析，提取所需字段
            int width = 0, height = 0, duration = 0;
            String codec = "";

            // 解析宽度
            Pattern widthPattern = Pattern.compile("\"width\":\\s*(\\d+)");
            Matcher widthMatcher = widthPattern.matcher(jsonOutput);
            if (widthMatcher.find()) {
                width = Integer.parseInt(widthMatcher.group(1));
            }

            // 解析高度
            Pattern heightPattern = Pattern.compile("\"height\":\\s*(\\d+)");
            Matcher heightMatcher = heightPattern.matcher(jsonOutput);
            if (heightMatcher.find()) {
                height = Integer.parseInt(heightMatcher.group(1));
            }

            // 解析时长
            Pattern durationPattern = Pattern.compile("\"duration\":\\s*\"([\\d.]+)\"");
            Matcher durationMatcher = durationPattern.matcher(jsonOutput);
            if (durationMatcher.find()) {
                duration = (int) Math.round(Double.parseDouble(durationMatcher.group(1)));
            }

            // 解析编码
            Pattern codecPattern = Pattern.compile("\"codec_name\":\\s*\"([^\"]+)\"");
            Matcher codecMatcher = codecPattern.matcher(jsonOutput);
            if (codecMatcher.find()) {
                codec = codecMatcher.group(1);
            }

            if (width > 0 && height > 0) {
                log.info("ffprobe获取视频信息成功 >> 分辨率:{}x{}, 编码:{}, 时长:{}s", width, height, codec, duration);
                return new VideoInfo(width, height, codec, duration);
            } else {
                log.warn("ffprobe未能获取到完整的视频信息");
                return null;
            }

        } catch (Exception e) {
            log.error("使用ffprobe获取视频信息失败: {}", videoPath, e);
            return null;
        }  finally {
            // 只清理下载的临时文件，不清理原始本地文件
            if (isRemoteUrl &&  videoPath!= null) {
                MediaResourceManager.cleanupTempFile(new File(videoPath));
            }
        }
    }

}
