package com.lazhu.common.media;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.util.StreamUtils;

import java.io.*;
import java.nio.ByteBuffer;
import java.nio.charset.CharacterCodingException;
import java.nio.charset.Charset;
import java.nio.charset.CharsetDecoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;

/**
 * 字幕服务
 * 负责字幕的添加、合成等操作
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
public class SubtitleService {

    /**
     * 添加字幕到视频（硬字幕烧录）
     *
     * @param videoUrl     视频路径
     * @param subtitlePath 字幕文件路径
     * @param outPath      输出文件路径
     */
    public static void addSubtitle(String videoUrl, String subtitlePath, String outPath) {
        addSubtitle(videoUrl, subtitlePath, outPath, null, null);
    }

    /**
     * 添加字幕到视频（硬字幕烧录），可指定资源字体目录（resources/fonts）和字幕编码（charenc）
     */
    public static void addSubtitle(String videoUrl, String subtitlePath, String outPath, String resourceFontsDir, String charEnc) {
        log.info("添加字幕到视频 >> 视频路径:{}, 字幕文件:{}, 使用编码:{}", videoUrl, subtitlePath, charEnc);

        // 参数验证
        MediaResourceManager.validateParameters(videoUrl, subtitlePath, outPath);
        MediaResourceManager.validateFileExists(subtitlePath, "字幕文件不存在");
        //如果是网络文件,先下载文件到本地
        if (MediaPathUtils.isNetworkUrl(videoUrl)) {
            File tempFile = MediaResourceManager.createTempFile("video_" + FileUtil.getPrefix(videoUrl), ".mp4");
            log.info("下载视频文件 {} >> 临时文件:{}", videoUrl, tempFile.getAbsolutePath());
            HttpUtil.downloadFile(videoUrl, tempFile);
            videoUrl = tempFile.getAbsolutePath();
        }
        try {
            VideoInfo videoInfo = MediaUtil.getVideoInfo(videoUrl);
            String s = FileUtil.readString(subtitlePath, StandardCharsets.UTF_8);
            List<String> lines = new ArrayList<>();
            for (String line : s.split("\n")) {
                // 如果包含中文字符，则处理
                if (line.matches(".*[\u4e00-\u9fa5]+.*")) {
                    line = wrapTextByWidth(line, videoInfo.getWidth());
                }
                lines.add(line);
            }
            FileUtil.writeUtf8String(StrUtil.join("\n", lines), subtitlePath);
            log.info("字幕处理完成，：{}", StrUtil.join("\n", lines));
            // 处理字幕路径
            String processedSubtitlePath = MediaPathUtils.processPathForFFmpeg(subtitlePath);
//            String processedSubtitlePath = convertSubtitleToUTF8(subtitlePath);
            MediaResourceManager.ensureOutputDirectory(outPath);
            File outputFile = new File(outPath);

            // 处理资源字体目录：默认从 classpath:fonts/ 提取到临时目录（兼容 fat JAR）
            String fontsDirOnDisk = extractFontsFromResources(resourceFontsDir);

            StringBuilder filterBuilder = new StringBuilder();

            filterBuilder.append("subtitles=").append(processedSubtitlePath);

            if (StrUtil.isNotBlank(fontsDirOnDisk)) {
                String processedFontsDir = MediaPathUtils.processPathForFFmpeg(fontsDirOnDisk);
                filterBuilder.append(":fontsdir=").append(processedFontsDir);
            }
            if (StrUtil.isNotBlank(charEnc)) {
                filterBuilder.append(":charenc=").append(charEnc);
            }
            // 使用应用内置的字体，确保跨平台一致性，并设置边距和自动换行
            filterBuilder.append(":force_style='FontName=NotoSansCJKsc-Regular,FontSize=14,Bold=0,Italic=0,Underline=0,Alignment=2,MarginL=20,MarginR=20,MarginV=20,WrapStyle=0'");


            String subtitleFilter = filterBuilder.toString();

            // 构建FFmpeg命令
            FFmpegExecutor.FFmpegCommandBuilder commandBuilder = new FFmpegExecutor.FFmpegCommandBuilder()
                    .overwrite()
                    .addArgs("-itsoffset", "0.5")
                    .input(videoUrl)
                    .videoFilter(subtitleFilter)
                    .output(outPath);


            // Linux系统性能优化
            if (MediaPathUtils.isLinux()) {
                // 添加线程优化参数
                commandBuilder.addArgs("-threads", "0") // 自动选择线程数
                        // 使用更快的编码预设
                        .addArgs("-c:v", "libx264")
                        .addArgs("-preset", "fast") // 使用快速编码预设
                        .addArgs("-crf", "23")
                        .addArgs("-c:a", "copy"); // 直接复制音频流，不重新编码
            }

            String[] args = commandBuilder.build();

            // 执行命令
            FFmpegExecutor.FFmpegResult result = FFmpegExecutor.executeCommand(args, outputFile);

            if (result.isSuccess()) {
                log.info("添加字幕成功 >> 视频路径:{}, 输出文件:{}, 大小:{}KB",
                        videoUrl, outPath, outputFile.length() / 1024);
            } else {
                log.error("添加字幕失败 >> 视频路径:{}, 字幕文件:{}, 错误信息:{}",
                        videoUrl, subtitlePath, result.getOutput());
                throw new RuntimeException("添加字幕失败");
            }
        } catch (Exception e) {
            log.error("添加字幕异常 >> 视频路径:{}, 字幕文件:{}", videoUrl, subtitlePath, e);
            throw new RuntimeException("添加字幕异常", e);
        }
    }

    // 进程级缓存的字体临时目录（避免重复复制）
    private static volatile String CACHED_FONTS_DIR = null;

    private static String extractFontsFromResources(String resourceFontsDir) {
        String baseDir = StrUtil.isBlank(resourceFontsDir) ? "fonts" : resourceFontsDir;
        // 命中缓存直接返回
        if (StrUtil.isNotBlank(CACHED_FONTS_DIR) && FileUtil.isDirectory(CACHED_FONTS_DIR)) {
            return CACHED_FONTS_DIR;
        }
        try {
            PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            Resource[] resources = resolver.getResources("classpath*:" + baseDir + "/**/*");

            if (resources.length == 0) {
                log.info("未在资源目录发现字体文件: {}", baseDir);
                return null;
            }

            File targetDir = new File(System.getProperty("java.io.tmpdir"), "app_fonts_" + System.currentTimeMillis());
            if (!targetDir.exists() && !targetDir.mkdirs()) {
                throw new IOException("无法创建字体临时目录: " + targetDir.getAbsolutePath());
            }

            int copied = 0;
            for (Resource res : resources) {
                if (res.isReadable()) {
                    String filename = res.getFilename();
                    if (StrUtil.isBlank(filename)) continue;
                    String lower = filename.toLowerCase();
                    if (!(lower.endsWith(".ttf") || lower.endsWith(".ttc") || lower.endsWith(".otf"))) {
                        continue;
                    }
                    File outFile = new File(targetDir, filename);
                    try (InputStream in = res.getInputStream(); OutputStream out = new FileOutputStream(outFile)) {
                        StreamUtils.copy(in, out);
                        copied++;
                    }
                }
            }
            if (copied == 0) {
                log.info("资源目录存在但未发现字体文件: {}", baseDir);
                return null;
            }
            CACHED_FONTS_DIR = targetDir.getAbsolutePath();
            log.info("已将字体复制到临时目录(已缓存): {} (共{}个)", CACHED_FONTS_DIR, copied);
            return CACHED_FONTS_DIR;
        } catch (Exception e) {
            log.debug("提取资源字体失败: {}", baseDir, e);
        }
        return null;
    }

    /**
     * 合成ASS字幕到视频（硬字幕烧录）
     *
     * @param videoUrl 视频路径（支持本地文件和网络URL）
     * @param assPath  ASS字幕文件路径
     * @param outPath  输出文件路径
     * @param fontsDir 字体目录路径（可选，为null时使用系统默认字体目录）
     * @param quality  视频质量（CRF值，18-28，数值越小质量越高，可选，默认23）
     */
    public static void mergeAssSubtitle(String videoUrl, String assPath, String outPath,
                                        String fontsDir, Integer quality, String charEnc) {
        log.info("合成ASS字幕到视频 >> 视频路径:{}, ASS字幕文件:{}, 输出路径:{}",
                videoUrl, assPath, outPath);

        // 参数验证
        MediaResourceManager.validateParameters(videoUrl, assPath, outPath);
        MediaResourceManager.validateFileExists(assPath, "ASS字幕文件不存在");

        try {
            MediaResourceManager.ensureOutputDirectory(outPath);
            File outputFile = new File(outPath);

            // 若未显式提供字体目录，从资源中提取并缓存至临时目录（与 addSubtitle 一致）
            if (StrUtil.isBlank(fontsDir)) {
                fontsDir = extractFontsFromResources(null);
            }
            // 构建视频滤镜参数
            String filter = buildAssFilter(assPath, fontsDir, charEnc);

            // 设置默认质量
            if (quality == null || quality < 0 || quality > 51) {
                quality = 23;
            }

            // 构建FFmpeg命令
            String[] args = new FFmpegExecutor.FFmpegCommandBuilder()
                    .input(videoUrl)
                    .videoFilter(filter)
                    .videoCodec("libx264")
                    .quality(quality)
                    .audioCodec("copy")
                    .overwrite()
                    .output(outPath)
                    .build();

            // 执行命令
            FFmpegExecutor.FFmpegResult result = FFmpegExecutor.executeCommand(args, outputFile);

            if (result.isSuccess()) {
                log.info("合成ASS字幕成功 >> 视频路径:{}, 输出文件:{}, 文件大小:{}KB",
                        videoUrl, outPath, outputFile.length() / 1024);
            } else {
                log.error("合成ASS字幕失败 >> 视频路径:{}, ASS字幕文件:{}, 错误信息:{}",
                        videoUrl, assPath, result.getOutput());
                throw new RuntimeException("合成ASS字幕失败，退出码: " + result.getExitCode());
            }
        } catch (Exception e) {
            log.error("合成ASS字幕异常 >> 视频路径:{}, ASS字幕文件:{}", videoUrl, assPath, e);
            throw new RuntimeException("合成ASS字幕异常", e);
        }
    }

    /**
     * 合成ASS字幕到视频（使用默认参数）
     */
    public static void mergeAssSubtitle(String videoUrl, String assPath, String outPath) {
        mergeAssSubtitle(videoUrl, assPath, outPath, null, null, null);
    }


    /**
     * 构建ASS滤镜参数
     */
    private static String buildAssFilter(String assPath, String fontsDir, String charEnc) {
        String processedAssPath = MediaPathUtils.processPathForFFmpeg(assPath);

        StringBuilder filterBuilder = new StringBuilder();
        filterBuilder.append("ass=").append(processedAssPath);

        if (StrUtil.isNotBlank(fontsDir)) {
            String processedFontsDir = MediaPathUtils.processPathForFFmpeg(fontsDir);
            filterBuilder.append(":fontsdir=").append(processedFontsDir);
        }
        if (StrUtil.isNotBlank(charEnc)) {
            filterBuilder.append(":charenc=").append(charEnc);
        }
        String filter = filterBuilder.toString();
        log.debug("构建的ASS滤镜参数: {}", filter);
        return filter;
    }

    /**
     * 检测并转换字幕文件编码为UTF-8
     *
     * @param subtitlePath 原始字幕文件路径
     * @return UTF-8编码的字幕文件路径
     */
    private static String convertSubtitleToUTF8(String subtitlePath) throws IOException {
        File subtitleFile = new File(subtitlePath);
        if (!subtitleFile.exists()) {
            throw new FileNotFoundException("字幕文件不存在: " + subtitlePath);
        }

        // 读取字幕文件内容
        byte[] contentBytes = Files.readAllBytes(subtitleFile.toPath());

        // 检测编码
        Charset detectedCharset = detectCharset(contentBytes);
        log.info("检测到字幕文件 {} 的编码为: {}", subtitlePath, detectedCharset);

        // 如果已经是UTF-8编码，则直接返回原路径
        if (StandardCharsets.UTF_8.equals(detectedCharset)) {
            return MediaPathUtils.processPathForFFmpeg(subtitlePath);
        }

        // 转换为UTF-8编码
        String content = new String(contentBytes, detectedCharset);

        // 创建临时文件保存UTF-8编码的字幕
        File tempSubtitleFile = MediaResourceManager.createTempFile(
                "subtitle_utf8_" + FileUtil.getPrefix(subtitlePath),
                "." + FileUtil.getSuffix(subtitlePath));

        // 写入UTF-8编码的内容
        Files.write(tempSubtitleFile.toPath(), content.getBytes(StandardCharsets.UTF_8));
        log.info("已将字幕文件从 {} 转换为UTF-8编码，保存到: {}", detectedCharset, tempSubtitleFile.getAbsolutePath());

        return MediaPathUtils.processPathForFFmpeg(tempSubtitleFile.getAbsolutePath());
    }


    /**
     * 改进的字符编码检测方法，支持中文编码，特别优化Linux环境
     *
     * @param data 字节数据
     * @return 检测到的字符编码
     */
    private static Charset detectCharset(byte[] data) {
        // 检查BOM
        if (data.length >= 3 && data[0] == (byte) 0xEF && data[1] == (byte) 0xBB && data[2] == (byte) 0xBF) {
            return StandardCharsets.UTF_8;
        }

        if (data.length >= 2 && data[0] == (byte) 0xFF && data[1] == (byte) 0xFE) {
            return StandardCharsets.UTF_16LE;
        }

        if (data.length >= 2 && data[0] == (byte) 0xFE && data[1] == (byte) 0xFF) {
            return StandardCharsets.UTF_16BE;
        }

        // 检查是否为有效的UTF-8
        if (isValidUTF8(data)) {
            return StandardCharsets.UTF_8;
        }

        // 尝试检测GBK/GB2312编码（常见的中文编码）
        if (isValidGBK(data)) {
            try {
                return Charset.forName("GBK");
            } catch (Exception e) {
                log.warn("无法获取GBK编码，使用UTF-8");
            }
        }

        // 尝试检测GB2312编码
        if (isValidGB2312(data)) {
            try {
                return Charset.forName("GB2312");
            } catch (Exception e) {
                log.warn("无法获取GB2312编码，使用UTF-8");
            }
        }

        // 针对Linux系统的编码检测优化
        String osName = System.getProperty("os.name").toLowerCase();
        if (osName.contains("linux")) {
            // Linux系统优先尝试UTF-8，然后尝试系统默认编码
            try {
                // 检查系统默认编码
                String systemEncoding = System.getProperty("file.encoding");
                if (systemEncoding != null && !systemEncoding.equalsIgnoreCase("UTF-8")) {
                    log.info("Linux系统检测到默认编码: {}", systemEncoding);
                    return Charset.forName(systemEncoding);
                }

                // 尝试检测LANG环境变量
                String lang = System.getenv("LANG");
                if (lang != null && lang.contains("zh")) {
                    log.info("Linux系统检测到中文环境: {}", lang);
                    // 中文环境通常使用UTF-8，但某些系统可能使用GBK
                    if (lang.contains("GBK") || lang.contains("GB2312")) {
                        try {
                            return Charset.forName("GBK");
                        } catch (Exception e) {
                            log.warn("无法获取GBK编码，使用UTF-8");
                        }
                    }
                }
            } catch (Exception e) {
                log.warn("Linux系统编码检测失败，使用UTF-8", e);
            }
        } else if (osName.contains("win")) {
            // Windows系统通常使用GBK
            try {
                return Charset.forName("GBK");
            } catch (Exception e) {
                log.warn("无法获取GBK编码，使用UTF-8");
            }
        }

        return StandardCharsets.UTF_8;
    }

    /**
     * 简单检查是否为有效的UTF-8编码
     *
     * @param data 字节数据
     * @return 是否为有效的UTF-8
     */
    private static boolean isValidUTF8(byte[] data) {
        try {
            CharsetDecoder decoder = StandardCharsets.UTF_8.newDecoder();
            decoder.decode(ByteBuffer.wrap(data));
            return true;
        } catch (CharacterCodingException e) {
            return false;
        }
    }

    /**
     * 检查是否为有效的GBK编码
     *
     * @param data 字节数据
     * @return 是否为有效的GBK
     */
    private static boolean isValidGBK(byte[] data) {
        try {
            CharsetDecoder decoder = Charset.forName("GBK").newDecoder();
            decoder.decode(ByteBuffer.wrap(data));
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查是否为有效的GB2312编码
     *
     * @param data 字节数据
     * @return 是否为有效的GB2312
     */
    private static boolean isValidGB2312(byte[] data) {
        try {
            CharsetDecoder decoder = Charset.forName("GB2312").newDecoder();
            decoder.decode(ByteBuffer.wrap(data));
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 查找Linux系统字体目录
     *
     * @return 字体目录路径，如果未找到则返回null
     */
    private static String findLinuxSystemFonts() {
        // 常见的Linux字体目录
        String[] commonFontDirs = {
                "/usr/share/fonts",
                "/usr/local/share/fonts",
                "/usr/share/fonts/truetype",
                "/usr/share/fonts/opentype",
                "/usr/share/fonts/TTF",
                "/usr/share/fonts/OTF",
                "/usr/share/fonts/zh_CN",
                "/usr/share/fonts/zh_TW",
                "/usr/share/fonts/chinese",
                "/usr/share/fonts/noto",
                "/usr/share/fonts/dejavu",
                "/usr/share/fonts/liberation",
                "/usr/share/fonts/ubuntu",
                "/usr/share/fonts/centos",
                "/usr/share/fonts/redhat"
        };

        for (String fontDir : commonFontDirs) {
            File dir = new File(fontDir);
            if (dir.exists() && dir.isDirectory()) {
                // 检查目录中是否有字体文件
                File[] files = dir.listFiles((file, name) -> {
                    String lower = name.toLowerCase();
                    return lower.endsWith(".ttf") || lower.endsWith(".ttc") || lower.endsWith(".otf");
                });

                if (files != null && files.length > 0) {
                    log.info("找到Linux系统字体目录: {} (包含{}个字体文件)", fontDir, files.length);
                    return fontDir;
                }
            }
        }

        // 尝试使用fc-list命令查找字体目录
        try {
            Process process = Runtime.getRuntime().exec("fc-list --format='%{file}\\n' | head -1");
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String firstFont = reader.readLine();
                if (firstFont != null && !firstFont.trim().isEmpty()) {
                    File fontFile = new File(firstFont);
                    String fontDir = fontFile.getParent();
                    if (fontDir != null) {
                        log.info("通过fc-list找到字体目录: {}", fontDir);
                        return fontDir;
                    }
                }
            }
            process.waitFor();
        } catch (Exception e) {
            log.debug("fc-list命令执行失败，尝试其他方法", e);
        }

        log.info("未找到Linux系统字体目录，将使用资源字体");
        return null;
    }

    /**
     * 根据视频宽度和期望每行字符数，将文本按宽度换行
     *
     * @param text       待处理的文本
     * @param videoWidth 视频宽度（像素）
     * @return 换行后的文本
     */
    private static String wrapTextByWidth(String text, int videoWidth) {
        if (StrUtil.isBlank(text)) {
            return text;
        }
        // 以此为基准计算其他分辨率下每行应放置的字符数
        // fontSize=14 时 1080像素宽度视频一行最多放17个中文字符,
        int charLength = 1080 / 13;
        // 每行最大字数
        int expectedCharsPerLine = videoWidth / charLength;
        if (expectedCharsPerLine >= text.length()) {
            return text;
        }
        String[] split = StrUtil.split(text, expectedCharsPerLine);
        return ArrayUtil.join(split, "\n");
    }
}
