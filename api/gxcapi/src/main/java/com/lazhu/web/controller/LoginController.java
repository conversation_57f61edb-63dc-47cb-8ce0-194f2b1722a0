package com.lazhu.web.controller;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import com.lazhu.business.user.entity.User;
import com.lazhu.business.user.service.UserService;
import com.lazhu.common.Const;
import com.lazhu.common.LzSmsConst;
import com.lazhu.common.RedisKeyConst;
import com.lazhu.common.exception.BusinessException;
import com.lazhu.filter.TokenProp;
import com.lazhu.support.base.AbstractController;
import com.lazhu.support.base.BaseToken;
import com.lazhu.support.config.SpringContext;
import com.lazhu.support.response.SimpleResponse;
import com.lazhu.support.response.SimpleResponseBuilder;
import com.lazhu.support.util.JwtTokenUtil;
import com.lazhu.support.util.WebUtil;
import com.lazhu.utils.IpUtil;
import com.lazhu.utils.LzAliSmsTool;
import com.lazhu.web.dto.LoginParam;
import com.wf.captcha.ArithmeticCaptcha;
import com.wf.captcha.SpecCaptcha;
import com.wf.captcha.utils.CaptchaUtil;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

import static com.lazhu.common.Const.SMS_MAX_SEND_TIMES;

/**
 * 登录
 */
@RequestMapping("/api")
@RestController
public class LoginController extends AbstractController {

    @Autowired
    private UserService userService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private LzAliSmsTool lzAliSmsTool;

    @Autowired
    private TokenProp tokenProp;

    /**
     * 登录
     */
    @PostMapping("/login")
    public SimpleResponse<LoginParam.LoginResp> login(@RequestBody LoginParam.LoginReq param) {

        //校验验证码是否正确
        RBucket<Object> bucket = redissonClient.getBucket(RedisKeyConst.SMS_CODE + param.mobile());
        if (!bucket.isExists() || ObjectUtil.notEqual(param.verifyCode(), bucket.get())) {
            return new SimpleResponseBuilder<LoginParam.LoginResp>().error().setMsg("验证码错误").bulider();
        }
        //验证成功清除验证码
        bucket.delete();

        User user = userService.queryByMobile(param.mobile());
        if (user != null && ObjectUtil.equal(user.getUserStatus(), "0")) {
            //账户被禁用
            return new SimpleResponseBuilder<LoginParam.LoginResp>().error().setMsg("账户异常").bulider();
        }
        if (user == null) {
            //用户不存在，创建用户
//            user = userService.createUser(param.mobile());
            throw new BusinessException("账号不存在");
        }
        //生成token
        BaseToken bt = new BaseToken();
        bt.setUserId(user.getId().toString());
        bt.setFirstTime(System.currentTimeMillis());
        bt.setExpireTime(System.currentTimeMillis() + tokenProp.getExpireTime() * 1000);
        String token = JwtTokenUtil.createToken(bt, tokenProp.getSecret());
        LoginParam.LoginResp resp = LoginParam.LoginResp.of(user);
        return new SimpleResponseBuilder<>(resp).success().token(token).bulider();
    }

    /**
     * 发送短信验证码
     */
    @GetMapping("/sendSms")
    public SimpleResponse<String> sendSms(HttpServletRequest request, String mobile,String captcha) {

        // 校验图形验证码是否正确
        String codeId = Convert.toStr(WebUtil.getAttr("codeId"));
        if (StringUtils.isAnyBlank(captcha, codeId)) {
            return new SimpleResponseBuilder<String>().error().setMsg("请输入正确的图形验证码").bulider();
        }
        RBucket<String> captchaBucket = redissonClient.getBucket(RedisKeyConst.IMAGE_CODE + codeId);
        try {
            if (StringUtils.isEmpty(captchaBucket.get()) || !StringUtils.equals(captcha, captchaBucket.get())) {
                return new SimpleResponseBuilder<String>().error().setMsg("请输入正确的图形验证码").bulider();
            }
            //验证成功清除图形验证码
            captchaBucket.delete();
            //判断环境
            String sysEnvScience = SpringContext.getSysEnvScience();
            String code;
            if (!"released".equalsIgnoreCase(sysEnvScience)) {
                //非正式环境
                code = Const.DEV_SMS_CODE;
            } else {
                //正式环境
                //验证发送次数是否超过限制
                RBucket<Integer> bucket = redissonClient.getBucket(RedisKeyConst.SMS_SEND_COUNT + mobile);
                if (bucket.isExists() && bucket.get() != null && bucket.get() > SMS_MAX_SEND_TIMES) {
                    return new SimpleResponseBuilder<String>().error().setMsg("发送次数超过限制").bulider();
                }
                String ip = IpUtil.getIpAddr(request);
                RBucket<Integer> ipBucket = redissonClient.getBucket(RedisKeyConst.SMS_SEND_COUNT_IP + ip);
                if (ipBucket.isExists() && ipBucket.get() != null && ipBucket.get() > SMS_MAX_SEND_TIMES) {
                    return new SimpleResponseBuilder<String>().error().setMsg("发送次数超过限制").bulider();
                }
                //生成6位随机数
                code = RandomUtil.randomNumbers(6);
                //发送短信
                lzAliSmsTool.sendCodeSms(LzSmsConst.lzkj, mobile, code);
                //保存发送次数
                bucket.set(bucket.isExists() ? Convert.toInt(bucket.get(), 0) + 1 : 1, Duration.ofDays(1));
                ipBucket.set(ipBucket.isExists() ? Convert.toInt(ipBucket.get(), 0) : 1, Duration.ofDays(1));
            }
            //存入缓存 (5分钟有效)
            redissonClient.getBucket(RedisKeyConst.SMS_CODE + mobile).set(code, Duration.ofMinutes(5));
            return new SimpleResponseBuilder<>("").success().bulider();
        }finally {
            // 清除图形验证码
            captchaBucket.delete();
        }
    }

    /**
     * 退出登录
     */
    @GetMapping("/logout")
    public SimpleResponse<String> logout(@RequestHeader("token") String token) {
        // 解析token获取用户信息
        BaseToken baseToken = JwtTokenUtil.getToken(token, tokenProp.getSecret(), BaseToken.class);
        if (baseToken == null) {
            return new SimpleResponseBuilder<>("").success().bulider();
        }
        long expireTime = baseToken.getExpireTime() - System.currentTimeMillis();
        if(expireTime > 0){
            // 通过将token加入缓存实现过期效果
            RSet<Object> set = redissonClient.getSet(RedisKeyConst.INVALID_TOKEN);
            set.add(token);
            set.expire(Duration.ofMillis(expireTime));
        }
        return new SimpleResponseBuilder<>("").success().bulider();
    }

    @GetMapping("/captcha")
    public void captcha( HttpServletResponse response) throws Exception {
        ArithmeticCaptcha captcha;
        String code;
        do {
            captcha  = new ArithmeticCaptcha(130, 48);
            code = captcha.text().toLowerCase();
            // 尝试将结果解析为数字并检查是否为负数
            try {
                if (Integer.parseInt(code) >= 0) {
                    break; // 结果非负，跳出循环
                }
            } catch (NumberFormatException e) {
                // 解析失败，可能不是纯数字，根据需要处理，这里选择跳出
                break;
            }
        } while (true);
        String codeId = IdUtil.fastUUID();
        WebUtil.setAttr("codeId", codeId);
        RBucket<String> bucket = redissonClient.getBucket(RedisKeyConst.IMAGE_CODE + codeId);
        if (org.apache.commons.lang3.StringUtils.isEmpty(bucket.get())) {
            bucket.set(code, 3, TimeUnit.MINUTES);
        }
        bucket.expire(RedisKeyConst.IMAGE_CODE_EXPIRES, TimeUnit.SECONDS);
        ServletOutputStream outputStream = response.getOutputStream();
        captcha.out(outputStream);
    }

}
