package com.lazhu.web.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lazhu.business.publishlog.entity.PublishLog;
import com.lazhu.business.publishlog.entity.PublishLogQuery;
import com.lazhu.business.publishlog.service.PublishLogService;
import com.lazhu.business.publishlog.vo.PublishLogVO;
import com.lazhu.business.userchannelaccount.entity.UserChannelAccountQuery;
import com.lazhu.business.userchannelaccount.vo.UserChannelAccountVO;
import com.lazhu.support.base.AbstractController;
import com.lazhu.support.response.PageResponse;
import com.lazhu.support.response.PageResponseBuilder;
import com.lazhu.utils.WebTool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;


/**
 * 发布记录接口
 */
@RestController
@RequestMapping(value = "/api/pubLog")
public class PublishLogController extends AbstractController {

    @Autowired
    private PublishLogService publishLogService;


    /**
     * 发布记录列表
     */
    @PostMapping("/page")
    public Mono<PageResponse<PublishLogVO>> readPage(@RequestBody PublishLogQuery param) {
        param.setUserId(WebTool.getUserId());
        return Mono.fromCallable(() -> {
            Page<PublishLogVO> page = publishLogService.readPage(param);
            return new PageResponseBuilder<>(page).success().builder();
        });
    }
}
