package com.lazhu.web.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lazhu.web.dto.MediaAssertsDTO;
import com.lazhu.web.dto.MediaAssertsQuery;
import com.lazhu.web.dto.MediaAssetsCount;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 素材服务类
 */
@Repository
public interface ApiMediaAssertsMapper {


    /**
     * 查询图片素材
     */
    List<MediaAssertsDTO> queryImgAssertsList(Page<MediaAssertsDTO> page, @Param("params") MediaAssertsQuery query);

    /**
     * 查询视频素材
     */
    List<MediaAssertsDTO> queryVideoAssertsList(Page<MediaAssertsDTO> page,@Param("params")  MediaAssertsQuery query);

    /**
     * 查询音频素材
     */
    List<MediaAssertsDTO> queryVoiceAssertsList(Page<MediaAssertsDTO> page, @Param("params")  MediaAssertsQuery query);

    /**
     * 查询用户素材
     */
    List<MediaAssertsDTO> queryUserAssertsList(Page<MediaAssertsDTO> page, @Param("params")  MediaAssertsQuery query);

    List<MediaAssertsDTO> queryUserAssertsList(@Param("params")MediaAssertsQuery query);

    List<MediaAssetsCount> getMediaAssetsCount(@Param("actorIds") List<Long> actorIds);
}
