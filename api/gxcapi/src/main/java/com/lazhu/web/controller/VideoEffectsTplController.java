package com.lazhu.web.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lazhu.business.videoeffectstpl.dto.ProcessConfigDTO;
import com.lazhu.business.videoeffectstpl.entity.VideoEffectsTpl;
import com.lazhu.business.videoeffectstpl.entity.VideoEffectsTplQuery;
import com.lazhu.business.videoeffectstpl.service.VideoEffectsTplService;
import com.lazhu.common.media.MediaUtil;
import com.lazhu.common.utils.OssUtil;
import com.lazhu.support.base.AbstractController;
import com.lazhu.support.base.BaseController;
import com.lazhu.support.response.PageResponse;
import com.lazhu.support.response.PageResponseBuilder;
import com.lazhu.support.response.SimpleResponse;
import com.lazhu.support.response.SimpleResponseBuilder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.util.Map;

/**
 * 剪辑模板
 * <AUTHOR>
 * @since 2025-08-07
 */
@RestController
@RequestMapping(value = "/api/videoEffectsTpl")
public class VideoEffectsTplController extends AbstractController {

	@Autowired
	private VideoEffectsTplService videoEffectsTplService;
	
	@PostMapping(value = "/read/list")
	public PageResponse<VideoEffectsTpl> query(@RequestBody VideoEffectsTplQuery param) {
		Map<String, Object> params = BeanUtil.beanToMap(param);
		Page<VideoEffectsTpl> list = videoEffectsTplService.queryPage(params);
		return new PageResponseBuilder<>(list).success().builder();
	}

	@GetMapping(value = "/read/detail")
	public SimpleResponse<VideoEffectsTpl> get(Long id) {
		Assert.notNull(id, "ID");
		VideoEffectsTpl videoEffectsTpl = videoEffectsTplService.queryById(id);
		if(videoEffectsTpl!=null && StringUtils.isNotBlank(videoEffectsTpl.getProcessConfig())){
			videoEffectsTpl.setProcessConfigList(JSONArray.parseArray(videoEffectsTpl.getProcessConfig(), ProcessConfigDTO.class));
		}
		return new SimpleResponseBuilder<>(videoEffectsTpl).success().bulider();
	}
}
