package com.lazhu.web.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lazhu.baseai.llm.combine.VideoCombineResult;
import com.lazhu.baseai.llm.dto.LLMBaseRequest;
import com.lazhu.baseai.llm.dto.LLMBaseResponse;
import com.lazhu.baseai.llm.dto.TextCreationReq;
import com.lazhu.baseai.llm.dto.TextCreationResult;
import com.lazhu.baseai.tool.audio.CosyVoiceTool;
import com.lazhu.baseai.tool.audio.TxtToAudioService;
import com.lazhu.business.actor.entity.Actor;
import com.lazhu.business.actor.service.ActorService;
import com.lazhu.business.content.dto.ContentAudioInfo;
import com.lazhu.business.content.dto.ContentCreationReq;
import com.lazhu.business.content.dto.ContentDTO;
import com.lazhu.business.content.entity.Content;
import com.lazhu.business.content.entity.ContentQuery;
import com.lazhu.business.content.service.ContentService;
import com.lazhu.business.llmconfig.service.LLMService;
import com.lazhu.business.tasks.entity.Tasks;
import com.lazhu.business.tasks.entity.TasksQuery;
import com.lazhu.business.tasks.entity.VideoSynthTaskResult;
import com.lazhu.business.tasks.enums.TaskTypeEnum;
import com.lazhu.business.tasks.service.TasksService;
import com.lazhu.common.RedisKeyConst;
import com.lazhu.common.enums.CurStepEnum;
import com.lazhu.common.exception.BusinessException;
import com.lazhu.common.exception.PermissionException;
import com.lazhu.common.media.MediaUtil;
import com.lazhu.common.utils.OssUtil;
import com.lazhu.montage.service.VideoMontageService;
import com.lazhu.montage.service.VideoTalkService;
import com.lazhu.support.base.AbstractController;
import com.lazhu.support.response.PageResponse;
import com.lazhu.support.response.PageResponseBuilder;
import com.lazhu.support.response.SimpleResponse;
import com.lazhu.support.response.SimpleResponseBuilder;
import com.lazhu.utils.WebTool;
import com.lazhu.web.dto.TTsRequest;
import io.netty.util.internal.StringUtil;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.io.File;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 内容管理
 */
@RestController
@RequestMapping("/api/content")
public class ContentController extends AbstractController {


    @Autowired
    private LLMService lLmService;

    @Autowired
    private ContentService contentService;

    @Autowired
    private OssUtil ossUtil;


    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private ActorService actorService;

    @Autowired
    private VideoTalkService videoService;

    @Autowired
    private TasksService tasksService;
    @Autowired
    private VideoTalkService videoTalkService;
    @Autowired
    private VideoMontageService videoMontageService;

    /**
     * 保存内容
     */
    @PostMapping("/save")
    public Mono<SimpleResponse<ContentDTO>> save(@RequestBody ContentDTO contentDto) {
        Long userId = WebTool.getUserId();
        return Mono.fromCallable(() -> {
            contentDto.setUserId(userId);
            Content content = contentDto.toEntity();
            if (content.getId() == null) {
                contentService.save(content);
            } else {
                contentService.update(content);
            }
            contentDto.setId(content.getId());
            return new SimpleResponseBuilder<>(contentDto).success().bulider();
        });
    }

    @GetMapping("/detail")
    public Mono<SimpleResponse<ContentDTO>> detail(@RequestParam Long id) {
        Long userId = WebTool.getUserId();
        return Mono.fromCallable(() -> {
            Content content = contentService.queryById(id);
            if (content == null) {
                throw new BusinessException("内容不存在");
            }
            if (!content.getUserId().equals(userId)) {
                throw new PermissionException("无权限");
            }
            return content;
        }).map(content -> {
            ContentDTO contentDto = ContentDTO.fromEntity(content);
            Actor actor = actorService.queryById(content.getActorId());
            contentDto.setActorName(actor != null ? actor.getNickName() : "");
            return new SimpleResponseBuilder<>(contentDto).success().bulider();
        });
    }

    /**
     * 删除
     */
    @PostMapping("/del")
    public Mono<SimpleResponse<String>> del(@RequestBody List<Long> ids) {
        return Mono.fromCallable(() -> {
            contentService.batchDelById(ids);
            return new SimpleResponseBuilder<>("").success().bulider();
        });
    }

    /**
     * @return 内容列表
     */
    @GetMapping("/list")
    public Mono<PageResponse<ContentDTO>> contentList(ContentQuery query) {
        return Mono.fromCallable(() -> {
            query.setUserId(WebTool.getUserId());
            if (StrUtil.isBlank(query.getDescs())) {
                query.setDescs("update_time");
            }
            Page<Content> page = contentService.queryPage(BeanUtil.beanToMap(query));
            //查询角色
            List<Long> actorIds = page.getRecords().stream().map(Content::getActorId).toList();
            List<Actor> actors = actorService.selectByIds(actorIds);
            Map<Long, Actor> actorMap = actors.stream().collect(Collectors.toMap(Actor::getId, e -> e));
            //封装数据
            List<ContentDTO> contentDTOList = page.getRecords().stream().map(content -> {
                ContentDTO contentDto = ContentDTO.fromEntity(content);
                Actor actor = actorMap.get(content.getActorId());
                contentDto.setActorName(actor != null ? actor.getNickName() : "");
                return contentDto;
            }).toList();
            Page<ContentDTO> contentDTOPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
            contentDTOPage.setRecords(contentDTOList);
            return new PageResponseBuilder<>(contentDTOPage).success().builder();
        });
    }

    /**
     * 文本创作
     */
    @PostMapping("/txt/create")
    public Mono<SimpleResponse<JSONObject>> createTxt(@RequestBody ContentCreationReq.TextRequest param) {
        return Mono.fromCallable(() -> {
            //保存草稿
            Content content;
            if (param.getId() != null) {
                content = contentService.queryById(param.getId());
            } else {
                content = new Content();
            }
            content.setUserId(WebTool.getUserId());
            content.setActorId(param.getActorId());
            content.setTopic(param.getTopic());
            content.setStatus(0);
            content.setMetaData(JSONObject.toJSONString(param.getMetaData()));
            content.setContentType(param.getContentType());
            content.setCurStep(CurStepEnum.TXT_IN_PRODUCTION.getType());
            if (param.getId() != null) {
                contentService.update(content);
            } else {
                contentService.save(content);
            }
            String taskId = content.getId().toString();
            RMap<String, String> map = redissonClient.getMap(RedisKeyConst.TEXT_CREATION_TASK);
            JSONObject object = new JSONObject();
            object.put("taskId", taskId);
            object.put("done", false);
            map.put(taskId, object.toJSONString());
            // 大模型创作
            Thread.startVirtualThread(() -> {
                RMap<String, String> info = redissonClient.getMap(RedisKeyConst.TEXT_CREATION_TASK);
                String s = info.get(taskId);
                JSONObject taskInfo;
                if (StrUtil.isEmpty(s)) {
                    taskInfo = new JSONObject();
                    taskInfo.put("taskId", taskId);
                } else {
                    taskInfo = JSONObject.parseObject(s);
                }
                try {
                    // 组装参数
                    LLMBaseRequest<TextCreationReq> req = new LLMBaseRequest<>();
                    TextCreationReq textCreationReq = contentService.fromTextCreateRequest(param);
                    req.setParams(textCreationReq);
                    LLMBaseResponse<List<TextCreationResult>> llmResponse = lLmService.createTxt(req);
                    taskInfo.put("conversationId", llmResponse.getConversationId());
                    taskInfo.put("list", llmResponse.getBody());
                    taskInfo.put("status", 1);
                } catch (Exception e) {
                    taskInfo.put("status", 0);
                    taskInfo.put("errorMsg", e.getMessage());
                }
                taskInfo.put("done", true);
                info.put(taskId, JSONObject.toJSONString(taskInfo));
                content.setCurStep(CurStepEnum.TXT_COMPLETE.getType());
                contentService.update(content);
            });
            return new SimpleResponseBuilder<>(object).success().bulider();
        });
    }

    /**
     * 文本微调
     */
    @PostMapping("/txt/finetune")
    public Mono<SimpleResponse<JSONObject>> finetuneTxt(@RequestBody ContentCreationReq.TextFineTuneRequest param) {
        return Mono.fromCallable(() -> {
            RMap<String, String> map = redissonClient.getMap(RedisKeyConst.TEXT_MODIFY_TASK);
            String taskId = IdUtil.simpleUUID();
            JSONObject object = new JSONObject();
            object.put("taskId", taskId);
            object.put("done", false);
            map.put(taskId, object.toJSONString());

            // 大模型创作
            Thread.startVirtualThread(() -> {
                RMap<String, String> info = redissonClient.getMap(RedisKeyConst.TEXT_MODIFY_TASK);
                String s = info.get(taskId);
                JSONObject taskInfo;
                if (StrUtil.isEmpty(s)) {
                    taskInfo = new JSONObject();
                    taskInfo.put("taskId", taskId);
                } else {
                    taskInfo = JSONObject.parseObject(s);
                }
                try {
                    // 组装参数
                    LLMBaseRequest<TextCreationReq> req = new LLMBaseRequest<>();
                    TextCreationReq textCreationReq = contentService.TextFineTuneRequest(param);
                    req.setParams(textCreationReq);
                    req.setConversationId(param.conversationId());
                    LLMBaseResponse<List<TextCreationResult>> llmResponse = lLmService.finetuneTxt(req);
                    taskInfo.put("conversationId", llmResponse.getConversationId());
                    taskInfo.put("content", CollUtil.getFirst(llmResponse.getBody()));
                    taskInfo.put("status", 1);
                } catch (Exception e) {
                    taskInfo.put("status", 0);
                    taskInfo.put("errorMsg", e.getMessage());
                }
                taskInfo.put("done", true);
                info.put(taskId, JSONObject.toJSONString(taskInfo));
            });

            return new SimpleResponseBuilder<>(object).success().bulider();
        });
    }

    /**
     * 查询文本创作任务状态
     *
     * @param taskId 任务ID
     * @param type   任务类型 1 文本生成任务 2 文本修改任务 3 文本分段任务
     */
    @GetMapping("/text/task/query")
    public SimpleResponse<JSONObject> queryTextTask(@RequestParam String taskId, @RequestParam String type) {
        RMap<String, String> taskInfo;
        if ("1".equals(type)) {
            taskInfo = redissonClient.getMap(RedisKeyConst.TEXT_CREATION_TASK);
        } else if ("2".equals(type)) {
            taskInfo = redissonClient.getMap(RedisKeyConst.TEXT_MODIFY_TASK);
        } else if ("3".equals(type)) {
            taskInfo = redissonClient.getMap(RedisKeyConst.TEXT_SEGMENT_TASK);
        } else {
            return new SimpleResponseBuilder<JSONObject>().error("任务不存在").bulider();
        }
        String s = taskInfo.get(taskId);
        if (StrUtil.isEmpty(s)) {
            return new SimpleResponseBuilder<JSONObject>().error("任务不存在").bulider();
        }
        JSONObject jsonObject = JSONObject.parseObject(s);

        Boolean done = jsonObject.getBoolean("done");
        if (done) {
            // 如果制作完成，且成功
            Content content = contentService.queryById(Convert.toLong(taskId));
            if (ObjectUtil.equal(1, jsonObject.getInteger("status")) && ObjectUtil.isNotNull(content)) {
                JSONArray jsonArray = jsonObject.getJSONArray("list");
                if (!jsonArray.isEmpty()) {
                    JSONObject object = jsonArray.getJSONObject(0);
                    content.setTitle(object.getString("title"));
                    content.setContent(object.getString("copywriting"));
                    content.setSummary(object.getString("digest"));
                    contentService.update(content);
                }
            }
            // 删除redis
            taskInfo.remove(taskId);
        }
        return new SimpleResponseBuilder<JSONObject>(jsonObject).success().bulider();
    }

    /**
     * 视频创作
     */
    @PostMapping("/video/create")
    public SimpleResponse<VideoCombineResult> createVideo(@RequestBody ContentCreationReq.VideoRequest param) {
        logger.info("===> 视频创作开始，参数：{}", JSONObject.toJSONString(param));
        ContentDTO contentDTO = param.getContentDTO();
        if (contentDTO == null || contentDTO.getId() == null) {
            throw new BusinessException("内容不存在");
        }
        Long actorId = contentDTO.getActorId();
        Actor actor = actorService.queryById(actorId);
        if (actor == null) {
            throw new BusinessException("角色不存在");
        }
        String voiceId = lLmService.queryVoiceId(actorId);
        if (voiceId == null) {
            throw new BusinessException("角色未配置声音，请在IP管理中上传人物声音");
        }
        // 统一从 DTO 序列化到 Entity
        Content content = contentDTO.toEntity();
        content.setUserId(WebTool.getUserId());
        content.setTitle(param.getTitle());
        content.setStatus(0);
        content.setContent(param.getContent());
        JSONObject metaData = contentDTO.getMetaData();
        metaData.put("audioSource", 2);
        metaData.put("actorId", String.valueOf(param.getActorId()));
        metaData.put("videoId", param.getVideoId());
        content.setMetaData(JSONObject.toJSONString(metaData));
        content.setCurStep(CurStepEnum.VIDEO_IN_PRODUCTION.getType());
        contentService.update(content);
        //创建视频任务
        videoService.createVideoTalkTask(content.getId());
        //返回结果
        VideoCombineResult result = new VideoCombineResult();
        result.setTaskId(content.getId().toString());
        result.setDone(false);
        return new SimpleResponseBuilder<>(result).success().bulider();
    }

    /**
     * 重新创作
     */
    @GetMapping("/video/create/retry")
    public SimpleResponse<VideoCombineResult> createVideoRetry(Long id) {
        Content temp = contentService.queryById(id);
        // 判断是重新剪辑还是重新生成
        if(StringUtil.isNullOrEmpty(temp.getMediaUrl())){
            //创建视频
            videoService.createVideoTalkTask(id);
        }else {
            // 删除原有视频剪辑任务
            tasksService.deleteByContentIdAndType(id,TaskTypeEnum.VIDEO_MONTAGE.getCode());
            //创建视频剪辑任务
            Tasks montageTask = new Tasks();
            montageTask.setContentId(id);
            montageTask.setTaskId(String.valueOf(id));
            montageTask.setPriority(0);
            montageTask.setType(TaskTypeEnum.VIDEO_MONTAGE.getCode()); // 视频剪辑任务类型
            montageTask.setStatus(Tasks.STATUS_WAITING); // 待处理状态
            tasksService.save(montageTask);
        }
        // 更新状态
        temp.setCurStep(CurStepEnum.VIDEO_IN_PRODUCTION.getType());
        contentService.update(temp);
        //返回结果
        VideoCombineResult result = new VideoCombineResult();
        result.setTaskId(id.toString());
        result.setDone(false);
        return new SimpleResponseBuilder<>(result).success().bulider();
    }

    /**
     * 视频合成任务查询 (轮询)
     */
    @GetMapping("/video/task/query")
    public Mono<SimpleResponse<VideoCombineResult>> queryVideoTask(@RequestParam("taskId") String taskId) {
        return Mono.fromCallable(() -> {

            Content content = contentService.queryById(Long.valueOf(taskId));

            if (Objects.equals(content.getCurStep(), CurStepEnum.VIDEO_IN_PRODUCTION.getType())) {
                //视频进行中
                VideoCombineResult result = new VideoCombineResult();
                result.setDone(false);
                return result;
            }

            if (Objects.equals(content.getCurStep(), CurStepEnum.VIDEO_COMPLETE.getType())) {
                //视频合成完成
                TasksQuery query = new TasksQuery();
                query.setContentId(Convert.toLong(taskId));
                List<Tasks> tasks = tasksService.queryList(BeanUtil.beanToMap(query));
                //有任意条失败即为失败
                boolean failed = tasks.stream().anyMatch(e -> e.getStatus() == 3);
                if (failed) {
                    Tasks tasks1 = tasks.stream().filter(e -> e.getStatus() == 3).findFirst().orElse(null);
                    VideoSynthTaskResult videoSynthesizeTask = JSONObject.parseObject(tasks1.getResult(), VideoSynthTaskResult.class);
                    VideoCombineResult result = new VideoCombineResult();
                    result.setErrorMsg(videoSynthesizeTask.getMessage());
                    result.setTaskId(taskId);
                    result.setDone(true);
                    result.setResult("0");
                    return result;
                } else {
                    VideoCombineResult result = new VideoCombineResult();
                    result.setVideoPath(content.getMediaUrl());
                    result.setDuration(content.getDuration());
                    result.setTaskId(taskId);
                    result.setCoverImage(content.getCoverImg());
                    result.setDone(true);
                    result.setResult("1");
                    return result;
                }
            }
            throw new BusinessException("任务不存在");
        }).map(resp -> {
            return new SimpleResponseBuilder<>(resp).success().bulider();
        });
    }

    /**
     * 更新剪辑视频
     */
    @PostMapping("/video/clip/update")
    public SimpleResponse<String> updateClipVideo(@RequestBody ContentDTO contentDTO) {
        Content content = new Content();
        content.setId(contentDTO.getId());
        content.setExposeMediaUrl(contentDTO.getExposeMediaUrl());
        //生成封面
        File coverImageFile = MediaUtil.getVideoCover(contentDTO.getExposeMediaUrl());
        String coverImage = ossUtil.upload(coverImageFile);
        List<String> imgs = Collections.singletonList(coverImage);
        content.setCoverImg(JSONObject.toJSONString(imgs));
        content.setDuration(MediaUtil.getDuration(contentDTO.getExposeMediaUrl()));
        content.setStatus(contentDTO.getStatus());
        contentService.update(content);
        return new SimpleResponseBuilder<>("").success().bulider();
    }

    /**
     * 视频剪辑
     */
    @GetMapping("/video/clip/test")
    public SimpleResponse<String> testVideoClip(@RequestParam Long id) {
        Content content = contentService.queryById(id);
        Thread.startVirtualThread(() -> videoMontageService.processVideo(contentService.queryById(id)));
        return new SimpleResponseBuilder<>(content.getExposeMediaUrl()).success().bulider();
    }


    /**
     * 文本转语音
     */
    @PostMapping("/tts")
    public SimpleResponse<ContentAudioInfo> tts(@RequestBody TTsRequest request) {
        Long contentId = request.getContentId();
        String text = request.getText();
        Content content = contentService.queryById(contentId);
        Long actorId = content.getActorId();
        Actor actor = actorService.queryById(actorId);
        if (actor == null) {
            throw new BusinessException("IP不存在");
        }
        String voiceId = lLmService.queryVoiceId(actorId);
        if (voiceId == null) {
            throw new BusinessException("请先配置IP声音");
        }
        String outputPath = TxtToAudioService.getMp3PartPath(contentId.toString(), videoService.generatorFolder(), request.getIndex());
        FileUtil.touch(outputPath);
        CosyVoiceTool.AudioInfo audioInfo = TxtToAudioService.tts(request.getIndex(), voiceId, text, outputPath);
        //上传语音
        File tempFile = new File(outputPath);
        String audioUrl = ossUtil.uploadWithRandomFileName(tempFile, "video/" + contentId);
        ContentAudioInfo info = new ContentAudioInfo();
        info.setSsml(text);
        info.setAudioUrl(audioUrl);
        info.setDuration(audioInfo.timeLong());
        info.setFirstDelay(audioInfo.firstDelay());
        return new SimpleResponseBuilder<>(info).success().bulider();
    }


    /**
     * tts分段
     */
    @GetMapping("/tts/segment")
    public SimpleResponse<JSONObject> ttsSegment(@RequestParam(value = "contentId") Long contentId) {
        String taskId = contentId.toString();
        RMap<String, String> taskInfo = redissonClient.getMap(RedisKeyConst.TEXT_SEGMENT_TASK);
        JSONObject object = new JSONObject();
        object.put("taskId", taskId);
        object.put("done", false);
        taskInfo.put(taskId, object.toJSONString());

        // 异步处理
        Thread.startVirtualThread(() -> {
            RMap<String, String> info = redissonClient.getMap(RedisKeyConst.TEXT_SEGMENT_TASK);
            String s = info.get(taskId);
            JSONObject taskObject;
            if (StrUtil.isEmpty(s)) {
                taskObject = new JSONObject();
                taskObject.put("taskId", taskId);
            } else {
                taskObject = JSONObject.parseObject(s);
            }

            try {
                Content content = contentService.queryById(contentId);
                String ssml = content.getSsml();
                List<ContentAudioInfo> list;
                if (StrUtil.isNotBlank(ssml)) {
                    list = JSONObject.parseArray(ssml, ContentAudioInfo.class);
                } else {
                    list = videoService.createSsml(content.getContent());
                }
                taskObject.put("list", list);
                taskObject.put("status", 1);
            } catch (Exception e) {
                taskObject.put("status", 0);
                taskObject.put("errorMsg", e.getMessage());
            }

            taskObject.put("done", true);
            info.put(taskId, JSONObject.toJSONString(taskObject));
        });

        return new SimpleResponseBuilder<>(object).success().bulider();
    }


}
