package com.lazhu.web.controller;

import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSON;
import com.lazhu.business.content.entity.Content;
import com.lazhu.business.content.service.ContentService;
import com.lazhu.business.publishlog.dto.PublishRequest;
import com.lazhu.business.publishlog.dto.PublishResponse;
import com.lazhu.business.publishlog.entity.PublishLog;
import com.lazhu.business.publishlog.service.PublishLogService;
import com.lazhu.business.publishlog.service.PublishService;
import com.lazhu.common.exception.BusinessException;
import com.lazhu.common.media.MediaResourceManager;
import com.lazhu.common.media.MediaUrlProcessor;
import com.lazhu.gxc.publish.model.UploadResult;
import com.lazhu.support.base.AbstractController;
import com.lazhu.support.response.ArrayResponse;
import com.lazhu.support.response.ArrayResponseBuilder;
import com.lazhu.support.response.SimpleResponse;
import com.lazhu.support.response.SimpleResponseBuilder;
import com.lazhu.support.util.StringUtils;
import com.lazhu.utils.WebTool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 发布接口
 */
@RestController
@RequestMapping(value = "/api/pub")
public class PublishController extends AbstractController {

    @Autowired
    private PublishService publishService;

    @Autowired
    private ContentService contentService;

    @Autowired
    private PublishLogService publishLogService;


    private List<PublishLog> buildLog(PublishRequest request, Content content) {
        List<PublishLog> publishLogs = new ArrayList<>();
        for (PublishRequest.Account account : request.getAccountList()) {
            PublishLog publishLog = new PublishLog();
            publishLog.setContentId(request.getContentId());
            publishLog.setTitle(request.getMetaData().getTitle());
            publishLog.setDescription(request.getMetaData().getDescription());
            publishLog.setPublishType(request.getPublishType());
            publishLog.setCreateTime(new Date());
            publishLog.setUpdateTime(new Date());
            publishLog.setCreateBy(WebTool.getUserId());
            publishLog.setMetaData(JSON.toJSONString(request.getMetaData()));
            publishLog.setChannel(account.getChannelType());
            publishLog.setActorId(content.getActorId());
            publishLog.setChannelAccount(account.getAccount());
            publishLog.setUserId(content.getUserId());
            publishLogs.add(publishLog);
        }
        return publishLogs;
    }

    /**
     * 发布
     */
    @PostMapping("/publish")
    public ArrayResponse<PublishResponse> publish(@RequestBody PublishRequest request) {
        Content content = contentService.queryById(request.getContentId());
        // 视频已发布，不能重新发布
        if (Objects.equals(content.getStatus(), 2)) {
            throw new BusinessException("视频已发布，不能重新发布");
        }
        // 构建发布记录
        List<PublishLog> publishLogs = buildLog(request, content);
        String exposeMediaUrl = content.getExposeMediaUrl();
        if (StringUtils.isBlank(exposeMediaUrl)) {
            throw new BusinessException("视频连接为空");
        }
        String videoPath = null;
        List<PublishResponse> publishResponses = new ArrayList<>();
        try {
            videoPath = MediaUrlProcessor.downloadToLocal(exposeMediaUrl);
            for (PublishLog publishLog : publishLogs) {
                if (request.getPublishType() == 1) {
                    // 实时发布
                    UploadResult uploadResult = publishService.publish(publishLog, videoPath);
                    publishLog.setPublishTime(new Date());
                    if (uploadResult.isSuccess()) {
                        publishLog.setPublishId(uploadResult.getVideoId());
                        publishLog.setPublishStatus("2");
                        publishResponses.add(PublishResponse.success(publishLog.getChannel(), publishLog.getChannelAccount()));
                    } else {
                        publishLog.setPublishStatus("3");
                        publishResponses.add(PublishResponse.fail(publishLog.getChannel(), publishLog.getChannelAccount(), uploadResult.getMessage()));
                    }
                } else {
                    // 定时发布通过调度来处理
                    publishLog.setPublishStatus("0");
                    publishLog.setPublishTime(request.getPublishTime());
                    publishResponses.add(PublishResponse.wait(publishLog.getChannel(), publishLog.getChannelAccount()));
                }
            }
        } finally {
            // 清理临时文件
            if (videoPath != null) {
                MediaResourceManager.cleanupTempFile(new File(videoPath));
            }
        }
        // 如果有一条发布成功，更新内容为已发布
        if (publishResponses.stream().anyMatch(e -> "2".equals(e.getPublishStatus()))) {
            Content updateContent = new Content();
            updateContent.setId(content.getId());
            updateContent.setStatus(2);
            contentService.update(updateContent);
        }
        return new ArrayResponseBuilder<>(publishResponses).success().bulider();
    }

    /**
     * 重新发布
     */
    @GetMapping("/rePublish")
    public SimpleResponse<PublishResponse> rePublish(@RequestParam Long id) {
        PublishLog publishLog = publishLogService.queryById(id);
        // 如果已经发布，不能重新发布
        if (!"3".equals(publishLog.getPublishStatus())) {
            throw new BusinessException("此状态下不能重新发布");
        }
        String videoPath = null;
        try {
            if (publishLog.getPublishType() == 1) {
                Content content = contentService.queryById(publishLog.getContentId());
                String exposeMediaUrl = content.getExposeMediaUrl();
                videoPath = MediaUrlProcessor.downloadToLocal(exposeMediaUrl);
                UploadResult uploadResult = publishService.publish(publishLog, videoPath);
                publishLog.setPublishTime(new Date());
                if (uploadResult.isSuccess()) {
                    publishLog.setPublishId(uploadResult.getVideoId());
                    publishLog.setPublishStatus("2");

                    // 更新内容为已发布
                    Content updateContent = new Content();
                    updateContent.setId(content.getId());
                    updateContent.setStatus(2);
                    contentService.update(updateContent);

                    return new SimpleResponseBuilder<>(PublishResponse.success(publishLog.getChannel(), publishLog.getChannelAccount())).success().bulider();
                } else {
                    publishLog.setPublishStatus("3");
                    return new SimpleResponseBuilder<>(PublishResponse.fail(publishLog.getChannel(), publishLog.getChannelAccount(), uploadResult.getMessage())).success().bulider();
                }
            } else {
                // 定时发布通过调度来处理
                publishLog.setPublishStatus("0");
                return new SimpleResponseBuilder<>(PublishResponse.wait(publishLog.getChannel(), publishLog.getChannelAccount())).success().bulider();
            }
        } finally {
            // 清理临时文件
            if (videoPath != null) {
                MediaResourceManager.cleanupTempFile(new File(videoPath));
            }
        }
    }

}
