<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lazhu.web.mapper.ApiMediaAssertsMapper">


    <sql id="query">
        <if test="params.title != null and params.title != ''">
            and title like concat(concat('%',#{params.title}),'%')
        </if>
        <if test="params.actorId != null">
            and actor_id = #{params.actorId}
        </if>
        <if test="params.actorIds != null and params.actorIds.size > 0">
            and actor_id in
            <foreach item="item" collection="params.actorIds" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.userId != null">
            and create_by = #{params.userId}
        </if>
    </sql>

    <select id="queryImgAssertsList" resultType="com.lazhu.web.dto.MediaAssertsDTO">
        select id,title,img_url as url,actor_id as actorId,size as size  from c_media_assets_img
        <where>
            <include refid="query"/>
        </where>
    </select>

    <select id="queryVideoAssertsList" resultType="com.lazhu.web.dto.MediaAssertsDTO">
        select id,title,video_url as url,duration as duration,cover_img,actor_id as actorId,size as size
        from c_media_assets_video
        <where>
            <include refid="query"/>
        </where>
    </select>

    <select id="queryVoiceAssertsList" resultType="com.lazhu.web.dto.MediaAssertsDTO">
        select id,title,audio_url as url,duration as duration,actor_id as actorId from c_media_assets_voice
        <where>
            <include refid="query"/>
        </where>
    </select>

    <select id="queryUserAssertsList" resultType="com.lazhu.web.dto.MediaAssertsDTO">
        select id,assets_type as assetsType,title,media_url as url,duration as duration,cover_img,
        actor_id as actorId
        from
        c_user_media_assets
        <where>
            <if test="params.assetsType != null">
                and assets_type = #{params.assetsType}
            </if>
            <include refid="query"/>
        </where>
    </select>
    <select id="getMediaAssetsCount" resultType="com.lazhu.web.dto.MediaAssetsCount">
        select
        actor_id as actorId,
        assets_type as type,
        count(1) as count
        from
        c_user_media_assets
        where
        actor_id in
        <foreach item="item" collection="actorIds" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
        group by actor_id, assets_type
    </select>
</mapper>
