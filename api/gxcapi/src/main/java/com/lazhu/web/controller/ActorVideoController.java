package com.lazhu.web.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lazhu.business.actor.entity.ActorVideo;
import com.lazhu.business.actor.entity.ActorVideoQuery;
import com.lazhu.business.actor.service.ActorVideoService;
import com.lazhu.business.mediaassets.entity.UserMediaAssets;
import com.lazhu.common.media.MediaUtil;
import com.lazhu.common.utils.OssUtil;
import com.lazhu.montage.service.VideoMontageTool;
import com.lazhu.support.base.AbstractController;
import com.lazhu.support.response.PageResponse;
import com.lazhu.support.response.PageResponseBuilder;
import com.lazhu.support.response.SimpleResponse;
import com.lazhu.support.response.SimpleResponseBuilder;
import com.lazhu.utils.WebTool;
import com.lazhu.web.dto.MediaAssertsDTO;
import com.lazhu.web.dto.MediaAssertsQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.io.File;

/**
 * ip人物视频
 */
@RestController
@RequestMapping(value = "/api/actorVideo")
public class ActorVideoController extends AbstractController {
    @Autowired
    private ActorVideoService actorVideoService;

    @Autowired
    private VideoMontageTool videoMontageTool;

    @Autowired
    private OssUtil ossUtil;


    /**
     * ip人物视频列表
     */
    @PostMapping(value = "/list")
    public Mono<PageResponse<ActorVideo>> list(@RequestBody ActorVideoQuery query) {
        return Mono.create(sike -> {
            query.setUserId(WebTool.getUserId());
            Page<ActorVideo> list = actorVideoService.queryPage(BeanUtil.beanToMap( query));
            sike.success(new PageResponseBuilder<>(list).success().builder());
        });
    }



    /**
     * 上传ip人物视频保存
     */
    @PostMapping(value = "/upload")
    public Mono<SimpleResponse<ActorVideo>> upload(@RequestBody ActorVideo param) {
        return Mono.create(sike -> {
            param.setCreateBy(WebTool.getUserId());
            param.setUserId(WebTool.getUserId());

            // 压缩视频
            String processId = "video_montage_" + System.currentTimeMillis() + "_" + Thread.currentThread().threadId();
            // 根据传入尺寸，压缩视频，获取视频大小
            String outPath =videoMontageTool.generateOutputPath(processId,"video_actor",".mp4");
            MediaUtil.compressVideo(param.getMediaUrl(),outPath,28,0,0);
            File file = cn.hutool.core.io.FileUtil.file(outPath);
            // 上传新视频
            String url = ossUtil.upload(file);
            param.setMediaUrl(url);
            ActorVideo actorVideo = actorVideoService.saveActorVideo(param);
            sike.success(new SimpleResponseBuilder<>(actorVideo).success().bulider());
        });
    }


    /**
     * 删除素材
     */
    @GetMapping(value = "/del")
    public Mono<SimpleResponse<String>> del(@RequestParam Long id) {
        return Mono.create(sike -> {
            actorVideoService.deleteById(id);
            sike.success(new SimpleResponseBuilder<String>().success().bulider());
        });
    }



}
