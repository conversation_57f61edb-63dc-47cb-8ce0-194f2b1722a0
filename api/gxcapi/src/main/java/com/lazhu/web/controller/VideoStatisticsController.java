package com.lazhu.web.controller;


import com.lazhu.business.videostatistics.dto.VideoDailyStat;
import com.lazhu.business.videostatistics.dto.ChartRequest;
import com.lazhu.business.videostatistics.service.VideoStatisticsService;
import com.lazhu.business.videostatistics.vo.ChartDataVO;
import com.lazhu.business.videostatistics.vo.VideoStatsVO;
import com.lazhu.support.base.AbstractController;
import com.lazhu.support.response.ArrayResponse;
import com.lazhu.support.response.ArrayResponseBuilder;
import com.lazhu.support.response.SimpleResponse;
import com.lazhu.support.response.SimpleResponseBuilder;
import com.lazhu.utils.WebTool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 内容统计
 */
@RestController
@RequestMapping(value = "/api/vs")
public class VideoStatisticsController extends AbstractController {

    @Autowired
    private VideoStatisticsService videoStatisticsService;


    /**
     *  关键指标总览
     * <AUTHOR>
     * @param[1] periodType 1、七天
     * @return SimpleResponse<VideoStatsVO>
     * @time 2025/8/28 14:21
     */
    @GetMapping(value = "/getVideoStats")
    public SimpleResponse<VideoStatsVO> getVideoStats(String periodType) {
        Long userId = WebTool.getUserId();
        return new SimpleResponseBuilder<>(videoStatisticsService.getStatistics(userId,periodType)).success().bulider();
    }


   /**
    * 获取数据趋势图标
    * <AUTHOR>
    * @param[1] request
    * @return SimpleResponse<ChartDataVO>
    * @time 2025/8/28 15:07
    */
    @PostMapping(value = "/chart")
    public SimpleResponse<ChartDataVO> getVideoStatChart(@RequestBody ChartRequest request) {
        Long userId = WebTool.getUserId();
        request.setUserId(userId);
        return new SimpleResponseBuilder<>(videoStatisticsService.getVideoStatChart(request)).success().bulider();
    }


    /**
     *  获取数据详情
     * <AUTHOR>
     * @param[1] request
     * @return ArrayResponse<DailyStat>
     * @time 2025/8/28 16:29
     */
    @PostMapping(value = "/detail")
    public ArrayResponse<VideoDailyStat> readDetail(@RequestBody ChartRequest request) {
        Long userId = WebTool.getUserId();
        request.setUserId(userId);
        return new ArrayResponseBuilder<>(videoStatisticsService.queryDetail(request)).success().bulider();
    }
}
