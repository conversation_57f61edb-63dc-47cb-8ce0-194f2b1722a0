package com.lazhu.web.controller;

import com.lazhu.business.noticestatistics.dto.NoticeDailyStat;
import com.lazhu.business.noticestatistics.service.NoticeStatisticsService;
import com.lazhu.business.noticestatistics.vo.NoticeStatsVo;
import com.lazhu.business.videostatistics.dto.ChartRequest;
import com.lazhu.business.videostatistics.vo.ChartDataVO;
import com.lazhu.support.base.AbstractController;
import com.lazhu.support.response.ArrayResponse;
import com.lazhu.support.response.ArrayResponseBuilder;
import com.lazhu.support.response.SimpleResponse;
import com.lazhu.support.response.SimpleResponseBuilder;
import com.lazhu.utils.WebTool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * 关注者统计
 */
@RestController
@RequestMapping(value = "/api/ns")
public class NoticeStatisticsController extends AbstractController {

    @Autowired
    private NoticeStatisticsService noticeStatisticsService;


    /**
     *  关键指标总览
     * <AUTHOR>
     * @param[1] periodType 1、七天
     * @return SimpleResponse<NoticeStatsVo>
     * @time 2025/8/28 14:21
     */
    @GetMapping(value = "/getNoticeStats")
    public SimpleResponse<NoticeStatsVo> getNoticeStats(String periodType) {
        Long userId = WebTool.getUserId();
        return new SimpleResponseBuilder<>(noticeStatisticsService.getStatistics(userId,periodType)).success().bulider();
    }

    /**
     * 获取数据趋势图标
     * <AUTHOR>
     * @param[1] request
     * @return SimpleResponse<ChartDataVO>
     * @time 2025/8/28 15:07
     */
    @PostMapping(value = "/chart")
    public SimpleResponse<ChartDataVO> getNoticeStatChart(@RequestBody ChartRequest request) {
        Long userId = WebTool.getUserId();
        request.setUserId(userId);
        return new SimpleResponseBuilder<>(noticeStatisticsService.getVideoStatChart(request)).success().bulider();
    }


    /**
     *  获取数据详情
     * <AUTHOR>
     * @param[1] request
     * @return ArrayResponse<NoticeDailyStat>
     * @time 2025/8/28 16:29
     */
    @PostMapping(value = "/detail")
    public ArrayResponse<NoticeDailyStat> readDetail(@RequestBody ChartRequest request) {
        Long userId = WebTool.getUserId();
        request.setUserId(userId);
        return new ArrayResponseBuilder<>(noticeStatisticsService.queryDetail(request)).success().bulider();
    }
}
