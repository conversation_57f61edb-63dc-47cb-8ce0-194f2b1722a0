package com.lazhu.web.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.lazhu.baseai.tool.dify.PromptUtil;
import com.lazhu.business.actor.entity.*;
import com.lazhu.business.actor.service.ActorService;
import com.lazhu.business.actor.service.ActorVideoService;
import com.lazhu.business.actorcontentexample.entity.ActorContentExample;
import com.lazhu.business.actorcontentexample.service.ActorContentExampleService;
import com.lazhu.common.exception.BusinessException;
import com.lazhu.common.exception.PermissionException;
import com.lazhu.support.base.AbstractController;
import com.lazhu.support.response.ArrayResponse;
import com.lazhu.support.response.ArrayResponseBuilder;
import com.lazhu.support.response.SimpleResponse;
import com.lazhu.support.response.SimpleResponseBuilder;
import com.lazhu.utils.WebTool;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 演员接口
 */
@RestController
@RequestMapping(value = "/api/actor")
public class ActorController extends AbstractController {

    @Autowired
    private ActorService actorService;

    @Autowired
    private ActorContentExampleService actorContentExampleService;

    @Autowired
    private ActorVideoService actorVideoService;

    /**
     * 演员列表
     */
    @GetMapping(value = "/list")
    public Mono<ArrayResponse<ActorDTO>> list(ActorQuery query) {
        // 设置查询条件
        Long userId = WebTool.getUserId();
        query.setCreateBy(userId);
        query.setUseStatus(1);
        return Mono.fromCallable(() -> {
            // 查询数据
            return actorService.queryList(BeanUtil.beanToMap(query));
        }).map(list -> {
            //获取演员id
            List<Long> actorIds = list.stream().map(Actor::getId).toList();
            ActorVideoQuery actorVideoQuery =new ActorVideoQuery();
            actorVideoQuery.setActorIds(actorIds);
            List<ActorVideo> actorVideos = actorVideoService.queryList(BeanUtil.beanToMap(actorVideoQuery));
            Map<Long, List<ActorVideo>> map = actorVideos.stream().collect(Collectors.groupingBy(ActorVideo::getActorId));

            //数据封装
            List<ActorDTO> actorDTOList = new ArrayList<>();
            for (Actor actor : list) {
                ActorDTO actorDTO = ActorDTO.buildFromActor(actor);
                if(StringUtils.isNotBlank(actor.getVoiceUrl())){
                    actorDTO.setAudioMaterialCount(1);
                }else {
                    actorDTO.setAudioMaterialCount(0);
                }
                List<ActorVideo> videoList = map.get(actor.getId());
                actorDTO.setVideoMaterialCount(CollectionUtil.isEmpty(videoList)?0:videoList.size());
                actorDTOList.add(actorDTO);
            }
            // 构建响应
            return new ArrayResponseBuilder<>(actorDTOList).success().bulider();
        }).subscribeOn(Schedulers.boundedElastic());
    }

    /**
     * 演员详情
     */
    @GetMapping(value = "/detail")
    public Mono<SimpleResponse<ActorDTO>> detail(@RequestParam Long id) {
        return Mono.fromCallable(() -> {
            // 查询数据
            return actorService.queryById(id);
        }).map(actor -> {
            // 构建响应
            List<ActorContentExample> actorContentExamples = actorContentExampleService.queryByActorId(id);
            ActorDTO actorDTO = ActorDTO.buildFromActor(actor);
            actorDTO.setExampleContent(actorContentExamples);
            return new SimpleResponseBuilder<>(actorDTO).success().bulider();
        }).subscribeOn(Schedulers.boundedElastic());
    }


    /**
     * 创建演员
     */
    @PostMapping(value = "/create")
    public Mono<SimpleResponse<String>> create(@RequestBody ActorDTO param) {
        Long userId = WebTool.getUserId();
        Actor actor = BeanUtil.toBean(param, Actor.class);
        actor.setRolePrompt(param.getRolePrompt());
        return Mono.fromCallable(() -> {
            if (actor.getId() == null) {
                actor.setCreateBy(userId);
                actorService.save(actor);
            } else {
                // 检查是否是自己创建的演员
//                Actor existingActor = actorService.queryById(actor.getId());
//                if (existingActor == null) {
//                    throw new BusinessException("演员不存在");
//                }
//
//                // 权限检查
//                assert userId != null;
//                if (!userId.equals(existingActor.getCreateBy())) {
//                    throw new PermissionException("actor", "update", "无权修改其他用户的演员");
//                }
                actorService.update(actor);
            }
            List<ActorContentExample> exampleContent = param.getExampleContent();
            if (CollectionUtil.isNotEmpty(exampleContent)) {
                for (ActorContentExample example : exampleContent) {
                    example.setActorId(actor.getId());
                    actorContentExampleService.saveOrUpdate(example);
                }
            }
            return new SimpleResponseBuilder<>(actor.getId().toString()).success().bulider();
        }).subscribeOn(Schedulers.boundedElastic());
    }

    /**
     * 删除角色
     */
    @GetMapping(value = "/del")
    public Mono<SimpleResponse<String>> del(@RequestParam Long id) {
        Long userId = WebTool.getUserId();
        return Mono.fromCallable(() -> {
            Actor actor = actorService.queryById(id);
            if (actor == null) {
                throw new BusinessException("演员不存在");
            }
            // 权限检查
            assert userId != null;
            if (!userId.equals(actor.getCreateBy())) {
                throw new PermissionException("actor", "delete", "无权删除");
            }
            // 删除角色 （逻辑删除）
            actor.setUseStatus(0);
            actorService.update(actor);
            return new SimpleResponseBuilder<>("").success().bulider();
        }).subscribeOn(Schedulers.boundedElastic());
    }


    @Autowired
    private PromptUtil promptUtil;

    /**
     * 生成提示词
     */
    @PostMapping("/createPrompt")
    public SimpleResponse<String> createPrompt(@RequestBody JSONObject param) {
        String profile = param.getString("profile");
        String prompt = promptUtil.createPrompt(profile);
        return new SimpleResponseBuilder<>(prompt).success().bulider();
    }

    /**
     * 创建演员
     */
    @PostMapping(value = "/saveVoice")
    public Mono<SimpleResponse<Boolean>> saveVoice(@RequestBody Actor param) {
        return Mono.create(sike -> {
            actorService.saveVoice(param);
            sike.success(new SimpleResponseBuilder<Boolean>().success().bulider());
        });
    }
}
