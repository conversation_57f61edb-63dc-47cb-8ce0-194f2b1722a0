package com.lazhu.web.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lazhu.business.userchannelaccount.entity.UserChannelAccount;
import com.lazhu.business.userchannelaccount.entity.UserChannelAccountQuery;
import com.lazhu.business.userchannelaccount.enums.ChannelType;
import com.lazhu.business.userchannelaccount.service.UserChannelAccountService;
import com.lazhu.business.userchannelaccount.vo.UserChannelAccountVO;
import com.lazhu.gxc.publish.model.LoginResult;
import com.lazhu.gxc.publish.model.LoginSession;
import com.lazhu.support.base.AbstractController;
import com.lazhu.support.response.*;
import com.lazhu.utils.WebTool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户账户管理
 */
@RestController
@RequestMapping("/api/uc")
public class UserChannelController extends AbstractController {

    @Autowired
    private UserChannelAccountService userChannelAccountService;

    /**
     * 开始登录
     */
    @GetMapping("/startLogin")
    public SimpleResponse<LoginSession> startLogin(String actorId, Integer channelType) {
        ChannelType channelTypeEnum = ChannelType.fromType(channelType);
        LoginSession session = userChannelAccountService.startLogin(Long.valueOf(actorId), channelTypeEnum);
        return new SimpleResponseBuilder<>(session).success().bulider();
    }

    /**
     * 执行登录（轮询）
     */
    @GetMapping("/doLogin")
    public SimpleResponse<LoginResult> doLogin(String actorId, Integer channelType, String sessionId) {
        ChannelType channelTypeEnum = ChannelType.fromType(channelType);
        LoginResult loginResult = userChannelAccountService.login(Long.valueOf(actorId), channelTypeEnum, sessionId);
        return new SimpleResponseBuilder<>(loginResult).success().bulider();
    }


    /**
     * 删除账号
     */
    @GetMapping("/del")
    public SimpleResponse<String> del(@RequestParam Long id) {
        userChannelAccountService.deleteById(id);
        return new SimpleResponseBuilder<>("").success().bulider();
    }


    /**
     * 账号列表
     */
    @PostMapping("/page")
    public PageResponse<UserChannelAccountVO> readPage(@RequestBody UserChannelAccountQuery param) {
        param.setUserId(WebTool.getUserId());
        Page<UserChannelAccountVO> page = userChannelAccountService.readPage(param);
        return new PageResponseBuilder<>(page).success().builder();
    }

    /**
     * 账号列表
     * 根据条件获取账号列表
     */
    @PostMapping("/list")
    public ArrayResponse<UserChannelAccount> list(@RequestBody UserChannelAccountQuery param) {
        param.setUserId(WebTool.getUserId());
        List<UserChannelAccount> list = userChannelAccountService.queryList(BeanUtil.beanToMap(param));
        return new ArrayResponseBuilder<>(list).success().bulider();
    }

}
