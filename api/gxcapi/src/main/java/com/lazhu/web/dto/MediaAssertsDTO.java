package com.lazhu.web.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * 素材列表dto
 */
@Data
public class MediaAssertsDTO implements Serializable {

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    private String assetsType;

    private String title;

    private String url;

    /**
     * 封面
     */
    private String coverImg;

    /**
     * 时长
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long duration;

    /**
     * 角色id
     */
    @JSONField(serializeUsing =  ToStringSerializer.class)
    private Long actorId;

    /**
     * 角色名称
     */
    private String actorName;

    /**
     * 尺寸;宽 X 高
     */
    private String size;

}
