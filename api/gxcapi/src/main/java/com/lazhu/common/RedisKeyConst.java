package com.lazhu.common;

/**
 * Redis缓存的Key常量类
 */
public class RedisKeyConst {

    /**
     * 缓存前缀
     */
    public static final String REDIS_KEY_PREFIX = "gxc:";


    /**
     * 验证码发送次数 （手机号）
     */
    public static final String SMS_SEND_COUNT = REDIS_KEY_PREFIX + "sms:send_count:mobile:";

    /**
     * 验证码发送次数 （IP）
     */
    public static final String SMS_SEND_COUNT_IP = REDIS_KEY_PREFIX + "sms:send_count:ip:";

    /**
     * 短信验证码缓存
     */
    public static final String SMS_CODE = REDIS_KEY_PREFIX + "sms:code:";


    /**
     * 视频合成任务缓存
     */
    public static final String VIDEO_SYNTHESIZE_TASK = REDIS_KEY_PREFIX + "video_synthesize_task";


    /**
     * 文本创作任务缓存
     */
    public static final String TEXT_CREATION_TASK = REDIS_KEY_PREFIX + "text_creation_task";

    /**
     * 文本修改任务缓存
     */
    public static final String TEXT_MODIFY_TASK = REDIS_KEY_PREFIX + "text_modify_task";

    /**
     * 文字分段任务缓存
     */
    public static final String TEXT_SEGMENT_TASK = REDIS_KEY_PREFIX + "text_segment_task";


    /**
     * 无效token
     */
    public static final String INVALID_TOKEN = REDIS_KEY_PREFIX + "invalid_token";

    /**
     * 图形验证码
     */
    public static final String IMAGE_CODE="IMAGE_CODE:";

    /**
     * 验证码存活时长 60秒
     */
    public static final Long IMAGE_CODE_EXPIRES = 18000L;


}
