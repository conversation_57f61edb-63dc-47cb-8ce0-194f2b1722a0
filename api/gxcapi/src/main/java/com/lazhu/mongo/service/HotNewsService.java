package com.lazhu.mongo.service;

import cn.hutool.core.util.StrUtil;
import com.lazhu.mongo.entity.HotNews;
import com.lazhu.mongo.repository.HotNewsRepository;
import com.lazhu.system.sysparams.service.SysParamsService;
import com.lazhu.mongo.entity.HotTopicsQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.ReactiveMongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.regex.Pattern;

@Service
public class HotNewsService {

    @Autowired
    private HotNewsRepository hotNewsRepository;

    @Autowired
    private SysParamsService sysParamsService;

    @Autowired
    private ReactiveMongoTemplate mongoTemplate;

    /**
     * 高级查询方法 - 根据查询条件动态查询热点新闻（使用MongoTemplate）
     *
     * @param queryParams 查询参数
     * @return 热点新闻列表
     */
    public Flux<HotNews> findHotNewsAdvanced(HotTopicsQuery queryParams) {
        Query query = buildQuery(queryParams);

        // 添加分页和排序
        Pageable pageable = PageRequest.of(queryParams.getPage() - 1, queryParams.getSize(),
                Sort.by(Sort.Direction.DESC, "heat"));
        query.with(pageable);

        return mongoTemplate.find(query, HotNews.class);
    }

    /**
     * 高级查询计数方法 - 根据查询条件动态计数（使用MongoTemplate）
     *
     * @param queryParams 查询参数
     * @return 符合条件的记录数
     */
    public Mono<Long> countHotNewsAdvanced(HotTopicsQuery queryParams) {
        Query query = buildQuery(queryParams);
        return mongoTemplate.count(query, HotNews.class);
    }

    /**
     * 构建MongoDB查询条件
     *
     * @param queryParams 查询参数
     * @return MongoDB Query对象
     */
    private Query buildQuery(HotTopicsQuery queryParams) {
        Query query = new Query();
        Criteria criteria = new Criteria();

        boolean hasCriteria = false;

        // 来源条件
        if (StrUtil.isNotBlank(queryParams.getPlatSource())) {
            criteria = criteria.and("source").is(queryParams.getPlatSource());
            hasCriteria = true;
        }

        // 日期条件
        if (StrUtil.isNotBlank(queryParams.getTopicDate())) {
            if (hasCriteria) {
                criteria = criteria.and("date").is(queryParams.getTopicDate());
            } else {
                criteria = Criteria.where("date").is(queryParams.getTopicDate());
                hasCriteria = true;
            }
        }

        // 关键字条件（模糊查询）
        if (StrUtil.isNotBlank(queryParams.getKeyword())) {
            // 对用户输入的特殊字符进行转义，防止正则表达式语法错误
            String escapedKeyword = Pattern.quote(queryParams.getKeyword());
            if (hasCriteria) {
                criteria = criteria.and("topic").regex(escapedKeyword, "i");
            } else {
                criteria = Criteria.where("topic").regex(escapedKeyword, "i");
                hasCriteria = true;
            }
        }

        if (hasCriteria) {
            query.addCriteria(criteria);
        }

        return query;
    }

    public Flux<String> findAllSources() {
        String s = sysParamsService.queryByKey("hot_new_source_index");
        List<String> sourceIndex = Arrays.asList(s.split(","));
        //按配置的索引位置排序，没配置的排最后
        Comparator<String> comparator = (o1, o2) -> {
            int index1 = sourceIndex.indexOf(o1);
            index1 = index1 == -1 ? 100 : index1;
            int index2 = sourceIndex.indexOf(o2);
            index2 = index2 == -1 ? 100 : index2;
            // 按索引位置排序
            return Integer.compare(index1, index2);
        };
        return hotNewsRepository.findDistinctSources().map(HotNews::getSource).distinct().sort(comparator);
    }

}