package com.lazhu.common;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * redisson锁统一管理器
 */
@RequiredArgsConstructor
@Slf4j
@Component
public class RedissonLockManager {

    private final RedissonClient redissonClient;

    /**
     * 当前持有的锁集合，方便统一释放
     */
    private final Set<RLock> heldLocks = ConcurrentHashMap.newKeySet();


    /**
     * 直接加锁（不带超时时间，锁会一直持有直到显式释放）
     *
     * @param lockKey 锁的key
     * @return 获取成功返回锁对象
     */
    public RLock lock(String lockKey) {
        RLock lock = redissonClient.getLock(lockKey);
        lock.lock(); // 阻塞直到获取锁
        heldLocks.add(lock);
        log.debug("直接加锁成功: {}", lockKey);
        return lock;
    }

    /**
     * 尝试获取锁，带超时时间，单位秒
     *
     * @param lockKey 锁的key
     * @return 获取成功返回锁对象，否则null
     */
    public RLock tryLock(String lockKey) {
        RLock lock = redissonClient.getLock(lockKey);
        try {
            boolean acquired = lock.tryLock();
            if (acquired) {
                heldLocks.add(lock);
                log.debug("获取锁成功: {}", lockKey);
                return lock;
            } else {
                log.debug("获取锁失败: {}", lockKey);
                return null;
            }
        } catch (Exception e) {
            log.error("获取锁异常: {}", lockKey, e);
            return null;
        }
    }

    /**
     * 尝试获取锁，带超时时间，单位秒
     *
     * @param lockKey   锁的key
     * @param waitTime  等待时间，获取锁的最大等待时间
     * @param leaseTime 锁自动释放时间，防止死锁
     * @return 获取成功返回锁对象，否则null
     */
    public RLock tryLock(String lockKey, long waitTime, long leaseTime) {
        RLock lock = redissonClient.getLock(lockKey);
        try {
            boolean acquired = lock.tryLock(waitTime, leaseTime, TimeUnit.SECONDS);
            if (acquired) {
                heldLocks.add(lock);
                log.debug("获取锁成功: {}", lockKey);
                return lock;
            } else {
                log.debug("获取锁失败: {}", lockKey);
                return null;
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("获取锁被中断: {}", lockKey, e);
            return null;
        } catch (Exception e) {
            log.error("获取锁异常: {}", lockKey, e);
            return null;
        }
    }

    /**
     * 释放锁，安全释放，避免异常
     *
     * @param lock 锁对象
     */
    public void unlock(RLock lock) {
        if (lock == null) {
            return;
        }
        try {
            if (lock.isLocked()) {
                lock.unlock();
                heldLocks.remove(lock);
                log.debug("释放锁成功: {}", lock.getName());
            } else {
                log.warn("当前线程未持有锁，无法释放: {}", lock.getName());
            }
        } catch (IllegalMonitorStateException e) {
            log.warn("释放锁失败，当前线程未持有锁: {}", lock.getName());
        } catch (Exception e) {
            log.error("释放锁异常: {}", lock.getName(), e);
        }
    }

    /**
     * JVM关闭时释放所有持有的锁，单机优雅关闭时调用
     */
    @PostConstruct
    public void init() {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            log.info("JVM关闭钩子触发，开始释放所有持有的锁");
            for (RLock lock : heldLocks) {
                try {
                    if (lock.isLocked()) {
                        lock.unlock();
                        log.info("释放锁成功: {}", lock.getName());
                    }
                } catch (IllegalMonitorStateException e) {
                    log.warn("释放锁异常: {}", lock.getName(),e);
                } catch (Exception e) {
                    log.error("释放锁异常: {}", lock.getName(), e);
                }
            }
        }));
    }

    /**
     * 应用关闭时释放所有锁
     */
    @PreDestroy
    public void destroy() {
        log.info("应用关闭，释放所有持有的锁");
        for (RLock lock : heldLocks) {
            try {
                if (lock.isLocked()) {
                    lock.unlock();
                    log.info("释放锁成功: {}", lock.getName());
                }
            } catch (IllegalMonitorStateException e) {
                log.warn("释放锁失败 {}", lock.getName());
            } catch (Exception e) {
                log.error("释放锁异常: {}", lock.getName(), e);
            }
        }
    }
}