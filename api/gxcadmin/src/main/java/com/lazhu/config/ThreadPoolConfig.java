package com.lazhu.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池配置
 */
@Configuration
public class ThreadPoolConfig {


    // 获取CPU核心数
    private static final int CPU_CORES = Runtime.getRuntime().availableProcessors();


    /**
     * 视频剪辑线程池 CPU 密集
     */
    @Bean("videoMontageExecutor")
    public ThreadPoolTaskExecutor videoMontageExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数
        executor.setCorePoolSize(CPU_CORES+1);
        // 最大线程数
        executor.setMaxPoolSize(CPU_CORES*2);
        // 队列容量
        executor.setQueueCapacity(50);
        // 线程空闲时间
        executor.setKeepAliveSeconds(60);
        // 线程名称前缀
        executor.setThreadNamePrefix("videoMontageExecutor-");
        // 拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        // 设置线程池关闭时等待所有任务完成
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 设置线程池关闭时等待时间
        executor.setAwaitTerminationSeconds(60);
        executor.initialize();
        return executor;
    }

    /**
     * 对口型查询线程池 IO 密集
     */
    @Bean("videoTalkQueryExecutor")
    public ThreadPoolTaskExecutor videoTalkQueryExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数
        executor.setCorePoolSize(CPU_CORES);
        // 最大线程数
        executor.setMaxPoolSize(CPU_CORES*2);
        // 队列容量
        executor.setQueueCapacity(50);
        // 线程空闲时间
        executor.setKeepAliveSeconds(60);
        // 线程名称前缀
        executor.setThreadNamePrefix("videoTalkQueryExecutor-");
        // 拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 设置线程池关闭时等待所有任务完成
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 设置线程池关闭时等待时间
        executor.setAwaitTerminationSeconds(60);
        executor.initialize();
        return executor;
    }

    /**
     * 视频对口型任务提交线程池 IO 密集型
     */
    @Bean(name = "videoTalkSubmitThreadPool")
    public ThreadPoolTaskExecutor videoTalkSubmitThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数
        executor.setCorePoolSize(CPU_CORES);
        // 最大线程数
        executor.setMaxPoolSize(CPU_CORES*2);
        // 队列容量
        executor.setQueueCapacity(50);
        // 线程空闲时间
        executor.setKeepAliveSeconds(60);
        // 线程名称前缀
        executor.setThreadNamePrefix("videoTalkQueryExecutor-");
        // 拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 设置线程池关闭时等待所有任务完成
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 设置线程池关闭时等待时间
        executor.setAwaitTerminationSeconds(60);
        executor.initialize();
        return executor;
    }


}
