package com.lazhu.job;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.lazhu.business.content.entity.Content;
import com.lazhu.business.content.service.ContentService;
import com.lazhu.business.tasks.entity.Tasks;
import com.lazhu.business.tasks.entity.TasksQuery;
import com.lazhu.business.tasks.entity.VideoSynthTaskResult;
import com.lazhu.business.tasks.enums.TaskTypeEnum;
import com.lazhu.business.tasks.service.TasksService;
import com.lazhu.common.RedissonLockManager;
import com.lazhu.montage.service.VideoMontageService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 视频剪辑Job - 专门处理视频剪辑任务
 */
@Slf4j
@Component
public class VideoMontageJob {

    @Autowired
    private ContentService contentService;

    @Autowired
    private TasksService taskService;

    @Autowired
    private VideoMontageService videoMontageService;

    @Autowired
    private ThreadPoolTaskExecutor videoMontageExecutor;


    @Autowired
    private RedissonLockManager lockManager;

    /**
     * 处理视频剪辑任务 (30s 执行一次)
     */
    @Scheduled(fixedRate = 30 * 1000L)
    public void processVideoMontage() {
        log.info("[VideoMontage] ===> 开始处理视频剪辑任务");
        // 查询待处理的视频剪辑任务
        TasksQuery query = new TasksQuery();
        query.setType(TaskTypeEnum.VIDEO_MONTAGE.getCode()); // 视频剪辑任务类型
        query.setStatus(Tasks.STATUS_WAITING); // 待处理状态
        List<Tasks> tasksList = taskService.queryList(BeanUtil.beanToMap(query));
        if (tasksList == null || tasksList.isEmpty()) {
            log.info("[VideoMontage] ===> 无待处理的视频剪辑任务");
            return;
        }
        tasksList.forEach(task -> videoMontageExecutor.execute(() -> processMontageTask(task)));
    }

    /**
     * 处理单个视频剪辑任务
     */
    protected void processMontageTask(Tasks task) {
        String taskId = task.getTaskId();
        Long contentId = task.getContentId();
        log.info("[VideoMontage-{}] 开始处理剪辑任务，任务ID: {}，内容ID: {}", taskId, task.getId(), contentId);

        // 使用统一锁管理器加锁，带超时等待和自动释放
        String lockKey = "video:montage:task:" + contentId;
        RLock lock = lockManager.tryLock(lockKey);
        if (lock == null) {
            log.info("[VideoMontage-{}] 剪辑任务正在处理中，跳过: {}", contentId, contentId);
            return;
        }
        try {
            // 更新任务状态为处理中
            task.setStatus(Tasks.STATUS_PROCESSING);
            taskService.update(task);
            // 验证内容是否存在且有视频URL
            Content content = contentService.queryById(contentId);
            if (content == null) {
                log.error("[VideoMontage-{}] 内容不存在，内容ID: {}", taskId, contentId);
                task.setStatus(Tasks.STATUS_FAILED); // 失败状态
                taskService.update(task);
                return;
            }
            if (content.getMediaUrl() == null || content.getMediaUrl().trim().isEmpty()) {
                log.error("[VideoMontage-{}] 内容没有视频URL，内容ID: {}", taskId, contentId);
                task.setStatus(Tasks.STATUS_FAILED); // 失败状态
                taskService.update(task);
                return; // 等待下次调度
            }
            // 执行视频剪辑
            long montageStartTime = System.currentTimeMillis();
            try {
                videoMontageService.processVideo(content);
                log.info("[VideoMontage-{}] 视频剪辑完成，耗时: {}ms", taskId, System.currentTimeMillis() - montageStartTime);
                task.setStatus(Tasks.STATUS_SUCCESS);
                taskService.update(task);
            } catch (Exception e) {
                log.error("[VideoMontage-{}] 视频剪辑失败，耗时: {}ms", taskId, System.currentTimeMillis() - montageStartTime, e);
                // 更新任务状态为失败
                task.setStatus(Tasks.STATUS_FAILED);
                VideoSynthTaskResult videoSynthTaskResult = new VideoSynthTaskResult();
                videoSynthTaskResult.setData(null);
                videoSynthTaskResult.setMessage(e.getMessage());
                task.setResult(JSONObject.toJSONString(videoSynthTaskResult));
                taskService.update(task);
            }
        } finally {
            lockManager.unlock(lock);
        }
    }
}