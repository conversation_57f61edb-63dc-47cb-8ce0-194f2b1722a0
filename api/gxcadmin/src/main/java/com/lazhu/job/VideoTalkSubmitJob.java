package com.lazhu.job;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSONObject;
import com.lazhu.business.content.entity.Content;
import com.lazhu.business.content.service.ContentService;
import com.lazhu.business.tasks.entity.Tasks;
import com.lazhu.business.tasks.entity.TasksQuery;
import com.lazhu.business.tasks.entity.VideoSynthTaskResult;
import com.lazhu.business.tasks.enums.TaskTypeEnum;
import com.lazhu.business.tasks.service.TasksService;
import com.lazhu.common.RedissonLockManager;
import com.lazhu.common.enums.CurStepEnum;
import com.lazhu.montage.service.VideoTalkService;
import com.lazhu.system.sysparams.service.SysParamsService;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 对口型任务提交job
 */
@Slf4j
@Component
public class VideoTalkSubmitJob {


    public static final String SUBMIT_COUNT = "video_talk_submit_count";
    @Autowired
    private SysParamsService sysParamsService;
    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private TasksService tasksService;

    @Autowired
    private ThreadPoolTaskExecutor videoTalkSubmitThreadPool;

    @Autowired
    private VideoTalkService videoTalkService;

    @Autowired
    private ContentService contentService;

    @Autowired
    private RedissonLockManager lockManager;

    /**
     * 每隔1分钟执行一次
     */
    @Scheduled(fixedRate = 1, timeUnit = TimeUnit.MINUTES)
    public void execute() {
        log.info("[VideoTalkSubmit] 开始执行视频提交任务");
        //查询待提交的任务
        TasksQuery query = new TasksQuery();
        query.setStatus(Tasks.STATUS_WAITING);
        query.setType(TaskTypeEnum.VIDEO_TALK_SUBMIT.getCode());
        List<Tasks> tasks = tasksService.queryList(BeanUtil.beanToMap(query));
        tasks.forEach(task -> videoTalkSubmitThreadPool.execute(() -> submitTask(task)));
    }

    private void submitTask(Tasks task) {
        RLock videoTalkSubmitLock = lockManager.tryLock("video_talk_submit_lock");
        try {
            if (videoTalkSubmitLock == null) {
                return;
            }
            // 判断是否可以提交
            RAtomicLong atomicLong = redissonClient.getAtomicLong(SUBMIT_COUNT);
            long currentCount = atomicLong.get();
            //限流 RPS  每秒提交次数
            int rps = Convert.toInt(sysParamsService.queryByKey("video_talk_request_per_second"), 1);

            if (currentCount >= rps) {
                log.info("[VideoTalkSubmit] 提交任务达到上限，跳过提交，contentId:{},当前值: {}, 限制: {}", task.getContentId(), currentCount, rps);
                return;
            }
            atomicLong.incrementAndGet();
        } finally {
            lockManager.unlock(videoTalkSubmitLock);
        }
        // 加锁
        String lockKey = "video_talk_submit_lock:" + task.getContentId();
        RLock lock = lockManager.tryLock(lockKey);
        if (lock == null) {
            log.info("[VideoTalkSubmit-{}] 任务正在处理，跳过", task.getContentId());
            return;
        }
        try {
            log.info("[VideoTalkSubmit-{}] 开始处理", task.getContentId());
            task.setStatus(Tasks.STATUS_PROCESSING);
            tasksService.update(task);
            long start = System.currentTimeMillis();
            Content content = contentService.queryById(task.getContentId());
            List<String> taskIds = videoTalkService.submitVideoTalk(content);
            // 创建对口型查询任务
            List<Tasks> tasksList = new ArrayList<>();
            for (int i = 0; i < taskIds.size(); i++) {
                Tasks newTask = new Tasks();
                newTask.setContentId(task.getContentId());
                newTask.setType(TaskTypeEnum.VIDEO_TALK_QUERY.getCode());
                newTask.setPriority(i);
                newTask.setStatus(Tasks.STATUS_WAITING);
                newTask.setTaskId(taskIds.get(i));
                newTask.setCreateTime(new Date());
                newTask.setUpdateTime(new Date());
                tasksList.add(newTask);
            }
            tasksService.batchSave(tasksList);
            // 更新任务状态
            task.setStatus(Tasks.STATUS_SUCCESS);
            tasksService.update(task);
            log.info("[VideoTalkSubmit-{}] 任务提交完成,耗时: {}ms", task.getContentId(), System.currentTimeMillis() - start);
        } catch (Exception e) {
            log.error("[VideoTalkSubmit-{}] 任务提交失败", task.getContentId(), e);
            // 更新任务状态
            task.setStatus(Tasks.STATUS_FAILED);
            VideoSynthTaskResult video = new VideoSynthTaskResult();
            video.setData(null);
            video.setMessage(e.getMessage());
            task.setResult(JSONObject.toJSONString(video));
            tasksService.update(task);

            // 更新内容状态
            Content content = new Content();
            content.setId(task.getContentId());
            content.setCurStep(CurStepEnum.VIDEO_COMPLETE.getType());
            contentService.update(content);
            // 提交异常，释放限流
            releaseLimit();
        } finally {
            lockManager.unlock(lock);
        }
    }

    private void releaseLimit() {
        RAtomicLong submitCount = redissonClient.getAtomicLong(SUBMIT_COUNT);
        long l = submitCount.decrementAndGet();
        if (l < 0) {
            // 判断如果小于0，则设置为0
            submitCount.compareAndSet(l, 0);
        }
    }


    @PostConstruct
    private void init() {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            log.info("JVM关闭钩子触发，回滚正在执行的任务");
            // 查询正在执行的任务
            TasksQuery tasksQuery = new TasksQuery();
            tasksQuery.setStatus(Tasks.STATUS_PROCESSING);
            List<Tasks> tasks = tasksService.queryList(BeanUtil.beanToMap(tasksQuery));
            if (CollectionUtil.isEmpty(tasks)) {
                return;
            }
            tasks.forEach(task -> {
                log.info("回滚任务: {}", task.getId());
                task.setStatus(Tasks.STATUS_WAITING);
                tasksService.update(task);
            });
        }));
    }

}
