package com.lazhu.job;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lazhu.baseai.llm.dto.LLMBaseResponse;
import com.lazhu.baseai.llm.dto.VideoSynthesizeResp;
import com.lazhu.business.content.entity.Content;
import com.lazhu.business.content.service.ContentService;
import com.lazhu.business.llmconfig.service.LLMService;
import com.lazhu.business.tasks.entity.Tasks;
import com.lazhu.business.tasks.entity.TasksQuery;
import com.lazhu.business.tasks.entity.VideoSynthTaskResult;
import com.lazhu.business.tasks.enums.TaskTypeEnum;
import com.lazhu.business.tasks.service.TasksService;
import com.lazhu.common.RedissonLockManager;
import com.lazhu.common.media.MediaUtil;
import com.lazhu.common.utils.OssUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

import static com.lazhu.job.VideoTalkSubmitJob.SUBMIT_COUNT;

/**
 * 对口型查询Job - 处理对口型任务查询
 */
@Slf4j
@Component
public class VideoTalkQueryJob {

    @Autowired
    private ContentService contentService;

    @Autowired
    private LLMService lLMService;

    @Autowired
    private TasksService taskService;

    @Autowired
    private OssUtil ossUtil;


    @Autowired
    private ThreadPoolTaskExecutor videoTalkQueryExecutor;

    @Autowired
    private RedissonLockManager lockManager;

    @Autowired
    private RedissonClient redissonClient;

    /**
     * 对口型任务查询 (30s 执行一次)
     */
    @Scheduled(fixedRate = 30 * 1000L)
    public void queryVideoTalkStatus() {
        log.info("[VideoTalkQuery] ===> 开始查询对口型视频任务状态");
        // 查询未完成的视频合成任务
        TasksQuery query = new TasksQuery();
        query.setType(TaskTypeEnum.VIDEO_TALK_QUERY.getCode());
        query.setStatusList(Arrays.asList(Tasks.STATUS_PROCESSING, Tasks.STATUS_WAITING));
        List<Tasks> tasksList = taskService.queryList(BeanUtil.beanToMap(query));
        if (CollectionUtil.isEmpty(tasksList)) {
            // 没有任务，设置计数器为0
            RAtomicLong submitCount = redissonClient.getAtomicLong(SUBMIT_COUNT);
            submitCount.set(0);
            return;
        }
        // 根据contentId分组，同一个contentId的多个任务放到一个线程（串行）
        Map<Long, List<Tasks>> tasksMap = tasksList.stream().collect(Collectors.groupingBy(Tasks::getContentId));
        tasksMap.forEach((contentId, tasks) -> videoTalkQueryExecutor.execute(() -> tasks.forEach(this::processTask)));
        log.info("[VideoTalkQuery] ===> 对口型视频任务状态查询完成，任务数: {}", tasksList.size());
    }

    protected void processTask(Tasks task) {
        String taskId = task.getTaskId();
        Long contentId = task.getContentId();
        log.info("[VideoTalkQuery-{}] 开始处理任务，任务ID: {}，内容ID: {}", contentId, taskId, contentId);

        String taskLockKey = "video:synthesis:task:" + contentId;
        RLock taskLock = lockManager.tryLock(taskLockKey);
        if (taskLock == null) {
            log.info("[VideoTalkQuery-{}] 任务正在处理中，跳过: {}", contentId, taskId);
            return;
        }
        try {
            task.setStatus(Tasks.STATUS_PROCESSING);
            taskService.update(task);
            // 查询视频任务状态
            long processResultStartTime = System.currentTimeMillis();
            LLMBaseResponse<VideoSynthesizeResp> res = lLMService.queryVideoTask(taskId);
            VideoSynthesizeResp body = res.getBody();

            if (body.getStatus().equals("SUCCEEDED")) {
                task.setStatus(Tasks.STATUS_SUCCESS);
                VideoSynthTaskResult taskResult = new VideoSynthTaskResult();
                taskResult.setData(body);
                task.setResult(JSONObject.toJSONString(taskResult));
                // 合并视频
                videoTalkQueryExecutor.execute(() -> combineVideo(task));
                // 更新任务状态
                taskService.update(task);
                // 释放限流
                releaseLimit();
            } else if (body.getStatus().equals("FAILED") || body.getStatus().equals("UNKNOWN")) {
                task.setStatus(Tasks.STATUS_FAILED);
                VideoSynthTaskResult taskResult = new VideoSynthTaskResult();
                taskResult.setCode(body.getCode());
                taskResult.setMessage(body.getMsg());
                taskResult.setData(body);
                task.setResult(JSONObject.toJSONString(taskResult));
                taskService.update(task);
                // 释放限流
                releaseLimit();
            }
            log.info("[VideoTalkQuery-{}] 处理任务结果完成，耗时: {}ms", taskId, System.currentTimeMillis() - processResultStartTime);
        } finally {
            lockManager.unlock(taskLock);
        }
    }


    private void releaseLimit() {
        RAtomicLong submitCount = redissonClient.getAtomicLong(SUBMIT_COUNT);
        long l = submitCount.decrementAndGet();
        if (l < 0) {
            // 判断如果小于0，则设置为0
            submitCount.compareAndSet(l, 0);
        }
    }

    /**
     * 合并视频 - 当所有相关任务完成时，合并视频并触发剪辑任务
     */
    private void combineVideo(Tasks task) {
        String taskId = task.getTaskId();
        Long contentId = task.getContentId();
        log.info("[VideoTalkQuery-{}] 开始合并视频，内容ID: {}", taskId, contentId);

        //获取视频链接
        TasksQuery query = new TasksQuery();
        query.setContentId(contentId);
        query.setType(TaskTypeEnum.VIDEO_TALK_QUERY.getCode());
        query.setExcludeId(task.getId());
        List<Tasks> tasksList = taskService.queryList(BeanUtil.beanToMap(query));
        tasksList.add(task);

        //检查任务是否全部完成
        boolean isAllCompleted = tasksList.stream().allMatch(task1 -> task1.getStatus() == Tasks.STATUS_SUCCESS);
        if (!isAllCompleted) {
            log.info("[VideoTalkQuery-{}] 存在未完成的任务，等待其他任务完成后再合并", taskId);
            return;
        }
        log.info("[VideoTalkQuery-{}] 所有任务已完成，开始合并步骤", contentId);

        // 根据优先级排序
        tasksList.sort(Comparator.comparingInt(Tasks::getPriority));
        List<String> videoUrls = tasksList.stream().map(e -> {
            String result = e.getResult();
            return JSONObject.parseObject(result, VideoSynthTaskResult.class).getData().getVideoUrl();
        }).toList();

        // 视频合成
        String videoUrl = mergeAndUploadVideos(videoUrls, contentId.toString());

        // 更新内容
        Content content = contentService.queryById(contentId);
        content.setId(contentId);
        content.setMediaUrl(videoUrl);

        // 生成封面
        File coverImageFile = MediaUtil.getVideoCover(videoUrl);
        String coverImage = ossUtil.upload(coverImageFile);
        content.setCoverImg(JSONArray.toJSONString(Collections.singletonList(coverImage)));
        // 获取视频时长
        content.setDuration(MediaUtil.getDuration(videoUrl));
        // 创建视频剪辑任务
        createVideoMontageTask(contentId);
        // 清理封面图片临时文件
        FileUtil.del(coverImageFile);
        // 更新内容
        contentService.update(content);
    }

    /**
     * 创建视频剪辑任务
     */
    private void createVideoMontageTask(Long contentId) {
        log.info("[VideoTalkQuery] 为内容ID: {} 创建视频剪辑任务", contentId);

        Tasks montageTask = new Tasks();
        montageTask.setContentId(contentId);
        montageTask.setTaskId(String.valueOf(contentId));
        montageTask.setPriority(0);
        montageTask.setType(TaskTypeEnum.VIDEO_MONTAGE.getCode()); // 视频剪辑任务类型
        montageTask.setStatus(Tasks.STATUS_WAITING); // 待处理状态
        taskService.save(montageTask);

        log.info("[VideoTalkQuery] 视频剪辑任务创建成功，内容ID: {}，任务ID: {}", contentId, montageTask.getId());
    }

    /**
     * 合并并上传视频
     */
    private String mergeAndUploadVideos(List<String> videoUrls, String taskId) {
        long mergeStartTime = System.currentTimeMillis();
        log.info("[VideoTalkQuery-{}] 开始合并并上传视频，视频数量: {}", taskId, videoUrls.size());

        if (CollectionUtil.isEmpty(videoUrls)) {
            throw new RuntimeException("视频URL列表为空");
        }

        String url;
        if (CollectionUtil.size(videoUrls) > 1) {
            // 多个视频需要合并
            String tempPath = FileUtil.getTmpDirPath();
            if (tempPath.endsWith(File.separator)) {
                tempPath = tempPath + taskId;
            } else {
                tempPath = tempPath + File.separator + taskId;
            }

            log.info("[VideoTalkQuery-{}] 开始合并对口型视频：{}", taskId, videoUrls);

            // 增加转场特效合并
            long actualMergeStartTime = System.currentTimeMillis();
            MediaUtil.merge(videoUrls, tempPath + "/video.mp4");
            log.info("[VideoTalkQuery-{}] 视频合并完成，耗时: {}ms", taskId, System.currentTimeMillis() - actualMergeStartTime);

            url = ossUtil.upload(FileUtil.file(tempPath + "/video.mp4"), "video/" + taskId);
            FileUtil.del(tempPath); // 清理临时文件
        } else {
            // 单个视频直接上传
            String videoUrl = videoUrls.getFirst();
            url = ossUtil.upload(videoUrl, "video/" + taskId + "/video.mp4");
        }

        log.info("[VideoTalkQuery-{}] 合并并上传视频完成，总耗时: {}ms，最终URL: {}", taskId, System.currentTimeMillis() - mergeStartTime, url);
        return url;
    }
}