package com.lazhu.job;

import cn.hutool.core.collection.CollUtil;
import com.lazhu.business.userchannelaccount.entity.UserChannelAccount;
import com.lazhu.business.userchannelaccount.enums.ChannelType;
import com.lazhu.business.userchannelaccount.service.UserChannelAccountService;
import com.lazhu.gxc.publish.VideoPublisher;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 发布授权状态检查任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PublishAuthCheckJob {

    private final UserChannelAccountService userChannelAccountService;

    private final VideoPublisher videoPublisher;

    /**
     * 检查发布授权状态 每10分钟检查一次
     */
    @Scheduled(cron = "0 0/10 * * * ?")
    public void checkAuthStatus() {
        log.info("检查发布授权状态");
        // 查询所有在线的账号
        List<UserChannelAccount> accountList = userChannelAccountService.queryOnlineAccount();
        if (CollUtil.isEmpty(accountList)) {
            return;
        }
        for (UserChannelAccount account : accountList) {
            Integer channel = account.getChannel();
            ChannelType channelType = ChannelType.fromType(channel);
            boolean b = videoPublisher.validateCookies(channelType.toPlatformType(), account.getActorId().toString());
            if (b) {
                continue;
            }
            log.info("账号{}发布授权已失效", account.getActorId());
            // 更新状态
            account.setLoginStatus(0);
            userChannelAccountService.update(account);
            // 发送消息 TODO
        }
    }

}
