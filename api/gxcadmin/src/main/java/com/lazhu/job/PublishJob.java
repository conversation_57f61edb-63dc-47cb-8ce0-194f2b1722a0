package com.lazhu.job;

import cn.hutool.core.collection.CollUtil;
import com.lazhu.business.content.entity.Content;
import com.lazhu.business.content.service.ContentService;
import com.lazhu.business.publishlog.dto.PublishResponse;
import com.lazhu.business.publishlog.entity.PublishLog;
import com.lazhu.business.publishlog.service.PublishLogService;
import com.lazhu.business.publishlog.service.PublishService;
import com.lazhu.business.userchannelaccount.entity.UserChannelAccount;
import com.lazhu.business.userchannelaccount.enums.ChannelType;
import com.lazhu.business.userchannelaccount.service.UserChannelAccountService;
import com.lazhu.common.media.MediaResourceManager;
import com.lazhu.common.media.MediaUrlProcessor;
import com.lazhu.gxc.publish.VideoPublisher;
import com.lazhu.gxc.publish.model.UploadResult;
import com.lazhu.support.util.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.Date;
import java.util.List;

/**
 * 发布授权状态检查任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PublishJob {

    private final UserChannelAccountService userChannelAccountService;

    private final VideoPublisher videoPublisher;

    private final PublishLogService publishLogService;

    private final PublishService publishService;

    private final ContentService contentService;

    /**
     * 检查发布授权状态 每10分钟检查一次
     */
    @Scheduled(cron = "0 0/10 * * * ?")
    public void checkAuthStatus() {
        log.info("检查发布授权状态");
        // 查询所有在线的账号
        List<UserChannelAccount> accountList = userChannelAccountService.queryOnlineAccount();
        if (CollUtil.isEmpty(accountList)) {
            return;
        }
        for (UserChannelAccount account : accountList) {
            Integer channel = account.getChannel();
            ChannelType channelType = ChannelType.fromType(channel);
            boolean b = videoPublisher.validateCookies(channelType.toPlatformType(), account.getActorId().toString());
            if (b) {
                continue;
            }
            log.info("账号{}发布授权已失效", account.getActorId());
            // 更新状态
            account.setLoginStatus(0);
            userChannelAccountService.update(account);
            // 发送消息 TODO
        }
    }

    /**
     * 执行待发布任务
     */
    @Scheduled(cron = "0 0/1 * * * ?")
    public void publishJob() {
        log.info("执行待发布任务");
        // 查询待发布任务
        List<PublishLog> publishLogList = publishLogService.queryWaitPublish(new Date());
        if (CollUtil.isEmpty(publishLogList)) {
            return;
        }
        for (PublishLog publishLog : publishLogList) {
            // 发布
            long startTime = System.currentTimeMillis();
            log.info("开始执行发布任务{}", publishLog.getId());
            Content content = contentService.queryById(publishLog.getContentId());
            String url = content.getExposeMediaUrl();
            if (StringUtils.isBlank(url)) {
                log.error("视频路径为空");
                continue;
            }
            String videoPath = MediaUrlProcessor.downloadToLocal(url);
            try {
                PublishResponse publishResponse;
                UploadResult uploadResult = publishService.publish(publishLog, videoPath);
                if (uploadResult.isSuccess()) {
                    publishLog.setPublishId(uploadResult.getVideoId());
                    publishLog.setPublishStatus("2");
                    publishResponse = PublishResponse.success(publishLog.getChannel(), publishLog.getChannelAccount());

                    // 发布成功 更新内容为已发布
                    Content updateContent = new Content();
                    updateContent.setId(content.getId());
                    updateContent.setStatus(2);
                    contentService.update(updateContent);
                } else {
                    publishLog.setPublishStatus("3");
                    publishResponse = PublishResponse.fail(publishLog.getChannel(), publishLog.getChannelAccount(), uploadResult.getMessage());
                }
                log.info("发布任务{} 执行结果{} 耗时{}", publishLog.getId(), publishResponse, System.currentTimeMillis() - startTime);
            } catch (Exception e) {
                log.error("发布任务{} 执行失败", publishLog.getId(), e);
            } finally {
                // 清理临时文件
                if (videoPath != null) {
                    MediaResourceManager.cleanupTempFile(new File(videoPath));
                }
            }
        }
    }


}
