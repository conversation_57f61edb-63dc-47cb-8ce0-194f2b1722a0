package com.lazhu.business.videoeffectstpl.web;

import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSONArray;
import com.lazhu.business.videoeffectstpl.dto.ProcessConfigDTO;
import com.lazhu.business.videoeffectstpl.entity.VideoEffectsTpl;
import com.lazhu.business.videoeffectstpl.entity.VideoEffectsTplQuery;
import com.lazhu.common.media.MediaUtil;
import com.lazhu.common.media.VideoInfo;
import com.lazhu.common.media.VideoInfoService;
import com.lazhu.common.utils.OssUtil;
import com.lazhu.support.base.BaseController;
import com.lazhu.support.response.PageResponse;
import com.lazhu.support.response.SimpleResponse;
import com.lazhu.support.response.SimpleResponseBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;


import java.io.File;

/**
 * 剪辑模板
 * <AUTHOR>
 * @since 2025-08-07
 */
@RestController
@RequestMapping(value = "/bus/videoEffectsTpl")
@Slf4j
public class VideoEffectsTplController extends BaseController<VideoEffectsTpl> {

	@Autowired
	private OssUtil ossUtil;
	
	@PostMapping(value = "/read/list")
	public PageResponse<VideoEffectsTpl> query(@RequestBody VideoEffectsTplQuery param) {
		return super.query(param);
	}

	@GetMapping(value = "/read/detail")
	public SimpleResponse<VideoEffectsTpl> get(Long id) {
		Assert.notNull(id, "ID");
		VideoEffectsTpl videoEffectsTpl = this.service.queryById(id);
		if(videoEffectsTpl!=null && StringUtils.isNotBlank(videoEffectsTpl.getProcessConfig())){
			videoEffectsTpl.setProcessConfigList(JSONArray.parseArray(videoEffectsTpl.getProcessConfig(), ProcessConfigDTO.class));
		}
		return new SimpleResponseBuilder<>(videoEffectsTpl).success().bulider();
	}

	@PostMapping("/add")
	public SimpleResponse<VideoEffectsTpl> add(@RequestBody VideoEffectsTpl param) throws InterruptedException{
	    if (param.getId() != null) {
			throw new InterruptedException("add element not need  id !");
		}
		buildValue(param);
		return super.save(param);
	}

	@PostMapping("/edit")
	public SimpleResponse<VideoEffectsTpl> edit(@RequestBody VideoEffectsTpl param) throws InterruptedException{
		if (param.getId() == null) {
			throw new InterruptedException("update element need  id !");
		}
		buildValue(param);
		return super.update(param);
	}

	@GetMapping("/del")
	public SimpleResponse<VideoEffectsTpl> del(Long id) {
		Assert.notNull(id, "ID");
		return super.delete(id);
	}

	private void  buildValue(VideoEffectsTpl param){
		if(StringUtils.isNotBlank(param.getVideoUrl())){
			String fileSizeInMB= String.format("%.2f", com.lazhu.common.utils.FileUtil.getFileSizeInMB(param.getVideoUrl()));
			log.info("压缩后视频大小：{}", fileSizeInMB);
			// 获取视频尺寸信息
			VideoInfo videoInfo = VideoInfoService.getVideoInfo(param.getVideoUrl());
			param.setSize(videoInfo.getWidth() + "*" + videoInfo.getHeight()+"|"+videoInfo.getAspectRatio());

			File coverImgFile = MediaUtil.getVideoCover(param.getVideoUrl());
			String coverImgUrl = ossUtil.upload(coverImgFile);
			param.setCoverImg(coverImgUrl);
			FileUtil.del(coverImgFile);
		}
	}
}
