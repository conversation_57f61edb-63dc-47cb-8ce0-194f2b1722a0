package com.lazhu.business.actor.web;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.lazhu.baseai.tool.dify.PromptUtil;
import com.lazhu.business.actor.entity.ActorDTO;
import com.lazhu.business.actorcontentexample.entity.ActorContentExample;
import com.lazhu.business.actorcontentexample.service.ActorContentExampleService;
import com.lazhu.support.response.SimpleResponseBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import org.springframework.util.Assert;

import com.lazhu.support.base.BaseController;
import com.lazhu.support.response.PageResponse;
import com.lazhu.support.response.SimpleResponse;
import com.lazhu.business.actor.entity.Actor;
import com.lazhu.business.actor.entity.ActorQuery;

import java.util.List;

/**
 * <p>
 * 演员表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@RestController
@RequestMapping(value = "bus/actor")
public class ActorController extends BaseController<Actor> {


    @Autowired
    private PromptUtil promptUtil;

    @Autowired
    private ActorContentExampleService actorContentExampleService;


    @PostMapping(value = "/read/list")
    public PageResponse<Actor> query(@RequestBody ActorQuery param) {
        param.setUseStatus(1);
        return super.query(param);
    }

    @GetMapping(value = "/read/detail")
    public SimpleResponse<ActorDTO> detail(Long id) {
        Assert.notNull(id, "ID");
        Actor actor = this.service.queryById(id);
        List<ActorContentExample> actorContentExamples = actorContentExampleService.queryByActorId(id);
        ActorDTO actorDTO = ActorDTO.buildFromActor(actor);
        actorDTO.setExampleContent(actorContentExamples);
        return new SimpleResponseBuilder<ActorDTO>(actorDTO).success().bulider();
    }

    @PostMapping("/add")
    public SimpleResponse<Actor> add(@RequestBody ActorDTO param) throws InterruptedException {
        if (param.getId() != null) {
            throw new InterruptedException("add element not need  id !");
        }
        Actor actor = BeanUtil.toBean(param, Actor.class);
        service.save(actor);
        List<ActorContentExample> exampleContent = param.getExampleContent();
        if (CollectionUtil.isNotEmpty(exampleContent)) {
            for (ActorContentExample example : exampleContent) {
                example.setActorId(actor.getId());
                actorContentExampleService.saveOrUpdate(example);
            }
        }
        return new SimpleResponseBuilder<Actor>(actor).success().bulider();
    }

    @PostMapping("/edit")
    public SimpleResponse<Actor> edit(@RequestBody ActorDTO param) throws InterruptedException {
        if (param.getId() == null) {
            throw new InterruptedException("update element need  id !");
        }
        Actor actor = BeanUtil.toBean(param, Actor.class);
        service.update(actor);
        List<ActorContentExample> exampleContent = param.getExampleContent();
        if (CollectionUtil.isNotEmpty(exampleContent)) {
            for (ActorContentExample example : exampleContent) {
                example.setActorId(actor.getId());
                actorContentExampleService.saveOrUpdate(example);
            }
        }
        return new SimpleResponseBuilder<Actor>(actor).success().bulider();
    }

    @GetMapping("/del")
    public SimpleResponse<Actor> del(Long id) {
        Assert.notNull(id, "ID");
        Actor actor = this.service.queryById(id);
        actor.setUseStatus(0);
        return super.update(actor);
    }

    /**
     * 生成提示词
     */
    @PostMapping("/createPrompt")
    public SimpleResponse<String> createPrompt(@RequestBody JSONObject param) {
        String profile = param.getString("profile");
        String prompt = promptUtil.createPrompt(profile);
        return new SimpleResponseBuilder<>(prompt).success().bulider();
    }

}
