server:
  tomcat:
    uri-encoding: UTF-8
  port: 8081


spring:
  application:
    name: gxcAdmin
  servlet:
    multipart:
      # 禁用spring的文件上传,使用系统自带
      enabled: false
  redis:
    redisson:
      config: classpath:config/redisson-dev.yml



sys:
  admin-domain: http://**************:8080
  accessUrls:
  url: ${sys.admin-domain}

lz:
  upload:
    webSite: ${sys.admin-domain}/upload
    path: /data/upload
    dempFile: /data/demp
    base: /data/
    type: oss
    ossBucket: lazhuyun
    ossWebSite: https://ins-file.lazhuyun.cn
    ossBaseKey: test


datasource:
  driverClassName: com.mysql.cj.jdbc.Driver
  url: **********************************************************************************************************************************************************************************************
  username: galaxy_creator
  password: wts111!!
  #--------------------------
  # 下面为连接池的补充设置，应用到上面所有数据源中
  # 初始化大小，最小，最大
  initialSize: 1
  minIdle: 5
  maxActive: 200
  # 配置获取连接等待超时的时间
  maxWait: 60000
  # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
  timeBetweenEvictionRunsMillis: 60000
  # 配置一个连接在池中最小生存的时间，单位是毫秒
  minEvictableIdleTimeMillis: 300000
  validationQuery: SELECT 1 FROM DUAL
  testWhileIdle: true
  testOnBorrow: false
  testOnReturn: false
  #打开PSCache，并且指定每个连接上PSCache的大小
  poolPreparedStatements: false
  #spring.datasource.maxPoolPreparedStatementPerConnectionSize=20
  # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙 log4j用于日志
  filters: stat
  # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
  connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000

mybatis-plus:
  # 如果是放在src/main/java目录下 classpath:com/yourpackage/*/mapper/*Mapper.xml
  # 如果是放在resource目录 classpath:/mapper/*Mapper.xml
  mapper-locations: classpath*:com/lazhu/**/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.lazhu.**.entity
  global-config:
    #主键类型  0:"数据库ID自增", 1:"用户输入ID",2:"全局唯一ID (数字类型唯一ID)", 3:"全局唯一ID UUID";
    #id-type: 0
    #字段策略 0:"忽略判断",1:"非 NULL 判断"),2:"非空判断"
    field-strategy: 1
    #驼峰下划线转换
    db-column-underline: true
    #刷新mapper 调试神器
    refresh-mapper: true
    #数据库大写下划线转换
    capital-mode: true
    # Sequence序列接口实现类配置
    #key-generator: com.baomidou.mybatisplus.incrementer.OracleKeyGenerator
    #逻辑删除配置（下面3个配置）
    #logic-delete-value: 1
    #logic-not-delete-value: 0
    #sql-injector: com.baomidou.mybatisplus.mapper.LogicSqlInjector
    #自定义填充策略接口实现
    #meta-object-handler: com.baomidou.springboot.MyMetaObjectHandler
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
    cache-enabled: false
    #配置JdbcTypeForNull
    jdbc-type-for-null: 'null'

# 统一门户登录
sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: ${spring.application.name}
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: 604800
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: 86400
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: false
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: false
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: simple-uuid
  # 是否输出操作日志
  is-log: false
  sso-client:
    client: localTest
    # SSO-Server端 统一认证地址
    auth-url: http://uos.wts9999.net/sso/auth
    # 使用 Http 请求校验ticket (模式三)
    is-http: true
    # SSO-Server端 ticket校验地址
    check-ticket-url: http://10.192.1.194:32746/sso/checkTicket
    # 单点注销地址
    slo-url: http://10.192.1.194:32746/sso/signout
    # 查询数据地址
    get-data-url: http://10.192.1.194:32746/sso/getData
  sign:
    # API 接口调用秘钥
    secret-key: wSYNu6m9zA6Pr6wTKIdinKfDvTSEXHrU

ai:
  # 请求接口 （视频口播文案生成+修改+图文修改）
  url: http://**************/v1/chat-messages
  #workflow_url
  workflow_url: http://**************/v1/workflows/run
  # 提示词优化token
  prompt_create_auth: Bearer app-fLx39RpgJH1ww5trve1MTx8T
  #图文文本生成token
  text_create_token: Bearer app-8NkqQVUuFXkKQsOwLOIZ1Ozn
  #图文修改token
  text_modify_token: Bearer app-I6cjLlBoglHhSTAD0iuejfg8
  #视频口播文案生成token + 视频口播文案修改
  video_text_create_token: Bearer app-9qh6yejZNw1Ht0IPZvxKCkAq
  # tts  优化token
  tts_optimization_auth: Bearer app-zpaCirB7XVa9qmVdEY0nt814
  # 选题创作token
  topic_creation_auth: Bearer app-DGDyM6AgXvpO2MTyxwWgkoMg
  # 热点搜索token
  topic_search_auth: Bearer app-G3X7FPzYuZcZrjPkCl60t9KA
  # 链接读取url
  link_read_url: http://**************:8082/articles/wechat
  # ASS字幕生成token
  ass_create_token: Bearer app-QU8QaVFImSGf68Luk9xQbdjQ
  # 剪辑流程token
  video_process_create_token: Bearer app-Omc4cZp3MdjVQE4QT7yRvJkZ

  #图片与文件识别url
  identify_url: https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions
  #图片与文件识别token
  identify_token: sk-e35cd0bdc0334ec3afb05089178fff5b
  # 知识库上传接口
  knowledge_base_upload: http://**************/v1/datasets/f0b92bff-4d61-4284-bd86-61a96e318a02/documents/%s/segments
  # 知识库更新以及删除接口
  knowledge_base_update: http://**************/v1/datasets/f0b92bff-4d61-4284-bd86-61a96e318a02/documents/%s/segments/%s
  # 知识库token
  knowledge_base_token: Bearer dataset-kA5GCIo9hw6pdyThE60DpcVD

# 发布配置
publish:
  enabled: true
  chrome-path: C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe
  headless: false
  cookie-path: d://gxc_publish_cookies
