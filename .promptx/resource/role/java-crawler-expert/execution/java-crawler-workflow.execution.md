<execution>
  <constraint>
    ## 技术约束条件
    - **Java版本要求**：必须兼容项目现有的Java版本
    - **Playwright版本**：使用稳定版本，避免beta版本的不稳定性
    - **内存限制**：考虑服务器内存限制，避免内存溢出
    - **网络环境**：考虑网络延迟和不稳定性
    - **目标网站限制**：遵循微信视频号的访问频率限制
  </constraint>
  
  <rule>
    ## 强制执行规则
    - **错误处理强制**：所有网络请求必须包含异常处理
    - **日志记录强制**：关键操作必须记录详细日志
    - **数据验证强制**：采集的数据必须经过格式验证
    - **配置外置强制**：所有可变参数必须提取为配置项
    - **测试覆盖强制**：核心功能必须有对应的测试用例
  </rule>
  
  <guideline>
    ## 开发指导原则
    - **渐进式开发**：先实现核心功能，再逐步完善
    - **模块化设计**：功能模块独立，便于维护和扩展
    - **防御式编程**：充分考虑各种异常情况
    - **性能优化**：在保证功能正确的前提下优化性能
    - **代码复用**：充分利用现有代码和第三方库
  </guideline>
  
  <process>
    ## 爬虫开发标准流程
    
    ### Phase 1: 需求分析与技术调研 (20分钟)
    ```mermaid
    flowchart TD
        A[需求分析] --> B[目标页面分析]
        B --> C[数据结构分析]
        C --> D[技术方案选择]
        D --> E[可行性评估]
    ```
    
    **具体步骤**：
    1. **需求确认**：明确要采集的数据类型和格式
    2. **页面分析**：分析目标页面的DOM结构和数据加载方式
    3. **接口分析**：识别Ajax请求和数据接口
    4. **反爬分析**：了解可能的反爬机制
    
    ### Phase 2: 架构设计与模块规划 (30分钟)
    ```mermaid
    graph LR
        A[爬虫引擎] --> B[页面解析器]
        A --> C[数据提取器]
        A --> D[数据存储器]
        A --> E[配置管理器]
        A --> F[日志管理器]
    ```
    
    **设计要点**：
    - **爬虫引擎**：负责浏览器控制和页面导航
    - **页面解析器**：负责DOM解析和元素定位
    - **数据提取器**：负责数据提取和格式化
    - **数据存储器**：负责数据持久化
    - **配置管理器**：负责配置参数管理
    - **日志管理器**：负责日志记录和错误追踪
    
    ### Phase 3: 核心功能实现 (60分钟)
    ```mermaid
    flowchart TD
        A[初始化Playwright] --> B[配置浏览器]
        B --> C[登录处理]
        C --> D[页面导航]
        D --> E[等待数据加载]
        E --> F[提取表格数据]
        F --> G[数据验证]
        G --> H[数据存储]
        H --> I[清理资源]
    ```
    
    **实现重点**：
    1. **浏览器配置**：设置合适的浏览器参数
    2. **登录机制**：处理微信登录流程
    3. **数据等待**：确保数据完全加载
    4. **表格解析**：准确提取表格中的统计数据
    5. **数据清洗**：处理数据格式和异常值
    
    ### Phase 4: 测试与优化 (30分钟)
    ```mermaid
    graph TD
        A[单元测试] --> B[集成测试]
        B --> C[性能测试]
        C --> D[稳定性测试]
        D --> E[错误处理测试]
    ```
    
    **测试策略**：
    - **功能测试**：验证数据采集的准确性
    - **异常测试**：测试各种异常情况的处理
    - **性能测试**：评估采集效率和资源消耗
    - **稳定性测试**：长时间运行的稳定性验证
  </process>
  
  <criteria>
    ## 质量评估标准
    
    ### 功能完整性
    - ✅ 能够成功访问目标页面
    - ✅ 能够准确提取表格数据
    - ✅ 数据格式符合预期
    - ✅ 异常情况处理完善
    
    ### 性能指标
    - ✅ 单次采集时间 < 30秒
    - ✅ 内存使用 < 500MB
    - ✅ CPU使用率 < 50%
    - ✅ 成功率 > 95%
    
    ### 代码质量
    - ✅ 代码结构清晰
    - ✅ 注释完善
    - ✅ 异常处理完整
    - ✅ 日志记录详细
    
    ### 可维护性
    - ✅ 模块化设计
    - ✅ 配置外置
    - ✅ 易于扩展
    - ✅ 测试覆盖充分
  </criteria>
</execution>
